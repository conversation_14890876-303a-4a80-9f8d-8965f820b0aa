/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.config;

import java.util.Objects;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "", ignoreInvalidFields = true)
@PropertySource("classpath:conf/sungoin.properties")
public class SungoinSetting {
    private String sungoinApiEndpoint;
    
    private Integer apiCallTimeOut;
    
    private Boolean callerUseOriginalNo;

    public String getSungoinApiEndpoint() {
        return sungoinApiEndpoint;
    }

    public void setSungoinApiEndpoint(String sungoinApiEndpoint) {
        this.sungoinApiEndpoint = sungoinApiEndpoint;
    }

	public Integer getApiCallTimeOut() {
		return Objects.isNull(apiCallTimeOut) ? 25 : apiCallTimeOut;
	}

	public void setApiCallTimeOut(Integer apiCallTimeOut) {
		this.apiCallTimeOut = apiCallTimeOut;
	}

	public Boolean getCallerUseOriginalNo() {
		return Objects.isNull(callerUseOriginalNo) ? false : callerUseOriginalNo;
	}

	public void setCallerUseOriginalNo(Boolean callerUseOriginalNo) {
		this.callerUseOriginalNo = callerUseOriginalNo;
	}
    
}
