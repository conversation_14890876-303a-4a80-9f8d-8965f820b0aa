/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "", ignoreInvalidFields = true)
@PropertySource("classpath:conf/great.properties")
public class GreatSetting {
    
    private String greatApiEndpoint;

    public String getGreatApiEndpoint() {
        return greatApiEndpoint;
    }

    public void setGreatApiEndpoint(String greatApiEndpoint) {
        this.greatApiEndpoint = greatApiEndpoint;
    }
    
}
