/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * <AUTHOR> 2015-7-22
 */
@Configuration
@ConfigurationProperties(prefix = "", ignoreInvalidFields = true)
@PropertySource("classpath:conf/baidu.properties")
public class BaiduSettings {

	private String queryIvrUrl;
	
	private String queryBindPhoneUrl;
	
	private String akId;
	
	private String akSecret;
	
	private String appKey;
	
	private String ttsUrl;

	public String getQueryIvrUrl() {
		return queryIvrUrl;
	}

	public void setQueryIvrUrl(String queryIvrUrl) {
		this.queryIvrUrl = queryIvrUrl;
	}

	public String getQueryBindPhoneUrl() {
		return queryBindPhoneUrl;
	}

	public void setQueryBindPhoneUrl(String queryBindPhoneUrl) {
		this.queryBindPhoneUrl = queryBindPhoneUrl;
	}

	public String getAkId() {
		return akId;
	}

	public void setAkId(String akId) {
		this.akId = akId;
	}

	public String getAkSecret() {
		return akSecret;
	}

	public void setAkSecret(String akSecret) {
		this.akSecret = akSecret;
	}

	public String getAppKey() {
		return appKey;
	}

	public void setAppKey(String appKey) {
		this.appKey = appKey;
	}

	public String getTtsUrl() {
		return ttsUrl;
	}

	public void setTtsUrl(String ttsUrl) {
		this.ttsUrl = ttsUrl;
	}
	
}
