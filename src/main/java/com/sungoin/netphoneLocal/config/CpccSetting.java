/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "", ignoreInvalidFields = true)
@PropertySource("classpath:conf/cpcc.properties")
public class CpccSetting {
    public static final String PATH_GET_IVR = "/getIvrKey";
    public static final String PATH_ADD = "/customer/add";
    
    private String cpccApiEndpoint;

    public String getCpccApiEndpoint() {
        return cpccApiEndpoint;
    }

    public void setCpccApiEndpoint(String cpccApiEndpoint) {
        this.cpccApiEndpoint = cpccApiEndpoint;
    }
    
}
