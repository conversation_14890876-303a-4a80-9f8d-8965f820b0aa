/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * <AUTHOR> 2015-7-22
 */
@Configuration
@ConfigurationProperties(prefix = "", ignoreInvalidFields = true)
@PropertySource("classpath:conf/common.properties")
public class CommonSettings {

    private final String[] emptyStringArray = new String[] {};
    private int platformId;
    private boolean mainServer;
    private String backupServerEndpoint;
    private String localNo;
    private String localCustomerNo;
    private String switchboardType;
    private String prefix;
    private boolean origCalleePrefix;
    private boolean bootConnectCti;
    private boolean originalNoPrefix;
    private String callerCheckIgnore;
    private String accessShortNum;
    private String releaseCheckUrl;
    private String specialOrig;
    private boolean allowCalleeMobile = true;
    private boolean origUseNetphone = false;
    private String initBindPhone = "10086";
    private String cloudStorageUser;
    private String cloudStoragePassword;
    private String syncFileClose;
	
	private String foreignPrefixNum;
	
	private String ignorCheckCaller;
	
	private boolean mobilePlusLocalArea = false;
	private boolean keepCallerLocalDistrict = false;
    private boolean addCallerNationCode = false;
    private boolean secondSaleOffhook = false;

    private String autoTestCaller;
    private String platformClientEndpoint;
//    private String zeroCaller;
    private String zeroDelayTime;
    
    private boolean specialPrefix = false;
    
    private String securityCheckUrl;
    
	public boolean isMainServer() {
        return this.mainServer;
    }

    public void setMainServer(boolean mainServer) {
        this.mainServer = mainServer;
    }

    public String getLocalNo() {
        return this.localNo;
    }

    public void setLocalNo(String localNo) {
        this.localNo = localNo;
    }

    public int getPlatformId() {
        return this.platformId;
    }

    public void setPlatformId(int platformId) {
        this.platformId = platformId;
    }

    public String[] getBackupServers() {
        return this.backupServerEndpoint == null ? this.emptyStringArray : this.backupServerEndpoint.split(",");
    }

    public String getBackupServerEndpoint() {
        return this.backupServerEndpoint;
    }

    public void setBackupServerEndpoint(String backupServerEndpoint) {
        this.backupServerEndpoint = backupServerEndpoint;
    }

    public String getLocalCustomerNo() {
        return this.localCustomerNo;
    }

    public void setLocalCustomerNo(String localCustomerNo) {
        this.localCustomerNo = localCustomerNo;
    }

    public String getSwitchboardType() {
        return this.switchboardType;
    }

    public void setSwitchboardType(String switchboardType) {
        this.switchboardType = switchboardType;
    }

    public String getPrefix() {
        return this.prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public boolean isOrigCalleePrefix() {
        return this.origCalleePrefix;
    }

    public void setOrigCalleePrefix(boolean origCalleePrefix) {
        this.origCalleePrefix = origCalleePrefix;
    }

    public boolean isBootConnectCti() {
        return this.bootConnectCti;
    }

    public void setBootConnectCti(boolean bootConnectCti) {
        this.bootConnectCti = bootConnectCti;
    }

    public boolean isOriginalNoPrefix() {
        return this.originalNoPrefix;
    }

    public void setOriginalNoPrefix(boolean originalNoPrefix) {
        this.originalNoPrefix = originalNoPrefix;
    }

    public String getCallerCheckIgnore() {
        return this.callerCheckIgnore;
    }

    public void setCallerCheckIgnore(String callerCheckIgnore) {
        this.callerCheckIgnore = callerCheckIgnore;
    }

    public String getAccessShortNum() {
        return this.accessShortNum;
    }

    public void setAccessShortNum(String accessShortNum) {
        this.accessShortNum = accessShortNum;
    }

    public String[] getAccessShortNumArray() {
        return this.accessShortNum == null ? this.emptyStringArray : this.accessShortNum.split(",");
    }

    public String getReleaseCheckUrl() {
        return this.releaseCheckUrl;
    }

    public void setReleaseCheckUrl(String releaseCheckUrl) {
        this.releaseCheckUrl = releaseCheckUrl;
    }

    public String getSpecialOrig() {
        return this.specialOrig;
    }

    public void setSpecialOrig(String specialOrig) {
        this.specialOrig = specialOrig;
    }

    public boolean isAllowCalleeMobile() {
        return this.allowCalleeMobile;
    }

    public void setAllowCalleeMobile(boolean allowCalleeMobile) {
        this.allowCalleeMobile = allowCalleeMobile;
    }

    public boolean isOrigUseNetphone() {
        return this.origUseNetphone;
    }

    public void setOrigUseNetphone(boolean origUseNetphone) {
        this.origUseNetphone = origUseNetphone;
    }

    public String getInitBindPhone() {
        return this.initBindPhone;
    }

    public void setInitBindPhone(String initBindPhone) {
        this.initBindPhone = initBindPhone;
    }

    public String getCloudStorageUser() {
        return this.cloudStorageUser;
    }

    public void setCloudStorageUser(String cloudStorageUser) {
        this.cloudStorageUser = cloudStorageUser;
    }

    public String getCloudStoragePassword() {
        return this.cloudStoragePassword;
    }

    public void setCloudStoragePassword(String cloudStoragePassword) {
        this.cloudStoragePassword = cloudStoragePassword;
    }

    public String getSyncFileClose() {
        return this.syncFileClose == null ? "0" : this.syncFileClose;
    }

    public void setSyncFileClose(String syncFileClose) {
        this.syncFileClose = syncFileClose;
    }

	public String getForeignPrefixNum() {
		return foreignPrefixNum;
	}

	public void setForeignPrefixNum(String foreignPrefixNum) {
		this.foreignPrefixNum = foreignPrefixNum;
	}

	public String getIgnorCheckCaller() {
		return ignorCheckCaller;
	}

	public void setIgnorCheckCaller(String ignorCheckCaller) {
		this.ignorCheckCaller = ignorCheckCaller;
	}
	
	public String[] getIgnorCheckCallerArray() {
        return this.ignorCheckCaller == null ? this.emptyStringArray : this.ignorCheckCaller.split(",");
    }

	public boolean isMobilePlusLocalArea() {
		return mobilePlusLocalArea;
	}

	public void setMobilePlusLocalArea(boolean mobilePlusLocalArea) {
		this.mobilePlusLocalArea = mobilePlusLocalArea;
	}

	public boolean isKeepCallerLocalDistrict() {
		return keepCallerLocalDistrict;
	}

	public void setKeepCallerLocalDistrict(boolean keepCallerLocalDistrict) {
		this.keepCallerLocalDistrict = keepCallerLocalDistrict;
	}

    public boolean isAddCallerNationCode() {
        return addCallerNationCode;
    }

    public void setAddCallerNationCode(boolean addCallerNationCode) {
        this.addCallerNationCode = addCallerNationCode;
    }
	
    private boolean inTest = false;

    public boolean isInTest() {
        return inTest;
    }

    public void setInTest(boolean inTest) {
        this.inTest = inTest;
    }
    
    private int sipConnectDelay=0;

    public int getSipConnectDelay() {
        return sipConnectDelay;
    }

    public void setSipConnectDelay(int sipConnectDelay) {
        this.sipConnectDelay = sipConnectDelay;
    }

    public boolean isSecondSaleOffhook() {
        return secondSaleOffhook;
    }

    public void setSecondSaleOffhook(boolean secondSaleOffhook) {
        this.secondSaleOffhook = secondSaleOffhook;
    }

    public String getAutoTestCaller() {
        return autoTestCaller;
    }

    public void setAutoTestCaller(String autoTestCaller) {
        this.autoTestCaller = autoTestCaller;
    }

    public String getPlatformClientEndpoint() {
        return platformClientEndpoint;
    }

    public void setPlatformClientEndpoint(String platformClientEndpoint) {
        this.platformClientEndpoint = platformClientEndpoint;
    }

//    public String getZeroCaller() {
//        return zeroCaller;
//    }
//
//    public void setZeroCaller(String zeroCaller) {
//        this.zeroCaller = zeroCaller;
//    }

    public String getZeroDelayTime() {
        return zeroDelayTime;
    }

    public void setZeroDelayTime(String zeroDelayTime) {
        this.zeroDelayTime = zeroDelayTime;
    }

	public boolean isSpecialPrefix() {
		return specialPrefix;
	}

	public void setSpecialPrefix(boolean specialPrefix) {
		this.specialPrefix = specialPrefix;
	}

    public String getSecurityCheckUrl() {
        return securityCheckUrl;
    }

    public void setSecurityCheckUrl(String securityCheckUrl) {
        this.securityCheckUrl = securityCheckUrl;
    }
    
}
