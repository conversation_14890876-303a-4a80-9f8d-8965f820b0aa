/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "", ignoreInvalidFields = true)
@PropertySource("classpath:conf/elevator.properties")
public class ElevatorSetting {

    public static final String API_USER_INFO = "/elevator/code_info";
    public static final String API_CREATE_CODE = "/sos_wait/code_create";
    
    private String elevatorApiEndpoint;

    public String getElevatorApiEndpoint() {
        return elevatorApiEndpoint;
    }

    public void setElevatorApiEndpoint(String elevatorApiEndpoint) {
        this.elevatorApiEndpoint = elevatorApiEndpoint;
    }

}
