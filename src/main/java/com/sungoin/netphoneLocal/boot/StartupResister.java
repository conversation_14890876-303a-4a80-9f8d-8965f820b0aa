/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.boot;

import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-8-11
 */
public class StartupResister {
	private static final Logger LOGGER = LoggerFactory.getLogger(StartupResister.class);
	private static final List<StartupAble> START_LIST = new ArrayList<StartupAble>();
	
	public static synchronized void register(StartupAble starter) {
		START_LIST.add(starter);
	}
	
	public static void doStart() {
		START_LIST.forEach(s -> {
			LOGGER.debug("开始启动类{}的start方法！",s.getClass().getName());
			s.start();
		});
	}
}
