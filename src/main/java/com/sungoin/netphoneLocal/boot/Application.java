/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.boot;

import com.sungoin.netphoneLocal.business.rmi.DynamicRmiRegister;
import javax.servlet.MultipartConfigElement;

import org.apache.catalina.connector.Connector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ExitCodeGenerator;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.context.embedded.EmbeddedServletContainerFactory;
import org.springframework.boot.context.embedded.tomcat.TomcatConnectorCustomizer;
import org.springframework.boot.context.embedded.tomcat.TomcatEmbeddedServletContainerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import com.sungoin.netphoneLocal.business.service.BaseService;
import com.sungoin.netphoneLocal.util.SpringHelper;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.web.servlet.MultipartConfigFactory;

/**
 * <AUTHOR> 2015-7-16
 */
@SpringBootApplication
@EnableAutoConfiguration
@ComponentScan(basePackages = { "com.sungoin.netphoneLocal" })
@EntityScan(basePackages = {"com.sungoin.netphoneLocal.*.po"})
@EnableJpaRepositories("com.sungoin.netphoneLocal.*.dao")
@ImportResource(value = { "classpath:/conf/RmiContext.xml" })
public class Application {

    private static final Logger LOGGER = LoggerFactory.getLogger(Application.class);

    @Bean
    MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        factory.setMaxFileSize("10MB");
        factory.setMaxRequestSize("10MB");
        return factory.createMultipartConfig();
    }

    @Bean
    public EmbeddedServletContainerFactory servletContainer() {
        TomcatEmbeddedServletContainerFactory factory = new TomcatEmbeddedServletContainerFactory();
        factory.addConnectorCustomizers(new TomcatConnectorCustomizer() {
            @Override
            public void customize(Connector cnctr) {
                cnctr.setMaxPostSize(0);
            }
        });
        return factory;
    }
    
    public static void main(String[] args) {
        ApplicationContext ctx = null;
        try {
            LOGGER.info("start application...");
            ctx = SpringApplication.run(Application.class, args);
            //动态注册Rmi
            DynamicRmiRegister rmiRegister = ctx.getBean(DynamicRmiRegister.class);
            rmiRegister.registerRmi();
            LOGGER.info("application start success... start startupable instants");
            BaseService baseService = SpringHelper.getBean(BaseService.class);
            baseService.initBindPhone();
            StartupResister.doStart();
        } catch (Exception ex) {
            LOGGER.error("程序启动出错：{}", ex.getMessage(), ex);
            if (ctx != null) {
                SpringApplication.exit(ctx, new ExitCodeGenerator() {
                    @Override
                    public int getExitCode() {
                        return -1;
                    }
                });
            }
        }
    }
}
