package com.sungoin.netphoneLocal.business.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "AREA_STRATEGY_TEMPLATE")
public class AreaStrategyTemplate {

	@Id
	@Column(name="ID")
	private String id;
	
	@Column(name="NUMBER_CODE")
	private String numberCode;
	
	@Column(name="TEMPLATE_ID")
	private String templateId;
	
	@Column(name="DISTIRCTS")
	private String distircts;
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getNumberCode() {
		return numberCode;
	}
	public void setNumberCode(String numberCode) {
		this.numberCode = numberCode;
	}
	public String getTemplateId() {
		return templateId;
	}
	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}
	public String getDistircts() {
		return distircts;
	}
	public void setDistircts(String distircts) {
		this.distircts = distircts;
	}

    @Override
    public String toString() {
        return "AreaStrategyTemplate{" + "id=" + id + ", numberCode=" + numberCode + ", templateId=" + templateId + ", distircts=" + distircts + '}';
    }
    
}
