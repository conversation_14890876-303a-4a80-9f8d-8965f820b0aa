package com.sungoin.netphoneLocal.business.po;

import com.sungoin.netphoneLocal.midware.MidwareConstants;
import com.sungoin.netphoneLocal.util.DES;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * <AUTHOR>
 * @description：百度智能客服配置
 * @date ：2025/07/15 14:10
 */
@Entity
@Table(name = "robot_agent_config")
public class RobotAgentConfig {

    @Id
    @Column(name="id")
    private String id;

    /**
     * 400号码
     */
    @Column(name =  "number_code")
    private String numberCode;

    /**
     * 智能客服中继号码
     */
    @Column(name =  "bind_phone")
    private String bindPhone;

    /**
     * 智能客服接听最大中继数量
     */
    @Column(name =  "bind_num")
    private Integer bindNum;

    /** 呼叫次数. */
    @Column(name = "CALLNUM")
    private int callnum;

    /**
     * 接待模式。1 智能接待，2 兜底接待
     */
    @Column(name =  "call_mode")
    private Integer callMode;

    /**
     * 接待模式=智能接待模式使用此属性，触发场景呼叫第一个空闲（waitCallTime）秒后，自动转接bindPhone
     */
    @Column(name =  "wait_call_time")
    private Integer waitCallTime;
    
    /**
     * 工号
     */
    @Column(name = "REPORT_NUM")
    private String reportNum;
    
    @Transient
    private Integer remainTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNumberCode() {
        return numberCode;
    }

    public void setNumberCode(String numberCode) {
        this.numberCode = numberCode;
    }

    public String getBindPhone() {
        return bindPhone;
    }

    public void setBindPhone(String bindPhone) {
        this.bindPhone = bindPhone;
    }

    public Integer getBindNum() {
        return bindNum;
    }

    public void setBindNum(Integer bindNum) {
        this.bindNum = bindNum;
    }

    public Integer getCallMode() {
        return callMode;
    }

    public void setCallMode(Integer callMode) {
        this.callMode = callMode;
    }

    public Integer getWaitCallTime() {
        return waitCallTime;
    }

    public void setWaitCallTime(Integer waitCallTime) {
        this.waitCallTime = waitCallTime;
    }

    public int getCallnum() {
        return callnum;
    }

    public void setCallnum(int callnum) {
        this.callnum = callnum;
    }

    public String getReportNum() {
        return reportNum;
    }

    public void setReportNum(String reportNum) {
        this.reportNum = reportNum;
    }

    public int getRemainTime() {
        if(this.remainTime == null) {
            this.remainTime = this.waitCallTime;
        }
        return remainTime;
    }

    public void setRemainTime(int remainTime) {
        this.remainTime = remainTime;
    }
    
    @Override
    public String toString() {
        return "RobotAgentConfig{" + "id=" + id + ", numberCode=" + numberCode + ", bindPhone=" + bindPhone + ", bindNum=" + bindNum + ", callMode=" + callMode + ", waitCallTime=" + waitCallTime + '}';
    }
    
    public BindPhone toBindPhone(Dept dept) {
        BindPhone bindPhone = new BindPhone();
        bindPhone.setAreaModle("1");
        bindPhone.setTimeModle(1);
        bindPhone.setBindNum(this.bindNum);
        bindPhone.setOrigBindPhoneNo(this.bindPhone);
        bindPhone.setBindPhoneNo(DES.getTwiceEncString(this.bindPhone));
        bindPhone.setPbx(true);
        bindPhone.setId(MidwareConstants.CALL_TYPE_ROBOT + "_" + this.numberCode + "_" + this.bindPhone);
        bindPhone.setAgentId(bindPhone.getId());
        bindPhone.setLsh(this.bindPhone);
        bindPhone.setReportNum(this.reportNum);
        bindPhone.setRecordFlag(true);
        bindPhone.setCustomerNo(this.numberCode);
        bindPhone.setDept(dept);
        bindPhone.setDeptLsh(dept.getDeptLsh());
        bindPhone.setPassthrough(true);
        bindPhone.setWaitTime(25);
        return bindPhone;
    }
    public BindPhone toScreenBindPhone(Dept dept, String robotPhone) {
        BindPhone bindPhone = new BindPhone();
        bindPhone.setAreaModle("1");
        bindPhone.setTimeModle(1);
        bindPhone.setBindNum(0);
        bindPhone.setOrigBindPhoneNo(robotPhone);
        bindPhone.setBindPhoneNo(DES.getTwiceEncString(this.bindPhone));
        bindPhone.setPbx(true);
        bindPhone.setId(MidwareConstants.CALL_TYPE_ROBOT + "_" + this.numberCode + "_" + robotPhone);
        bindPhone.setAgentId(bindPhone.getId());
        bindPhone.setLsh(robotPhone);
        bindPhone.setReportNum(this.reportNum);
        bindPhone.setRecordFlag(true);
        bindPhone.setCustomerNo(this.numberCode);
        bindPhone.setDept(dept);
        bindPhone.setDeptLsh(dept.getDeptLsh());
        bindPhone.setPassthrough(false);
        bindPhone.setWaitTime(25);
        return bindPhone;
    }
}
