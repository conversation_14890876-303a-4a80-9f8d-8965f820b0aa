/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.po;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.Table;
import javax.persistence.Temporal;
import org.hibernate.annotations.GenericGenerator;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "T_FILE_TASK")
public class FileTask implements Serializable {

	private static final long serialVersionUID = 4117352319885868923L;
	
	@Id
	@GenericGenerator(name = "uuid", strategy = "uuid2")
	@GeneratedValue(generator = "uuid")
	private String id;
	
	@Temporal(javax.persistence.TemporalType.TIMESTAMP)
	private Date createTime; //创建时间
	
	@Temporal(javax.persistence.TemporalType.TIMESTAMP)
	private Date lastProcessTime; //最后处理时间
	
	private int status;	//状态
	
	private int processCount; //处理次数
	
	@Lob
	@Basic(fetch = FetchType.LAZY)
	@Column(columnDefinition = "Longtext")
	private String parpams; //任务参数
	
	// OutDataTask id
	private String dataTaskId;
	
	private String localPath;

	public FileTask(String dataTaskId, String localPath) {
		this.dataTaskId = dataTaskId;
		this.localPath = localPath;
		this.createTime = new Date();
	}
 
	public FileTask() {
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getLastProcessTime() {
		return lastProcessTime;
	}

	public void setLastProcessTime(Date lastProcessTime) {
		this.lastProcessTime = lastProcessTime;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public int getProcessCount() {
		return processCount;
	}

	public void setProcessCount(int processCount) {
		this.processCount = processCount;
	}

	public String getParpams() {
		return parpams;
	}

	public void setParpams(String parpams) {
		this.parpams = parpams;
	}

	public String getDataTaskId() {
		return dataTaskId;
	}

	public void setDataTaskId(String dataTaskId) {
		this.dataTaskId = dataTaskId;
	}

	public String getLocalPath() {
		return localPath;
	}

	public void setLocalPath(String localPath) {
		this.localPath = localPath;
	}
	
}
