package com.sungoin.netphoneLocal.business.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.GenericGenerator;

/**
 * 彩铃.
 */
@Entity
@Table(name = "T_BASE_COLOR_RING", indexes = { @Index(columnList = "customer_no") })
public class ColorRing extends IDComparator {

    private static final long serialVersionUID = -5016561144895141672L;

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    private String id;

    /** 流水号. */
    @Column(name = "LSH")
    private String lsh;

    /** 400号码. */
    @Column(name = "CUSTOMER_NO")
    private String customerNo;

    /** 彩铃文件名称. */
    @Column(name = "MSG_FILENAME")
    private String fileName;

    /** 彩铃文件路径. */
    @Column(name = "MSG_FILEFULLNAME")
    private String filePath;

    /** 功能. */
    @Column(name = "FUNCTION_CATEGORY")
    private Integer category;

    /** 创建时间. */
    @Column(name = "CREATE_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /** 使用状态：1当前使用中 0未使用. */
    @Column(name = "ONLINE_FLAG")
    private boolean onlineFlag;

    /** 创建人. */
    @Column(name = "CREATE_PERSON")
    private String createPerson;

    public ColorRing() {
    }

	@Override
    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLsh() {
        return this.lsh;
    }

    public void setLsh(String lsh) {
        this.lsh = lsh;
    }

    public String getCustomerNo() {
        return this.customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getFileName() {
        return this.fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return this.filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Integer getCategory() {
        return this.category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public boolean isOnlineFlag() {
        return this.onlineFlag;
    }

    public void setOnlineFlag(boolean onlineFlag) {
        this.onlineFlag = onlineFlag;
    }

    public String getCreatePerson() {
        return this.createPerson;
    }

    public void setCreatePerson(String createPerson) {
        this.createPerson = createPerson;
    }

    @Override
    public String toString() {
        return "ColorRing [id=" + this.id + ", lsh=" + this.lsh
            + ", customerNo=" + this.customerNo + ", fileName=" + this.fileName
            + ", filePath=" + this.filePath + ", category=" + this.category
            + ", createTime=" + this.createTime + ", onlineFlag="
            + this.onlineFlag + ", createPerson=" + this.createPerson + "]";
    }
}
