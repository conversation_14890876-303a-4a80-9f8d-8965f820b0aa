/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.po;

import java.io.Serializable;

/**
 *
 * <AUTHOR> 2015-8-21
 */
public abstract class IDComparator implements Serializable {
	private static final long serialVersionUID = -124701177333368578L;

	protected abstract String getId();

	@Override
	public boolean equals(Object obj) {
		if (obj == null) {
			return false;
		}
		if (!(obj instanceof IDComparator)) {
			return false;
		}
		final IDComparator other = (IDComparator) obj;
		if (this.getId() == null || other.getId() == null) {
			return false;
		}
		return this.getId().equals(other.getId());
	}

	@Override
	public int hashCode() {
		int hash = 7;
		hash = 89 * hash + (this.getId() != null ? this.getId().hashCode() : 0);
		return hash;
	}
}
