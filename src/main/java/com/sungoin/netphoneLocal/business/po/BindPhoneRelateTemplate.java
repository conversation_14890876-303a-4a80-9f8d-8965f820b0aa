package com.sungoin.netphoneLocal.business.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "BIND_PHONE_RELATE_TEMPLATE")
public class BindPhoneRelateTemplate {

	@Id
	@Column(name="ID")
	private String id;
	
	@Column(name="NUMBER_CODE")
	private String numberCode;
	
	@Column(name="BIND_PHONE_LSH")
	private String bindPhoneLsh;
	
	@Column(name="TEMPLATE_ID")
	private String templateId;

	/**
	 * 0:地区模板，1:时间模板
	 */
	@Column(name="TEMPLATE_TYPE")
	private String templateType;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getNumberCode() {
		return numberCode;
	}

	public void setNumberCode(String numberCode) {
		this.numberCode = numberCode;
	}

	public String getBindPhoneLsh() {
		return bindPhoneLsh;
	}

	public void setBindPhoneLsh(String bindPhoneLsh) {
		this.bindPhoneLsh = bindPhoneLsh;
	}

	public String getTemplateId() {
		return templateId;
	}

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}

	public String getTemplateType() {
		return templateType;
	}

	public void setTemplateType(String templateType) {
		this.templateType = templateType;
	}
	
}
