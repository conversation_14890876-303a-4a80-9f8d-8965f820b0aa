/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.po;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Temporal;

import org.apache.commons.lang3.BooleanUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2015-7-16
 */
@Entity
@Table(name = "T_BASE_USER", indexes = { @Index(columnList = "user_id"), @Index(columnList = "original_no") })
public class User extends IDComparator {

    private static final long serialVersionUID = -5496905148518342734L;

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    private String id;

    @Column(name = "user_id")
    private String number;
    
    /**
     * 400对应的小号.
     */
    @Column(name = "original_no")
    private String originalNo;

    /**
     * 400号码是否可用.
     */
    @Column(name = "use_flag")
    private boolean useFlag;

    /**
     * 彩铃.
     */
    @Column(name = "colorring_flag")
    private boolean colorringFlag;

    /**
     * IVR导航.
     */
    @Column(name = "ivr_flag")
    private boolean ivrFlag;

    /**
     * 工号.
     */
    @Column(name = "gh_flag")
    private boolean ghFlag;

    /**
     * 满意度.
     */
    @Column(name = "sati_flag")
    private boolean satiFlag;

    /**
     * 录音.
     */
    @Column(name = "record_flag")
    private boolean recordFlag;

    /** 超级导航. */
    @Column(name = "superivr_flag")
    private boolean superIvrFlag;

    /**
     * 留言.
     */
    @Column(name = "voicebox_flag")
    private boolean voiceBoxFlag;

    /**
     * 防伪码.
     */
    @Column(name = "voicecode_flag")
    private boolean voiceCodeFlag;

    /**
     * 中继.
     */
    @Column(name = "line_flag")
    private boolean lineFlag;

    /**
     * 来电弹屏.
     */
    @Column(name = "screen_flag")
    private boolean screenFlag;

    /**
     * 总机.
     */
    @Column(name = "exchange_flag")
    private boolean exchangeFlag;

    /**
     * 坐席.
     */
    @Deprecated
    @Column(name = "agent_flag")
    private boolean agentFlag;

    /**
     * 预摘机.
     */
    @Column(name = "PRE_OFFHOOK")
    private boolean preOffhook;

    /**
     * 离线.
     */
    @Column(name = "offine_flag")
    private boolean offineFlag;

    @Column(name = "update_user")
    private String updateUser;

    @Column(name = "update_time")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date updateTime;

    /**
     * 开通日期.
     */
    @Column(name = "START_TIME")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date startTime;

    /**
     * 监听标识.
     */
    @Column(name = "UPLOAD_RECORD")
    private Boolean uploadRecord;

    /**
     * 终止日期.
     */
    @Column(name = "END_TIME")
    @Temporal(javax.persistence.TemporalType.TIMESTAMP)
    private Date endTime;

    /**
     * 录音提示音
     */
    @Column(name = "RECORD_RING_FLAG")
    private Boolean recordRingFlag;

    @Column(name = "FLASH_RING_FLAG")
    private boolean flashringFlag;

    /**
     * 消息状态推送
     */
    @Column(name = "PUSH_STATUS_FLAG")
    private Boolean pushStatusFlag;

    @Column(name = "FINISHING_TIME")
    private Integer finishingTime;
	
	@Column(name = "QUEUE_TYPE")
    private Integer queueType;
	
	 /**
     * 来电记忆功能.
     */
    @Column(name = "MEMORY_FLAG")
    private Boolean memoryFlag;
	
	/**
     * 录音格式标识（0：mp3,1：wav)
     */
    @Column(name = "AUDIO_FLAG")
    private Integer audioFlag;
    
    /**
     * 直营标识
     */
    @Column(name = "DIRECT_FLAG")
    private Boolean directFlag;
    
    /** 百度号码标识. */
    @Column(name = "BAIDU_FLAG")
    private Integer baiduFlag;
    
    /** 语音播报. */
    @Column(name = "SMART_BROAD_FLAG")
    private Boolean smartBroadFlag;
    
    /**
     * 新用户平台标识
     */
    @Column(name = "PLATFORM_PLUS_FLAG")
    private Boolean platformPlusFlag;

    /** 挂机 短信功能. */
    @Column(name = "SMS_FLAG")
    private Boolean smsFlag;
    
    /** 呼叫轨迹开关. */
    @Column(name = "TRACE_FLAG")
    private Boolean traceFlag;
    
    /** 排队等待计数，默认为0，可改为1,调整为1之后则从1开始提示前方排队等待人数. */
    @Column(name = "QUEUE_COUNT_FLAG")
    private Boolean queueCountFlag;
    
    /** 漏接短信功能. */
    @Column(name = "MISS_SMS_FLAG")
    private Boolean missSmsFlag;
    
    /**
     * 离线排队人数，>0 开启
     */
    @Column(name = "QUEUE_OFFLINE_COUNT")
    private Integer queueOfflineCount;
    
    /** 智能语音播报次数. */
    @Column(name = "SMART_BROAD_ONCE_FLAG")
    private Boolean smartBroadOnceFlag;
    
    /** 语音导航开关. */
    @Column(name = "AUDIO_IVR_FLAG")
    private Boolean audioIvrFlag;
    
    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "user")
    @OrderBy(value = "DEPTH")
    private List<Dept> depts = new ArrayList<Dept>();
    
    /**
     * 状态多号标识. 
     * 
     */
    @Column(name = "UNIQUE_NAME")
    private String uniqueName;

    /**
     * 转接失败接回标识
     * ALTER TABLE t_base_user ADD COLUMN TRANS_BACK_FLAG bit;
     */
    @Column(name = "TRANS_BACK_FLAG")
    private Boolean transBackFlag;

    /** 闪信短信功能. */
    @Column(name = "SX_SMS_FLAG")
    private Boolean sxSmsFlag;

    /**
     * 通话结束后状态设置【setAcsType，0：空闲，1：忙碌】，默认：空闲
     */
    @Column(name = "SET_ACS_TYPE")
    private Boolean setAcsType;

    public String getUniqueName() {
        return uniqueName;
    }

    public void setUniqueName(String uniqueName) {
        this.uniqueName = uniqueName;
    }

    public void addDept(Dept dept) {
        this.depts.add(dept);
        dept.setUser(this);
    }

    public void removeDept(Dept dept) {
        this.depts.remove(dept);
    }

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "user")
    @Where(clause = "use_flag=1")
    @OrderBy(value = "calledPhone desc,callerCode desc,callerType desc")
    private List<InterConnection> interConns = new ArrayList<InterConnection>();

    public void addInterConnection(InterConnection conn) {
        this.interConns.add(conn);
        conn.setUser(this);
    }

    public void removeInterConnection(InterConnection conn) {
        this.interConns.remove(conn);
    }

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "user")
    private List<WhiteList> whiteList = new ArrayList<WhiteList>();

    public void addWhiteList(WhiteList whiteList) {
        this.whiteList.add(whiteList);
        whiteList.setUser(this);
    }

    public void removeWhiteList(WhiteList whiteList) {
        this.whiteList.remove(whiteList);
    }

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "user")
    private List<BlackList> blackList = new ArrayList<BlackList>();

    public void addBlackList(BlackList blackList) {
        this.blackList.add(blackList);
        blackList.setUser(this);
    }

    public void removeBlackList(BlackList blackList) {
        this.blackList.remove(blackList);
    }

    public User() {
    }

    @Override
    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNumber() {
        return this.number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getOriginalNo() {
        return this.originalNo;
    }

    public void setOriginalNo(String originalNo) {
        this.originalNo = originalNo;
    }

    public boolean isUseFlag() {
        return this.useFlag;
    }

    public void setUseFlag(boolean useFlag) {
        this.useFlag = useFlag;
    }

    public boolean isColorringFlag() {
        return this.colorringFlag;
    }

    public void setColorringFlag(boolean colorringFlag) {
        this.colorringFlag = colorringFlag;
    }

    public boolean isIvrFlag() {
        return this.ivrFlag;
    }

    public void setIvrFlag(boolean ivrFlag) {
        this.ivrFlag = ivrFlag;
    }

    public boolean isGhFlag() {
        return this.ghFlag;
    }

    public void setGhFlag(boolean ghFlag) {
        this.ghFlag = ghFlag;
    }

    public boolean isSatiFlag() {
        return this.satiFlag;
    }

    public void setSatiFlag(boolean satiFlag) {
        this.satiFlag = satiFlag;
    }

    public boolean isRecordFlag() {
        return this.recordFlag;
    }

    public void setRecordFlag(boolean recordFlag) {
        this.recordFlag = recordFlag;
    }

    public boolean isVoiceBoxFlag() {
        return this.voiceBoxFlag;
    }

    public void setVoiceBoxFlag(boolean voiceBoxFlag) {
        this.voiceBoxFlag = voiceBoxFlag;
    }

    public boolean isVoiceCodeFlag() {
        return this.voiceCodeFlag;
    }

    public void setVoiceCodeFlag(boolean voiceCodeFlag) {
        this.voiceCodeFlag = voiceCodeFlag;
    }

    public boolean isLineFlag() {
        return this.lineFlag;
    }

    public void setLineFlag(boolean lineFlag) {
        this.lineFlag = lineFlag;
    }

    public boolean isScreenFlag() {
        return this.screenFlag && this.getPushStatusFlag();
    }

    public void setScreenFlag(boolean screenFlag) {
        this.screenFlag = screenFlag;
    }

    public boolean isExchangeFlag() {
        return this.exchangeFlag;
    }

    public void setExchangeFlag(boolean exchangeFlag) {
        this.exchangeFlag = exchangeFlag;
    }

    @Deprecated
    public boolean isAgentFlag() {
        return this.agentFlag;
    }

    @Deprecated
    public void setAgentFlag(boolean agentFlag) {
        this.agentFlag = agentFlag;
    }

    public boolean isOffineFlag() {
        return this.offineFlag;
    }

    public void setOffineFlag(boolean offineFlag) {
        this.offineFlag = offineFlag;
    }

    public boolean isPreOffhook() {
        return this.preOffhook;
    }

    public void setPreOffhook(boolean preOffhook) {
        this.preOffhook = preOffhook;
    }

    public String getUpdateUser() {
        return this.updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public boolean isSuperIvrFlag() {
        return this.superIvrFlag;
    }

    public void setSuperIvrFlag(boolean superIvrFlag) {
        this.superIvrFlag = superIvrFlag;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getStartTime() {
        return this.startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return this.endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<Dept> getDepts() {
        return this.depts;
    }

    public void setDepts(List<Dept> depts) {
        this.depts = depts;
    }

    public List<WhiteList> getWhiteList() {
        return this.whiteList;
    }

    public void setWhiteList(List<WhiteList> whiteList) {
        this.whiteList = whiteList;
    }

    public List<BlackList> getBlackList() {
        return this.blackList;
    }

    public void setBlackList(List<BlackList> blackList) {
        this.blackList = blackList;
    }

    @JsonIgnore
    public Dept getRootDept() {
        return this.getDepts().get(0);
    }

    public Boolean getRecordRingFlag() {
        return this.recordRingFlag;
    }

    public void setRecordRingFlag(Boolean recordRingFlag) {
        this.recordRingFlag = recordRingFlag;
    }

    public boolean isRecordRingFlag() {
        return this.recordRingFlag == null ? false : this.recordRingFlag;
    }

    public List<InterConnection> getInterConns() {
        return this.interConns;
    }

    public void setInterConns(List<InterConnection> interConns) {
        this.interConns = interConns;
    }

    public boolean isUploadRecord() {
        return this.uploadRecord == null ? false : this.uploadRecord;
    }

    public Boolean getUploadRecord() {
        return this.uploadRecord;
    }

    public void setUploadRecord(Boolean uploadRecord) {
        this.uploadRecord = uploadRecord;
    }

    public Boolean getPushStatusFlag() {
        return this.pushStatusFlag == null ? true : this.pushStatusFlag;
    }

    public void setPushStatusFlag(Boolean pushStatusFlag) {
        this.pushStatusFlag = pushStatusFlag;
    }

    public Integer getFinishingTime() {
        return this.finishingTime == null ? 1 : this.finishingTime;
    }

    public void setFinishingTime(Integer finishingTime) {
        this.finishingTime = finishingTime;
    }

    public boolean isFlashringFlag() {
        return this.flashringFlag;
    }

    public void setFlashringFlag(boolean flashringFlag) {
        this.flashringFlag = flashringFlag;
    }

	public Integer getQueueType() {
		return queueType;
	}

	public void setQueueType(Integer queueType) {
		this.queueType = queueType;
	}

	public Boolean isMemoryFlag() {
		return this.memoryFlag == null ? false : this.memoryFlag;
	}

	public void setMemoryFlag(Boolean memoryFlag) {
		this.memoryFlag = memoryFlag;
	}

	public Integer getAudioFlag() {
		return audioFlag;
	}

	public void setAudioFlag(Integer audioFlag) {
		this.audioFlag = audioFlag;
	}
	
	public boolean transMp3() {
		return this.audioFlag == null || this.audioFlag == 0;
	}

    public Boolean getDirectFlag() {
        return directFlag;
    }

    public void setDirectFlag(Boolean directFlag) {
        this.directFlag = directFlag;
    }
    
    public boolean isDirectNumber() {
        return this.directFlag == null ? false : this.directFlag;
    }
	
    public Integer getBaiduFlag() {
		return this.baiduFlag;
	}

	public void setBaiduFlag(Integer baiduFlag) {
		this.baiduFlag = baiduFlag;
	}
    
    public boolean isBaiduAifanfan() {
        return this.baiduFlag != null && this.baiduFlag == 1;
    }
    
    public boolean isBaiduJimuyu() {
        return this.baiduFlag != null && this.baiduFlag == 2;
    }
    
	public Boolean isSmartBroadFlag() {
		return this.smartBroadFlag == null ? false : this.smartBroadFlag;
	}
    
    public boolean isPlatformPlus() {
        return this.platformPlusFlag == null ? false : this.platformPlusFlag; 
    }
    
    public boolean isSmsFlag() {
        return this.smsFlag == null ? false : this.smsFlag; 
    }
    
    public boolean isTraceFlag() {
		return BooleanUtils.isTrue(this.traceFlag);
	}
    
    public boolean isQueueCountFlag() {
  		return BooleanUtils.isTrue(this.queueCountFlag);
  	}

    public Integer getQueueOfflineCount() {
        return queueOfflineCount;
    }

    public void setQueueOfflineCount(Integer queueOfflineCount) {
        this.queueOfflineCount = queueOfflineCount;
    }
    
    public int getQueueOfflineCountIntValue() {
        return this.queueOfflineCount == null ? 0 : this.queueOfflineCount;
    }
    
	public boolean isMissSmsFlag() {
		return BooleanUtils.isTrue(this.missSmsFlag);
	}

	public void setMissSmsFlag(Boolean missSmsFlag) {
		this.missSmsFlag = missSmsFlag;
	}

    public List<BlackList> getGlobalBlackList() {
        return this.blackList.stream().filter(black -> black.getExt() == null).collect(Collectors.toList());
    }
    
    
    public boolean isSmartBroadOnceFlag() {
  		return BooleanUtils.isTrue(this.smartBroadOnceFlag);
  	}
    
    public boolean isAudioIvrFlag() {
  		return BooleanUtils.isTrue(this.audioIvrFlag);
  	}

    public Boolean getTransBackFlag() {
        return transBackFlag == null ? Boolean.TRUE : transBackFlag;
    }

    public void setTransBackFlag(Boolean transBackFlag) {
        this.transBackFlag = transBackFlag;
    }

    public boolean isSxSmsFlag() {
        return BooleanUtils.isTrue(this.sxSmsFlag);
    }

    public boolean isSetAcsType() {
        return BooleanUtils.isTrue(this.setAcsType);
    }

    @Override
    public String toString() {
        return "User [number=" + this.number + ", originalNo=" + this.originalNo + ", useFlag=" + this.useFlag + "]";
    }
}
