package com.sungoin.netphoneLocal.business.po;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;

/**
 * 手机号段.
 */
@Entity
@Table(name = "T_BASE_MOBILE_LOCATION", indexes = { @Index(columnList = "NO") })
public class MobileLocation implements Serializable {

    private static final long serialVersionUID = -3042234065216849915L;

    /** 号段. */
    @Id
    @Column(name = "NO")
    private String no;

    /** 省份. */
    @Column(name = "PROVINCE")
    private String province;

    /** 城市. */
    @Column(name = "CITY")
    private String city;

    /** 卡的类型. */
    @Column(name = "LOCATION")
    private String location;

    /** 区号. */
    @Column(name = "DISTRICT_NO")
    private String districtNo;

    /** 运营商标识：1：移动，2.联通，3电信. */
    @Column(name = "PLATFORM")
    private Integer platform;

    public String getNo() {
        return this.no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public String getProvince() {
        return this.province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return this.city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getLocation() {
        return this.location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getDistrictNo() {
        return this.districtNo;
    }

    public void setDistrictNo(String districtNo) {
        this.districtNo = districtNo;
    }

    public Integer getPlatform() {
        return this.platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    @Override
    public String toString() {
        return "MobileLocation [no=" + this.no + ", province=" + this.province
            + ", city=" + this.city + ", location=" + this.location
            + ", districtNo=" + this.districtNo + ", platform=" + this.platform
            + "]";
    }

}
