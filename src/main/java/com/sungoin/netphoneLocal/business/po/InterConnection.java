package com.sungoin.netphoneLocal.business.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

/**
 * 互联互通
 */
@Entity
@Table(name = "T_BASE_INTER_CONNECTION", indexes = { @Index(columnList = "customer_no") })
public class InterConnection extends IDComparator {

    private static final long serialVersionUID = 2196781831852301261L;

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    private String id;

    /** 400号码. */
    @Column(name = "CUSTOMER_NO")
    private String customerNo;

    /** 转接号码(0000-表示所有号码，0001-所有手机号码，0002-所有固话号码) 也可以指定具体的转接号码,支持区号,多个以逗号分割 */
    @Column(name = "called_phone")
    private String calledPhone;

    /** 来电地区(0000-表示所有地区，0001-与被叫同地区，0002-与被叫不同地区) 也可以指定具体的区号,以逗号分隔 */
    @Column(name = "caller_code")
    private String callerCode;

    /** 来电类型 0-手机和固话，1-手机，2-固话 */
    @Column(name = "CALLER_TYPE")
    private String callerType;

    /** 转接配置模式(-1：使用流程配置 0-主叫号码 1-前缀+主叫号码 2-前缀+特殊号码 3-被叫加区号(真正的区号)) */
    @Column(name = "TX_MODE")
    private Integer txMode;

    /** 显示号码加减区号(0-自动 1-加区号，2-减区号) */
    @Column(name = "TX_CODE")
    private String txCode;

    /** 显示前缀(只有txMode等于1或2时有效) */
    @Column(name = "PREFIX")
    private String prefix;

    /** 特殊显示号码(txMode等于2有效或者测试时用于模拟主叫) */
    @Column(name = "DISPLAY")
    private String display;

    /** 主叫号码(此项只有useFlag=0时才需要填写，非0时传-1即可) 手机和固话号码均不用加区号 */
    @Column(name = "CALLER_PHONE")
    private String callerPhone;

    /** 使用标识 0-测试使用 1-正式使用 */
    @Column(name = "USE_FLAG")
    private boolean useFlag;

    /** 呼叫方式(0-默认 1-直接呼叫，2-转移呼叫) */
    @Column(name = "CALL_TYPE")
    private int callType;

    /** 转移呼叫显示号码加减区号(0-自动 1-加区号，2-减区号) */
    @Column(name = "TRANS_CODE")
    private int transCode;
    
	/** 运营商(0-移动,1-联通,2-电信). */
	@Column(name = "YYS_MODE")
	private String yysMode;
	
    /** 排序 */
    @Column(name = "ORDER_BY")
    private String orderBy;

    @ManyToOne(fetch = FetchType.LAZY)
    private User user;

    @Override
    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCustomerNo() {
        return this.customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getCalledPhone() {
        return this.calledPhone;
    }

    public void setCalledPhone(String calledPhone) {
        this.calledPhone = calledPhone;
    }

    public String getCallerCode() {
        return this.callerCode;
    }

    public void setCallerCode(String callerCode) {
        this.callerCode = callerCode;
    }

    public String getCallerType() {
        return this.callerType;
    }

    public void setCallerType(String callerType) {
        this.callerType = callerType;
    }

    public Integer getTxMode() {
        return this.txMode;
    }

    public void setTxMode(Integer txMode) {
        this.txMode = txMode;
    }

    public String getTxCode() {
        return this.txCode;
    }

    public void setTxCode(String txCode) {
        this.txCode = txCode;
    }

    public String getPrefix() {
        return this.prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getDisplay() {
        return this.display;
    }

    public void setDisplay(String display) {
        this.display = display;
    }

    public String getCallerPhone() {
        return this.callerPhone;
    }

    public void setCallerPhone(String callerPhone) {
        this.callerPhone = callerPhone;
    }

    public boolean isUseFlag() {
        return this.useFlag;
    }

    public void setUseFlag(boolean useFlag) {
        this.useFlag = useFlag;
    }

    public int getCallType() {
        return this.callType;
    }

    public void setCallType(int callType) {
        this.callType = callType;
    }

    public int getTransCode() {
        return this.transCode;
    }

    public void setTransCode(int transCode) {
        this.transCode = transCode;
    }

    public String getOrderBy() {
        return this.orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public User getUser() {
        return this.user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getYysMode() {
		return yysMode;
	}

	public void setYysMode(String yysMode) {
		this.yysMode = yysMode;
	}

	@Override
    public String toString() {
        return "InterConnection [customerNo=" + this.customerNo + ", calledPhone=" + this.calledPhone + ", callerCode="
            + this.callerCode + "]";
    }

}
