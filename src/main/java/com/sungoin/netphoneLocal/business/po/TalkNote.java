package com.sungoin.netphoneLocal.business.po;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.GenericGenerator;

/**
 * 通话记录表.
 */
@Entity
@Table(name = "T_BUSINESS_TALK_NOTE")
public class TalkNote implements Serializable {

    private static final long serialVersionUID = 5824277283751515789L;

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    private String id;

    /** 流水号. 保存绑定号码ID*/
    @Column(name = "LSH")
    private String lsh;

    /** 400号码. */
    @Column(name = "CUSTOMER_NO")
    private String customerNo;

    /** 400对应小号. */
    @Column(name = "CUSTOMER_ORIGINAL_NO")
    private String originalNo;

    /** 主叫号码. */
    @Column(name = "CALLER_NO")
    private String callerNo;

    /** 被叫号码. */
    @Column(name = "CALLEE_NO")
    private String calleeNo;

    /** 呼入时间. */
    @Column(name = "INCOMING_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date incomingTime;

    /** 呼出时间. */
    @Column(name = "OUTGOING_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date outgoingTime;

    /** 主叫摘机时间. */
    @Column(name = "CALLER_OFFHOOK_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date callerOffhookTime;

    /** 挂机时间. */
    @Column(name = "ONHOOK_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date onhookTime;

    /** 被叫摘机时间. */
    @Column(name = "CALLEE_OFFHOOK_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date calleeOffhookTime;

    /** 通话时长. */
    @Column(name = "TALK_INTERVAL")
    private long talkInterval;

    /** 通话类型;1普通未接通2转IVR未接通3普通接通4IVR接通5留言，默认1. */
    @Column(name = "TALK_TYPE")
    private Integer talkType = 1;

    /** 挂机模式:0主叫挂机1被叫挂机. */
    @Column(name = "ONHOOK_MODE")
    private Boolean callerOnHook;

    /** 工号. */
    @Column(name = "gh")
    private Long gh;

    /** 结束标志：0正常结束 1非正常结束. */
    @Column(name = "END_FLAG")
    private boolean endFlag;

    /** 对应部门. */
    @Column(name = "DEPT_ID")
    private String deptId;
    
    /**
     * 百度回传字段
     */
    @Column(name = "USER_FIELD")
    private String userField;
    
    /**
     * 未接原因
     */
    @Column(name = "ERROR_CODE")
    private Integer errorCode;
    
    public TalkNote() {
    }

    public TalkNote(String customerNo, String originalNo, String callerNo,
        Date incomingTime) {
        this.customerNo = customerNo;
        this.originalNo = originalNo;
        this.callerNo = callerNo;
        this.incomingTime = incomingTime;
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLsh() {
        return this.lsh;
    }

    public void setLsh(String lsh) {
        this.lsh = lsh;
    }

    public String getCustomerNo() {
        return this.customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getOriginalNo() {
        return this.originalNo;
    }

    public void setOriginalNo(String originalNo) {
        this.originalNo = originalNo;
    }

    public String getCallerNo() {
        return this.callerNo;
    }

    public void setCallerNo(String callerNo) {
        this.callerNo = callerNo;
    }

    public String getCalleeNo() {
        return this.calleeNo;
    }

    public void setCalleeNo(String calleeNo) {
        this.calleeNo = calleeNo;
    }

    public Date getIncomingTime() {
        return this.incomingTime;
    }

    public void setIncomingTime(Date incomingTime) {
        this.incomingTime = incomingTime;
    }

    public Date getOutgoingTime() {
        return this.outgoingTime;
    }

    public void setOutgoingTime(Date outgoingTime) {
        this.outgoingTime = outgoingTime;
    }

    public Date getCallerOffhookTime() {
        return this.callerOffhookTime;
    }

    public void setCallerOffhookTime(Date callerOffhookTime) {
        this.callerOffhookTime = callerOffhookTime;
    }

    public Date getOnhookTime() {
        return this.onhookTime;
    }

    public void setOnhookTime(Date onhookTime) {
        this.onhookTime = onhookTime;
    }

    public Date getCalleeOffhookTime() {
        return this.calleeOffhookTime;
    }

    public void setCalleeOffhookTime(Date calleeOffhookTime) {
        this.calleeOffhookTime = calleeOffhookTime;
    }

    public long getTalkInterval() {
        return this.talkInterval;
    }

    public void setTalkInterval(long talkInterval) {
        this.talkInterval = talkInterval;
    }

    public Integer getTalkType() {
        return this.talkType;
    }

    public void setTalkType(Integer talkType) {
        this.talkType = talkType;
    }

    public Boolean getCallerOnHook() {
        return this.callerOnHook;
    }

    public void setCallerOnHook(Boolean callerOnHook) {
        this.callerOnHook = callerOnHook;
    }

    public Long getGh() {
        return this.gh;
    }

    public void setGh(Long gh) {
        this.gh = gh;
    }

    public boolean isEndFlag() {
        return this.endFlag;
    }

    public void setEndFlag(boolean endFlag) {
        this.endFlag = endFlag;
    }

    public String getDeptId() {
        return this.deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getUserField() {
        return userField;
    }

    public void setUserField(String userField) {
        this.userField = userField;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }
    
}
