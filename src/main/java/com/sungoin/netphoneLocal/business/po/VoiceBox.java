package com.sungoin.netphoneLocal.business.po;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.GenericGenerator;

/**
 * 语音信箱.
 */
@Entity
@Table(name = "T_BUSINESS_VOICE_BOX",indexes = { @Index(columnList = "TALK_NOTE_ID")})
public class VoiceBox implements Serializable {

    private static final long serialVersionUID = 8031509482829018215L;

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    private String id;

    /** 400号码. */
    @Column(name = "CUSTOMER_NO")
    private String customerNo;

    /** 留言文件名称. */
    @Column(name = "MSG_FILENAME")
    private String fileName;

    /** 留言文件路径. */
    @Column(name = "MSG_FILEFULLNAME")
    private String filePath;

    /** 留言开始时间. */
    @Column(name = "START_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date startTime;

    /** 留言结束时间. */
    @Column(name = "END_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date endTime;

    /** 主叫号码. */
    @Column(name = "CALLER_NO")
    private String callerNo;

    /** 留言时长. */
    @Column(name = "MESSAGE_INTERVAL")
    private long interval;

    /** 部门流水号(如果是转到ivr部门则是部门id，否则是400号码). */
    @Column(name = "DEPT_LSH")
    private String deptLsh;

    /** 结束标志：0正常结束 1非正常结束. */
    @Column(name = "END_FLAG")
    private boolean endFlag;

    /** 对应通话记录. */
    @Column(name = "TALK_NOTE_ID")
    private String talkNoteId;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCustomerNo() {
        return this.customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getFileName() {
        return this.fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return this.filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Date getStartTime() {
        return this.startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return this.endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getCallerNo() {
        return this.callerNo;
    }

    public void setCallerNo(String callerNo) {
        this.callerNo = callerNo;
    }

    public long getInterval() {
        return this.interval;
    }

    public void setInterval(long interval) {
        this.interval = interval;
    }

    public String getDeptLsh() {
        return this.deptLsh;
    }

    public void setDeptLsh(String deptLsh) {
        this.deptLsh = deptLsh;
    }

    public boolean isEndFlag() {
        return this.endFlag;
    }

    public void setEndFlag(boolean endFlag) {
        this.endFlag = endFlag;
    }

    public String getTalkNoteId() {
        return this.talkNoteId;
    }

    public void setTalkNoteId(String talkNoteId) {
        this.talkNoteId = talkNoteId;
    }

}
