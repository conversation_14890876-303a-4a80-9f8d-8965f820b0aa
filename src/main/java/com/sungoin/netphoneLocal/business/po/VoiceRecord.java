package com.sungoin.netphoneLocal.business.po;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.GenericGenerator;

/**
 * 录音记录.
 */
@Entity
@Table(name = "T_BUSINESS_VOICE_RECORD",indexes = { @Index(columnList = "TALK_NOTE_ID")})
public class VoiceRecord implements Serializable {

    private static final long serialVersionUID = 589275573796222325L;

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    private String id;

    /** 400号码. */
    @Column(name = "CUSTOMER_NO")
    private String customerNo;

    /** 主叫号码. */
    @Column(name = "CALLER_NO")
    private String callerNo;

    /** 被叫号码. */
    @Column(name = "CALLEE_NO")
    private String calleeNo;

    /** 录音开始时间. */
    @Column(name = "START_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date startTime;

    /** 录音结束时间. */
    @Column(name = "END_TIME")
    private Date endTime;

    /** 录音文件名称. */
    @Column(name = "FILE_NAME")
    private String fileName;

    /** 录音文件路径. */
    @Column(name = "FILE_FULLNAME")
    private String filePath;

    /** 录音时长. */
    @Column(name = "RECORD_INTERVAL")
    private long interval;

    /** 结束标志：0正常结束 1非正常结束. */
    @Column(name = "END_FLAG")
    private boolean endFlag;

    /** 对应通话记录. */
    @Column(name = "TALK_NOTE_ID")
    private String talkNoteId;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCustomerNo() {
        return this.customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getCallerNo() {
        return this.callerNo;
    }

    public void setCallerNo(String callerNo) {
        this.callerNo = callerNo;
    }

    public String getCalleeNo() {
        return this.calleeNo;
    }

    public void setCalleeNo(String calleeNo) {
        this.calleeNo = calleeNo;
    }

    public Date getStartTime() {
        return this.startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return this.endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getFileName() {
        return this.fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return this.filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public long getInterval() {
        return this.interval;
    }

    public void setInterval(long interval) {
        this.interval = interval;
    }

    public boolean isEndFlag() {
        return this.endFlag;
    }

    public void setEndFlag(boolean endFlag) {
        this.endFlag = endFlag;
    }

    public String getTalkNoteId() {
        return this.talkNoteId;
    }

    public void setTalkNoteId(String talkNoteId) {
        this.talkNoteId = talkNoteId;
    }
}
