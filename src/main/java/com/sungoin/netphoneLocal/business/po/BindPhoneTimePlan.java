package com.sungoin.netphoneLocal.business.po;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

/**
 * 绑定号码时间策略.
 */
@Entity
@Table(name = "T_BASE_PHONE_TIME_PLAN", indexes = { @Index(columnList = "customer_no") })
public class BindPhoneTimePlan extends IDComparator {

    private static final long serialVersionUID = -3188560146292756670L;

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    private String id;

    /** 400号码. */
    @Column(name = "CUSTOMER_NO")
    private String customerNo;

    /** 绑定号码流水号. */
    @Column(name = "BINDPHONE_LSH")
    private String bindPhoneLsh;

    /** 时间日期数. */
    @Column(name = "TIME_TITLE")
    private Integer timeTitle;

    /** 开始时间. */
    @Column(name = "START_TIME")
    private String startTime;

    /** 结束时间. */
    @Column(name = "END_TIME")
    private String endTime;

    /** 是否选择. */
    @Column(name = "switch")
    private boolean checked;

    /** 对应绑定号码. */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BINDPHONE_ID", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private BindPhone bindPhone;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCustomerNo() {
        return this.customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public Integer getTimeTitle() {
        return this.timeTitle;
    }

    public void setTimeTitle(Integer timeTitle) {
        this.timeTitle = timeTitle;
    }

    public String getStartTime() {
        return this.startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return this.endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public boolean isChecked() {
        return this.checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public String getBindPhoneLsh() {
        return this.bindPhoneLsh;
    }

    public void setBindPhoneLsh(String bindPhoneLsh) {
        this.bindPhoneLsh = bindPhoneLsh;
    }

    public BindPhone getBindPhone() {
        return this.bindPhone;
    }

    public void setBindPhone(BindPhone bindPhone) {
        this.bindPhone = bindPhone;
    }

    @Override
    public String toString() {
        return "BindPhoneTimePlan [id=" + this.id + ", customerNo="
            + this.customerNo + ", bindPhoneLsh=" + this.bindPhoneLsh + "]";
    }

}
