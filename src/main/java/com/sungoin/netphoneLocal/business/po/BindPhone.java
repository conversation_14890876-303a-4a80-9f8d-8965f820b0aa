 package com.sungoin.netphoneLocal.business.po;

import com.sungoin.netphoneLocal.util.DES;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;

/**
 * 绑定号码.
 */
@Entity
@Table(name = "T_BASE_BIND_PHONE", indexes = { @Index(columnList = "customer_no") })
public class BindPhone extends IDComparator {

    private static final long serialVersionUID = 2196781831852301261L;

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    private String id;

    /** 400号码. */
    @Column(name = "CUSTOMER_NO")
    private String customerNo;

    /** 流水号. */
    @Column(name = "LSH")
    private String lsh;

    /** 绑定电话号码. */
    @Column(name = "BIND_PHONENO")
    private String bindPhoneNo;
    
    @Transient 
    private String origBindPhoneNo;

    /** 报工号. */
    @Column(name = "REPORT_NUM")
    private String reportNum;

    /** 时间策略:1.全部,2.周一周五,3,自定义. */
    @Column(name = "TIME_MODLE")
    private Integer timeModle;

    /** 地域设置,1默认值,为全部接听;如果只设置区号则只接听设置区号的,区号之间用,隔开. */
    @Lob
    @Column(name = "AREA_MODLE")
    private String areaModle;

    /** 中继绑定数量:默认为1,如果有绑定的同时在这个号上通话的只有绑定的数量. */
    @Column(name = "BIND_NUM")
    private Integer bindNum = 1;

    /** 创建人. */
    @Column(name = "CREATE_PERSON")
    private String createPerson;

    /** 创建时间. */
    @Column(name = "CREATE_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /** 呼叫模式:0.顺序呼,1.随机呼,2循环呼. */
    @Column(name = "CALL_MODLE")
    private Integer callModle;

    /** 等待时长. */
    @Column(name = "WAIT_TIME")
    private Integer waitTime;

    /** 号码序号. */
    @Column(name = "ORDER_BY")
    private Long orderBy;

    /** 是否为交换机0否1是默认为0. */
    @Column(name = "IS_PBX")
    private boolean pbx;

    /** 座席状态 'idle'-空闲 'busy'- 忙碌 ' 'connect' 通话中 'offline'-离线. */
    @Column(name = "STATUS")
    private String status;

    /** 呼叫次数. */
    @Column(name = "CALLNUM")
    private int callnum;

    /** 坐席Id. */
    @Column(name = "AGENT_ID")
    private String agentId;

    /** 登录标识 1是未登陆属于绑定电话状态 0是已登录可以弹屏进整理状态. */
    @Column(name = "NEED_LOGIN")
    private boolean needLogin = true;

    /** 是否透传. */
    @Column(name = "passthrough")
    private Boolean passthrough;

    /**
     * 部门流水号
     * 如果dept为空，且deptgLsh不为空
     * 查询一次dept，查到更新dept否则
     * 清空deptLsh（数据同步时可能会先插入部门再插入坐席提示音）
     * .
     */
    @Column(name = "dept_lsh")
    private String deptLsh;

    /** 对应部门. */
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "DEPT_ID", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Dept dept;

    /** 当前正在进行的通话记录. */
    @Column(name = "CURRENT_TALK_ID")
    private String currentTalkId;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "bindPhone")
    @Where(clause = "switch=1")
    private List<BindPhoneTimePlan> timePlanList = new ArrayList<BindPhoneTimePlan>();
    
    @Column(name = "RECORD_FLAG")
    private Boolean recordFlag;
    
    /**
     * 权重值(策略为权重模式时使用)
     */
    @Column(name = "RATIO")
    private Integer ratio;

    /**
     * 接听等级值(策略为等级接听时使用)
     */
    @Column(name="PHONE_LEVEL")
    private Integer phoneLevel;

    public void addBindPhoneTimePlan(BindPhoneTimePlan bindPhoneTimePlan) {
        this.timePlanList.add(bindPhoneTimePlan);
        bindPhoneTimePlan.setBindPhone(this);
    }

    public void removeBindPhoneTimePlan(BindPhoneTimePlan bindPhoneTimePlan) {
        this.timePlanList.remove(bindPhoneTimePlan);
    }

    public BindPhone() {
    }

    @Override
    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCustomerNo() {
        return this.customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getLsh() {
        return this.lsh;
    }

    public void setLsh(String lsh) {
        this.lsh = lsh;
    }

    public String getBindPhoneNo() {
        return this.bindPhoneNo;
    }

    public void setBindPhoneNo(String bindPhoneNo) {
        this.bindPhoneNo = bindPhoneNo;
    }

    public String getReportNum() {
        return this.reportNum;
    }

    public void setReportNum(String reportNum) {
        this.reportNum = reportNum;
    }

    public Integer getTimeModle() {
        return this.timeModle;
    }

    public void setTimeModle(Integer timeModle) {
        this.timeModle = timeModle;
    }

    public String getAreaModle() {
        return this.areaModle;
    }

    public void setAreaModle(String areaModle) {
        this.areaModle = areaModle;
    }

    public Integer getBindNum() {
        return this.bindNum;
    }

    public void setBindNum(Integer bindNum) {
        this.bindNum = bindNum;
    }

    public String getCreatePerson() {
        return this.createPerson;
    }

    public void setCreatePerson(String createPerson) {
        this.createPerson = createPerson;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getCallModle() {
        return this.callModle;
    }

    public void setCallModle(Integer callModle) {
        this.callModle = callModle;
    }

    public Integer getWaitTime() {
        return this.waitTime;
    }

    public void setWaitTime(Integer waitTime) {
        this.waitTime = waitTime;
    }

    public Long getOrderBy() {
        return this.orderBy;
    }

    public void setOrderBy(Long orderBy) {
        this.orderBy = orderBy;
    }

    public boolean isPbx() {
        return this.pbx;
    }

    public void setPbx(boolean pbx) {
        this.pbx = pbx;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getCallnum() {
        return this.callnum;
    }

    public void setCallnum(int callnum) {
        this.callnum = callnum;
    }

    public String getAgentId() {
        return this.agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public boolean isNeedLogin() {
        return this.needLogin;
    }

    public void setNeedLogin(boolean needLogin) {
        this.needLogin = needLogin;
    }

    public Boolean getPassthrough() {
        return this.passthrough;
    }

    public void setPassthrough(Boolean passthrough) {
        this.passthrough = passthrough;
    }

    public String getDeptLsh() {
        return this.deptLsh;
    }

    public void setDeptLsh(String deptLsh) {
        this.deptLsh = deptLsh;
    }

    public Dept getDept() {
        return this.dept;
    }

    public void setDept(Dept dept) {
        this.dept = dept;
    }

    public String getCurrentTalkId() {
        return this.currentTalkId;
    }

    public void setCurrentTalkId(String currentTalkId) {
        this.currentTalkId = currentTalkId;
    }

    public List<BindPhoneTimePlan> getTimePlanList() {
        return this.timePlanList;
    }

    public void setTimePlanList(List<BindPhoneTimePlan> timePlanList) {
        this.timePlanList = timePlanList;
    }

	public boolean isPassthrough() {
		return this.passthrough == null ? true : this.passthrough;
	}

    public Boolean getRecordFlag() {
        return recordFlag;
    }

    public void setRecordFlag(Boolean recordFlag) {
        this.recordFlag = recordFlag;
    }
	
    public boolean isRecordFlag() {
        return this.recordFlag == null ? false : this.recordFlag;
    }

    public String getOrigBindPhoneNo() {
        if(origBindPhoneNo == null && StringUtils.isNotEmpty(this.bindPhoneNo)) {
            this.origBindPhoneNo = DES.getTwiceDecString(this.bindPhoneNo);
        }
        return origBindPhoneNo;
    }

    public void setOrigBindPhoneNo(String origBindPhoneNo) {
        this.origBindPhoneNo = origBindPhoneNo;
    }

    public Integer getRatio() {
        return ratio;
    }

    public void setRatio(Integer ratio) {
        this.ratio = ratio;
    }

    public Integer getPhoneLevel() {
        return Objects.isNull(this.phoneLevel)?0:this.phoneLevel;
    }

    public void setPhoneLevel(Integer phoneLevel) {
        this.phoneLevel = phoneLevel;
    }

    public int getPhoneHashCode() {
		int hash = 7;
		hash = 29 * hash + Objects.hashCode(this.customerNo);
		hash = 29 * hash + Objects.hashCode(this.lsh);
		hash = 29 * hash + Objects.hashCode(this.bindPhoneNo);
		hash = 29 * hash + Objects.hashCode(this.reportNum);
		hash = 29 * hash + Objects.hashCode(this.timeModle);
		hash = 29 * hash + Objects.hashCode(this.areaModle);
		hash = 29 * hash + Objects.hashCode(this.bindNum);
		hash = 29 * hash + Objects.hashCode(this.createTime);
		hash = 29 * hash + Objects.hashCode(this.callModle);
		hash = 29 * hash + Objects.hashCode(this.waitTime);
		hash = 29 * hash + Objects.hashCode(this.orderBy);
		hash = 29 * hash + (this.pbx ? 1 : 0);
		hash = 29 * hash + Objects.hashCode(this.agentId);
		hash = 29 * hash + Objects.hashCode(this.deptLsh);
        hash = 29 * hash + Objects.hashCode(this.recordFlag);
		return hash;
	}
	
    @Override
    public String toString() {
        return "BindPhone [customerNo=" + this.customerNo + ", bindPhoneNo=" + this.getOrigBindPhoneNo() + ", deptLsh=" + this.deptLsh 
				+ ", gh=" + this.reportNum + ", status=" + this.status +", recordFlag=" + this.recordFlag + ", callnum=" + this.callnum
                + ", orderBy=" + this.orderBy + ", ratio=" + this.ratio + ", phoneLevel=" + this.phoneLevel + "]";
    }
	
	private String getTimeDetails() {
		return this.getTimePlanList().stream().filter(timePlan -> timePlan.isChecked()).map(timePlan -> {
			return "TimeTitle:" + timePlan.getTimeTitle() + ",开始时间：" + timePlan.getStartTime() + ",结束时间：" + timePlan.getEndTime();
		}).collect(Collectors.joining(","));
	}

	public String getDetails() {
		StringBuilder sb = new StringBuilder();
		sb.append("绑定号码：").append(this.getOrigBindPhoneNo()).append("，状态：").append(this.getStatus()).append("，地区策略：").append(this.getAreaModle())
				.append(",时间策略：[").append(getTimeDetails()).append("]");
		return sb.toString();
	}
    
    public int getRatioIntValue() {
        return this.ratio == null ? 0 : this.ratio;
    }
    
    public Double getWeightValue() {
        return this.getRatioIntValue() == 0 ? 0 : this.callnum / (this.getRatioIntValue() / 100d);
    }
    
    @Transient
    private String randomChar;
    
    public String getRandomChar() {
        if(this.randomChar == null) {
            this.randomChar = RandomStringUtils.randomAlphabetic(1);
        }
        return this.randomChar;
    }
}
