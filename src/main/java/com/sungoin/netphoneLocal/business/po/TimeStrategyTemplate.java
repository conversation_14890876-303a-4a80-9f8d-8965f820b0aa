package com.sungoin.netphoneLocal.business.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.apache.commons.lang3.RandomStringUtils;

@Entity
@Table(name = "TIME_STRATEGY_TEMPLATE")
public class TimeStrategyTemplate {

	@Id
	@Column(name="ID")
	private String id;
	
	@Column(name="NUMBER_CODE")
	private String numberCode;
	
	@Column(name="TEMPLATE_ID")
	private String templateId;

    /** 时间日期数. */
    @Column(name = "TIME_TITLE")
    private Integer timeTitle;

    /** 开始时间. */
    @Column(name = "START_TIME")
    private String startTime;

    /** 结束时间. */
    @Column(name = "END_TIME")
    private String endTime;

    /** 是否选择. */
    @Column(name = "switch")
    private boolean checked;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getNumberCode() {
		return numberCode;
	}

	public void setNumberCode(String numberCode) {
		this.numberCode = numberCode;
	}

	public String getTemplateId() {
		return templateId;
	}

	public void setTemplateId(String templateId) {
		this.templateId = templateId;
	}

	public Integer getTimeTitle() {
		return timeTitle;
	}

	public void setTimeTitle(Integer timeTitle) {
		this.timeTitle = timeTitle;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public boolean isChecked() {
		return checked;
	}

	public void setChecked(boolean checked) {
		this.checked = checked;
	}

    @Override
    public String toString() {
        return "TimeStrategyTemplate{" + "id=" + id + ", numberCode=" + numberCode + ", templateId=" + templateId + ", timeTitle=" + timeTitle + ", startTime=" + startTime + ", endTime=" + endTime + ", checked=" + checked + '}';
    }
    
    public int getOrderInt() {
        if(this.timeTitle == null) {
            return 0;
        }
        switch (this.timeTitle) {
            case 9:
                return 2;
            case 10:
                return 1;
            default:
                return 0;
        }
    }
    @Transient
    private String randomChar;
    
    public String getRandomChar() {
        if(this.randomChar == null) {
            this.randomChar = RandomStringUtils.randomAlphabetic(1);
        }
        return this.randomChar;
    }
}
