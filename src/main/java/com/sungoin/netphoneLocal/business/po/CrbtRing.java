package com.sungoin.netphoneLocal.business.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

import com.sungoin.netphoneLocal.business.bean.BusinessConstants;
import javax.persistence.ConstraintMode;
import javax.persistence.ForeignKey;
import javax.persistence.Transient;
import org.apache.commons.lang3.RandomStringUtils;

/**
 * 彩铃.
 */
@Entity
@Table(name = "T_BASE_CRBT_RING", indexes = { @Index(columnList = "customer_no") })
public class CrbtRing extends IDComparator {

    private static final long serialVersionUID = -5016561144895141672L;

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    private String id;

    /** 炫铃Id. */
    @Column(name = "CRBT_ID")
    private String crbtId;

    /** 400号码. */
    @Column(name = "CUSTOMER_NO")
    private String customerNo;

    /** 星期 . */
    @Column(name = "WEEK_TITLE")
    private Integer weekTitle;

    /** 炫铃开始时间. */
    @Column(name = "STARTTIME")
    private String startTime;

    /** 炫铃结束时间. */
    @Column(name = "ENDTIME")
    private String endTime;

    /** 是否进留言标识 1为是 0为否. */
    @Column(name = "TO_VOICE")
    private boolean toVoice;
    
    /** 炫铃结束挂机标识 2:挂机. */
    @Column(name = "TO_ONHOOK")
    private String toOnHook;


    /** 对应彩铃. */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "COLOR_RING_ID", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private ColorRing colorRing;

    /**
     * 彩铃流水号
     * 如果colorRing为空，且colorRingLsh不为空
     * 查询一次colorRing，查到更新colorRing否则
     * 清空colorRingLsh（数据同步时可能会先插入部门再插入彩铃）
     * .
     */
    @Column(name = "COLOR_RING_LSH")
    private String colorRingLsh;

    /** 部门流水号. */
    @Column(name = "DEPT_LSH")
    private String deptLsh;

    /** 对应部门. */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "DEPT_ID", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Dept dept;
    
    /**
     * 时间策略模板ID
     */
    @Column(name = "TEMPLATE_ID")
    private String templateId;
    
    @Column(name = "VOICE_RING_LSH")
    private String voiceRingLsh;

	@Override
    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCrbtId() {
        return this.crbtId;
    }

    public void setCrbtId(String crbtId) {
        this.crbtId = crbtId;
    }

    public String getCustomerNo() {
        return this.customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public Integer getWeekTitle() {
        return this.weekTitle;
    }

    public void setWeekTitle(Integer weekTitle) {
        this.weekTitle = weekTitle;
    }

    public String getStartTime() {
        return this.startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return this.endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public boolean isToVoice() {
        return this.toVoice;
    }

    public void setToVoice(boolean toVoice) {
        this.toVoice = toVoice;
    }

    public String getColorRingLsh() {
        return this.colorRingLsh;
    }

    public void setColorRingLsh(String colorRingLsh) {
        this.colorRingLsh = colorRingLsh;
    }

    public ColorRing getColorRing() {
        return this.colorRing;
    }

    public void setColorRing(ColorRing colorRing) {
        this.colorRing = colorRing;
    }

    public String getDeptLsh() {
        return this.deptLsh;
    }

    public void setDeptLsh(String deptLsh) {
        this.deptLsh = deptLsh;
    }

    public Dept getDept() {
        return this.dept;
    }

    public void setDept(Dept dept) {
        this.dept = dept;
    }

    public String getToOnHook() {
		return toOnHook;
	}

	public void setToOnHook(String toOnHook) {
		this.toOnHook = toOnHook;
	}
	
	public boolean isOnHook(){
		return BusinessConstants.TO_ONHOOK.equals(this.toOnHook);
	}

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getVoiceRingLsh() {
        return voiceRingLsh;
    }

    public void setVoiceRingLsh(String voiceRingLsh) {
        this.voiceRingLsh = voiceRingLsh;
    }

	@Override
    public String toString() {
        return "CrbtRing [id=" + this.id + ", crbtId=" + this.crbtId
            + ", customerNo=" + this.customerNo + ", weekTitle="
            + this.weekTitle + ", startTime=" + this.startTime + ", endTime="
            + this.endTime + ", toVoice=" + this.toVoice + ", toOnHook=" + this.toOnHook + ", colorRingLsh="
            + this.colorRingLsh + ", deptLsh=" + this.deptLsh + "]";
    }

    public int getOrderInt() {
        if(this.weekTitle == null) {
            return 0;
        }
        switch (this.weekTitle) {
            case 9:
                return 2;
            case 10:
                return 1;
            default:
                return 0;
        }
    }
    @Transient
    private String randomChar;
    
    public String getRandomChar() {
        if(this.randomChar == null) {
            this.randomChar = RandomStringUtils.randomAlphabetic(1);
        }
        return this.randomChar;
    }
}
