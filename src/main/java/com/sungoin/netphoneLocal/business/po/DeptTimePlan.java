package com.sungoin.netphoneLocal.business.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;

import org.hibernate.annotations.GenericGenerator;

/**
 * 部门时间策略.
 */
@Entity
@Table(name = "T_BASE_DEPT_TIME_PLAN", indexes = { @Index(columnList = "customer_no,dept_lsh") })
public class DeptTimePlan extends IDComparator {

    private static final long serialVersionUID = -3188560146292756670L;

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    private String id;

    /** 400号码. */
    @Column(name = "CUSTOMER_NO")
    private String customerNo;

    /** 部门流水号. */
    @Column(name = "DEPT_LSH")
    private String deptLsh;

    /** 时间日期数. */
    @Column(name = "TIME_TITLE")
    private Integer timeTitle;

    /** 开始时间. */
    @Column(name = "START_TIME")
    private String startTime;

    /** 结束时间. */
    @Column(name = "END_TIME")
    private String endTime;

    /** 是否选择. */
    @Column(name = "switch")
    private boolean checked;

	@Override
    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCustomerNo() {
        return this.customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getDeptLsh() {
        return this.deptLsh;
    }

    public void setDeptLsh(String deptLsh) {
        this.deptLsh = deptLsh;
    }

    public Integer getTimeTitle() {
        return this.timeTitle;
    }

    public String getStartTime() {
        return this.startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public void setTimeTitle(Integer timeTitle) {
        this.timeTitle = timeTitle;
    }

    public String getEndTime() {
        return this.endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public boolean isChecked() {
        return this.checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    @Override
    public String toString() {
        return "DeptTimePlan [id=" + this.id + ", customerNo="
            + this.customerNo + ", deptLsh=" + this.deptLsh + "]";
    }
}
