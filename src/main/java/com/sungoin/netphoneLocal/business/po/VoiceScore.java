package com.sungoin.netphoneLocal.business.po;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.GenericGenerator;

/**
 * 满意度.
 */
@Entity
@Table(name = "T_BUSINESS_VOICE_SCORE",indexes = { @Index(columnList = "TALK_NOTE_ID")})
public class VoiceScore implements Serializable {

    private static final long serialVersionUID = 841331480222773486L;

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    private String id;

    /** 400号码. */
    @Column(name = "CUSTOMER_NO")
    private String customerNo;

    /** 主叫号码. */
    @Column(name = "CALLER_NO")
    private String callerNo;

    /** 被叫号码. */
    @Column(name = "CALLEE_NO")
    private String calleeNo;

    /** 满意度调查结果;0.未评价,1.非常满意,2.满意,3一般,4.不满意,默认0. */
    @Column(name = "RESULT")
    private int result;

    /** 工号. */
    @Column(name = "GH")
    private String gh;

    /** 坐席Id. */
    @Column(name = "agent_Id")
    private String agentId;

    /** 开始时间. */
    @Column(name = "START_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date startTime;

    /** 结束时间. */
    @Column(name = "END_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date endTime;

    /** 结束标志：0正常结束 1非正常结束. */
    @Column(name = "END_FLAG")
    private boolean endFlag;

    /** 对应通话记录. */
    @Column(name = "TALK_NOTE_ID")
    private String talkNoteId;

    public VoiceScore() {
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCustomerNo() {
        return this.customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getCallerNo() {
        return this.callerNo;
    }

    public void setCallerNo(String callerNo) {
        this.callerNo = callerNo;
    }

    public String getCalleeNo() {
        return this.calleeNo;
    }

    public void setCalleeNo(String calleeNo) {
        this.calleeNo = calleeNo;
    }

    public int getResult() {
        return this.result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public String getGh() {
        return this.gh;
    }

    public void setGh(String gh) {
        this.gh = gh;
    }

    public String getAgentId() {
        return this.agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public Date getStartTime() {
        return this.startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return this.endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public boolean isEndFlag() {
        return this.endFlag;
    }

    public void setEndFlag(boolean endFlag) {
        this.endFlag = endFlag;
    }

    public String getTalkNoteId() {
        return this.talkNoteId;
    }

    public void setTalkNoteId(String talkNoteId) {
        this.talkNoteId = talkNoteId;
    }

}
