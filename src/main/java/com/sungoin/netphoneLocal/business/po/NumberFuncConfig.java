/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.po;

import java.util.regex.Pattern;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.Table;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;

/**
 *
 * <AUTHOR>
 */
@Entity
@DynamicUpdate
@Table(name = "NUMBER_FUNCTION_CONFIGURE",indexes = { @Index(columnList = "NUMBER_CODE") })
public class NumberFuncConfig extends IDComparator {
    
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    private String id;
    @Column(name = "NUMBER_CODE")
    private String numberCode;
	
    /** 导航不按键自动转接开关 0：关闭，1：开启. */
    @Column(name = "IVR_TRANSFER_FLAG")
    private String ivrTransferFlag;
    
    /** 导航不按键自动转接按键. */
    @Column(name = "INPUT_KEY")
    private String inputKey;
    
    /** 报工号不转接开关 0：关闭，1：开启.  */
    @Column(name = "GH_TRANSFER_FLAG")
    private String ghTransferFlag;
    
    /** 超时等待音播放类型 0:播放等待人数，1:不播放等待人数. */
    @Column(name = "QUEUE_ALERT_TYPE")
    private String queueAlertType;
    
    /** 接通后按键转接开关 0:开启，1:关闭. */
    @Column(name = "INPUT_TRANSFER_FLAG")
    private String inputTransferFlag;
    
    /** 按键收码超时时长（秒）. */
    @Column(name = "INPUT_TIME_OUT")
    private Integer inputTimeOut;
    
    /** 按键收码超时自动转接按键. */
    @Column(name = "INPUT_TIME_OUT_DTMF")
    private Integer inputTimeOutDtmf;
    
    /** 通话最大时长（秒）. */
    @Column(name = "CONNET_TIME")
    private Integer connetTime;
    
    /** 队列提示音最长播放时长（分钟）. */
    @Column(name = "QUEUE_PLAY_TIME")
    private Integer queuePlayTime;
    
    /** 允许呼入的短号. */
    @Column(name = "ACCESS_SHORT_NUM")
    private String accessShortNum;
    
    /** 短号外显号码. */
    @Column(name = "SHORT_SHOW_NUM")
    private String shortShowNum;
    
    /** 接口客户  0：关闭，1：开启. */
    @Column(name = "INTERACT_FLAG")
    private String interactFlag;
    
    /** 国际号码呼入开关 0：关闭，1：开启.  */
    @Column(name = "INTERNATIONAL_FLAG")
    private String internationalFlag;

    /** 录音声道配置 0：单声道，1：双声道. */
    @Column(name = "RECORD_CHANNEL_FLAG")
    private String recordChannelFlag ;
    
    /** 转接提示音标识 */
    @Column(name = "TRANSFER_TIP_FLAG")
    private String transferTipFlag ;
    
    @Override
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNumberCode() {
        return numberCode;
    }

    public void setNumberCode(String numberCode) {
        this.numberCode = numberCode;
    }

    public String getIvrTransferFlag() {
        return ivrTransferFlag;
    }

    public void setIvrTransferFlag(String ivrTransferFlag) {
        this.ivrTransferFlag = ivrTransferFlag;
    }

    public String getInputKey() {
        return inputKey;
    }

    public void setInputKey(String inputKey) {
        this.inputKey = inputKey;
    }

    public String getGhTransferFlag() {
        return ghTransferFlag;
    }

    public void setGhTransferFlag(String ghTransferFlag) {
        this.ghTransferFlag = ghTransferFlag;
    }

    public String getQueueAlertType() {
        return queueAlertType;
    }

    public void setQueueAlertType(String queueAlertType) {
        this.queueAlertType = queueAlertType;
    }

    public String getInputTransferFlag() {
        return inputTransferFlag;
    }

    public void setInputTransferFlag(String inputTransferFlag) {
        this.inputTransferFlag = inputTransferFlag;
    }

    public Integer getInputTimeOut() {
        return inputTimeOut;
    }

    public void setInputTimeOut(Integer inputTimeOut) {
        this.inputTimeOut = inputTimeOut;
    }

    public Integer getInputTimeOutDtmf() {
        return inputTimeOutDtmf;
    }

    public void setInputTimeOutDtmf(Integer inputTimeOutDtmf) {
        this.inputTimeOutDtmf = inputTimeOutDtmf;
    }

    public Integer getConnetTime() {
        return connetTime;
    }

    public void setConnetTime(Integer connetTime) {
        this.connetTime = connetTime;
    }

    public Integer getQueuePlayTime() {
        return queuePlayTime;
    }

    public void setQueuePlayTime(Integer queuePlayTime) {
        this.queuePlayTime = queuePlayTime;
    }
    
    public boolean isIvrTransferFlag() {
        return "1".equals(this.ivrTransferFlag);
    }
    
    public boolean isGhTransferFlag() {
        return "1".equals(this.ghTransferFlag);
    }
    
    public boolean isInputTransferFlag() {
        return "1".equals(this.inputTransferFlag);
    }
    
    public boolean isNotPlayWaitNum() {
        return "1".equals(this.queueAlertType);
    }

    public String getAccessShortNum() {
        return accessShortNum;
    }

    public void setAccessShortNum(String accessShortNum) {
        this.accessShortNum = accessShortNum;
    }
    
    public boolean containsShortNum(String number) {
        if(StringUtils.isEmpty(this.accessShortNum)) {
            return false;
        }
        String[] numArray = this.accessShortNum.split(",");
        boolean access = false;
        for(String s : numArray) {
            if(s.startsWith("*")) {
                String realNum = s.substring(1);
                if(realNum.equals(number)) {
                    access = true;
                    break;
                } else {
                    Pattern p = Pattern.compile("^[0][0-9]{2,3}" + realNum);
                    if(p.matcher(number).matches()) {
                        access = true;
                        break;
                    }
                }
            } else if(s.equals(number)) {
                access = true;
                break;
            } else if(number.length()==10 && number.startsWith("400") &&  s.equals("400")) {
                access = true;
                break;
            }
        }
        return access;
    }

    public String getShortShowNum() {
        return shortShowNum;
    }

    public void setShortShowNum(String shortShowNum) {
        this.shortShowNum = shortShowNum;
    }

    public String getInteractFlag() {
        return interactFlag;
    }

    public void setInteractFlag(String interactFlag) {
        this.interactFlag = interactFlag;
    }

    public String getInternationalFlag() {
        return internationalFlag;
    }

    public void setInternationalFlag(String internationalFlag) {
        this.internationalFlag = internationalFlag;
    }
    
    public String getRecordChannelFlag() {
		return recordChannelFlag;
	}

	public void setRecordChannelFlag(String recordChannelFlag) {
		this.recordChannelFlag = recordChannelFlag;
	}
    
    public boolean isInteractFlag() {
        return "1".equals(this.interactFlag);
    }
    
    public boolean isInternationFlag() {
        return "1".equals(this.internationalFlag);
    }

    public boolean isRecordChannelFlag() {
        return "1".equals(this.recordChannelFlag);
    }

    public String getTransferTipFlag() {
        return transferTipFlag;
    }

    public void setTransferTipFlag(String transferTipFlag) {
        this.transferTipFlag = transferTipFlag;
    }
    
    public boolean isTransferTipFlag() {
        return "1".equals(this.transferTipFlag);
    }
}
