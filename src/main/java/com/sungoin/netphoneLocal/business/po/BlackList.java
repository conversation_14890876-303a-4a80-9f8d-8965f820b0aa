package com.sungoin.netphoneLocal.business.po;

import com.sungoin.netphoneLocal.util.MyStringUtil;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.GenericGenerator;

/**
 * 黑名单.
 */
@Entity
@Table(name = "T_BASE_BLACK_LIST", indexes = { @Index(columnList = "customer_no") })
public class BlackList extends IDComparator {

    private static final long serialVersionUID = 4100717509823708471L;
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid2")
    @GeneratedValue(generator = "uuid")
    private String id;

    /** 所属400. */
    @Column(name = "CUSTOMER_NO")
    private String customerNo;

    /** 设置的白名单，支持区号. */
    @Column(name = "USER_NO")
    private String userNo;

    /** 号码归属地. */
    @Column(name = "DISTRICT")
    private String district;

    /** 添加日期. */
    @Column(name = "ADD_DATE")
    @Temporal(TemporalType.TIMESTAMP)
    private Date addDate;

    /** 备注. */
    @Column(name = "REMARK")
    private String remark;

    /** 序号. */
    @Column(name = "XH")
    private Long xh;
    
    /** 分机号码（导航按键）. */
    @Column(name = "EXT")
    private String ext;

    /** 添加人. */
    @Column(name = "ADD_USER")
    private String addUser;

    /** 失效日期. */
    @Column(name = "EXPIRE_DATE")
    private Date expireDate;

    /** 所属用户. */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "USER_ID", foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private User user;

    public BlackList() {
    }

    @Override
    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCustomerNo() {
        return this.customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getUserNo() {
        return this.userNo;
    }

    public void setUserNo(String userNo) {
        this.userNo = userNo;
    }

    public String getDistrict() {
        return this.district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public Date getAddDate() {
        return this.addDate;
    }

    public void setAddDate(Date addDate) {
        this.addDate = addDate;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getXh() {
        return this.xh;
    }

    public void setXh(Long xh) {
        this.xh = xh;
    }

    public String getAddUser() {
        return this.addUser;
    }

    public void setAddUser(String addUser) {
        this.addUser = addUser;
    }

    public User getUser() {
        return this.user;
    }

    public void setUser(User user) {
        this.user = user;
    }
    
    public String getExt() {
		return ext;
	}

	public void setExt(String ext) {
		this.ext = ext;
	}

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public boolean isNoInBlackList(String no) {
		//手机规则
		if(MyStringUtil.isMobile(this.getUserNo())) {
			return no.contains(this.getUserNo());
		}
		//原有规则
        return no.startsWith(this.getUserNo()) || (this.getUserNo().startsWith("0") ? no.startsWith(this
            .getUserNo().substring(1)) : no.startsWith("0" + this.getUserNo()));
    }

    @Override
    public String toString() {
        return "BlackList [id=" + this.id + ", customerNo=" + this.customerNo
            + ", userNo=" + this.userNo + ", district=" + this.district
            + ", addDate=" + this.addDate + ", remark=" + this.remark + ", xh="
            + this.xh + ", addUser=" + this.addUser + "]";
    }
}
