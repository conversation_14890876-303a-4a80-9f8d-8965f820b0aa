/*
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.sungoin.netphoneLocal.business.po.DeptTimePlan;

/**
 * <AUTHOR>
 */
public interface DeptTimePlanRepository extends
    CrudRepository<DeptTimePlan, String> {

    public List<DeptTimePlan> findByCustomerNoAndDeptLsh(String customerNo, String deptLsh);

    public List<DeptTimePlan> findByCustomerNoAndDeptLshAndChecked(String customerNo, String deptLsh, boolean checked);

    @Query("delete from DeptTimePlan d where d.customerNo = ?1 and d.deptLsh= ?2")
    @Modifying
    public void deleteDeptTimePlan(String customerNo, String deptLsh);

}
