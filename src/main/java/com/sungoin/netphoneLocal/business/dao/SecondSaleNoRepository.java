/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Interface.java to edit this template
 */
package com.sungoin.netphoneLocal.business.dao;

import com.sungoin.netphoneLocal.business.po.SecondSaleNo;
import java.util.List;
import org.springframework.data.repository.CrudRepository;

/**
 *
 * <AUTHOR>
 */
public interface SecondSaleNoRepository extends CrudRepository<SecondSaleNo, Long> {

    public List<SecondSaleNo> findByOriginalNo(String originalNo);
}
