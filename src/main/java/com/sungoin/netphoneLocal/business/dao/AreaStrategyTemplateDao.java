package com.sungoin.netphoneLocal.business.dao;

import org.springframework.data.repository.CrudRepository;

import com.sungoin.netphoneLocal.business.po.AreaStrategyTemplate;
import java.util.List;

public interface AreaStrategyTemplateDao extends CrudRepository<AreaStrategyTemplate, String> {

    public List<AreaStrategyTemplate> findByTemplateId(String templateId);
    
    public List<AreaStrategyTemplate> findByTemplateIdIn(List<String> list);
}
