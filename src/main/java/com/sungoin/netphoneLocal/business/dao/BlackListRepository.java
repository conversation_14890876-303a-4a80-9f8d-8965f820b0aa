/*
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.sungoin.netphoneLocal.business.po.BlackList;
import com.sungoin.netphoneLocal.business.po.User;

/**
 * <AUTHOR>
 */
public interface BlackListRepository extends CrudRepository<BlackList, String> {

    @Query("update BlackList d set d.user = null where d.user = ?1")
    @Modifying
    public void removeUser(User user);

    @Query("update BlackList d set d.user = ?1 where d.customerNo = ?2 and d.user is null")
    @Modifying
    public void attachUser(User user, String number);

    @Query("delete from BlackList d where d.customerNo = ?1")
    @Modifying
    public void deleteBlackList(String customerNo);

    public List<BlackList> findBlackListByCustomerNo(String customerNo);
    
    public List<BlackList> findBlackListByCustomerNoAndExt(String customerNo, String ext);

}
