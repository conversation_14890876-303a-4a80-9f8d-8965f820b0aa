/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.dao;

import java.util.List;

import org.springframework.data.repository.CrudRepository;

import com.sungoin.netphoneLocal.business.po.ColorRing;

/**
 * <AUTHOR>
 */
public interface ColorRingRepository extends CrudRepository<ColorRing, String> {

    ColorRing findByCustomerNoAndLsh(String customerNo, String lsh);

    List<ColorRing> findAllByCustomerNoAndLsh(String customerNo, String lsh);

    List<ColorRing> findColorRingByCustomerNoAndCategory(
        String customerNo, int category);

}
