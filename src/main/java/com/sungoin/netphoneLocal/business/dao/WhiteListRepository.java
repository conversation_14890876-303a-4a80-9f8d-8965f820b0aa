/*
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.business.po.WhiteList;
import java.util.Date;

/**
 * <AUTHOR>
 */
public interface WhiteListRepository extends CrudRepository<WhiteList, String> {

    @Query("update WhiteList d set d.user = null where d.user = ?1")
    @Modifying
    public void removeUser(User user);

    @Query("update WhiteList d set d.user = ?1 where d.customerNo = ?2 and d.user is null")
    @Modifying
    public void attachUser(User user, String number);

    @Query("delete from WhiteList d where d.customerNo = ?1")
    @Modifying
    public void deleteWhiteList(String customerNo);

    public List<WhiteList> findWhiteListByCustomerNo(String customerNo);
    
    public List<WhiteList> findWhiteListByCustomerNoAndUserNoAndAddDateAfter(String customerNo, String userNo, Date date);
    
    public List<WhiteList> findWhiteListByCustomerNoAndXh(String customerNo, Long xh);
    
    @Query("update WhiteList d set d.callCount = d.callCount -1 where d.customerNo = ?1 and d.xh = ?2")
    @Modifying
    public void subtractCount(String customerNo, Long xh);
}
