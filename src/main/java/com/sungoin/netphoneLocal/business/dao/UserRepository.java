/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.sungoin.netphoneLocal.business.po.User;

/**
 * <AUTHOR>
 */
public interface UserRepository extends CrudRepository<User, String> {

    @Query("select u from User u")
    public List<User> getAllUser();

    public User getUserByNumber(String number);

    public User getUserByOriginalNo(String originalNo);
}
