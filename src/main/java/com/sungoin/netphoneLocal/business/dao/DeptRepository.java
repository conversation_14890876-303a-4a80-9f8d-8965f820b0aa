/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.sungoin.netphoneLocal.business.po.ColorRing;
import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.business.po.User;

/**
 * <AUTHOR>
 */
public interface DeptRepository extends CrudRepository<Dept, String> {

    public Dept findByCustomerNoAndDepth(String customerNo, int depth);

    @Query("update Dept d set d.user = null where d.user = ?1")
    @Modifying
    public void removeUser(User user);

    @Query("update Dept d set d.colorRing = null where d.colorRing = ?1")
    @Modifying
    public void removeColorRing(ColorRing ring);

    @Query("update Dept d set d.agentAlertRing = null where d.agentAlertRing = ?1")
    @Modifying
    public void removeAgentAlertRing(ColorRing ring);

    @Query("update Dept d set d.parentDept = null where d.parentDept = ?1")
    @Modifying
    public void removeParentDept(Dept parentDept);

    @Query("update Dept d set d.user = ?1 where d.customerNo = ?2 and d.user is null")
    @Modifying
    public void attachUser(User user, String number);

    @Query("update Dept d set d.parentDept = ?1 where d.customerNo = ?2 and d.parentDeptLsh =?3 ")
    @Modifying
    public void attachParentDept(Dept parentDept, String number, String parentDeptLsh);

    @Query("update Dept d set d.parentDept = ?1 where d.customerNo = ?2 and d.parentDeptLsh =?3 ")
    @Modifying
    public void attachChildrenDept(Dept dept, String number, String deptLsh);

    @Query("update Dept d set d.colorRing = ?1 where d.customerNo = ?2 and d.colorRingLsh =?3 ")
    @Modifying
    public void attachColorRing(ColorRing ring, String number, String colorRingLsh);

    @Query("update Dept d set d.agentAlertRing = ?1 where d.customerNo = ?2 and d.salesAlertToneLsh =?3 ")
    @Modifying
    public void attachAgentAlertRing(ColorRing ring, String number, String salesAlertToneLsh);

    public Dept getDeptByCustomerNoAndDeptLsh(String customerNo, String deptLsh);

    public List<Dept> findDeptByCustomerNo(String customerNo);

    @Query(value = "select count(1) from t_base_dept d where d.customer_no = ?1", nativeQuery = true)
    public Long findDeptCountByCustomerNo(String customerNo);

    @Query(value = "select count(1) from t_base_dept d where d.customer_no = ?1 and d.parent_dept_lsh =?2", nativeQuery = true)
    public Long findDeptCountByCustomerNoAndParentDeptLsh(String customerNo, String parentDeptLsh);

	@Deprecated
    public Dept findDeptByCustomerNoAndIvrKey(String customerNo, String ivrKey);
	
	public Dept findDeptByParentDeptAndIvrKey(Dept parentDept, String ivrKey);
	
    @Query("update Dept d set d.lastBindPhoneLsh = ?3 where d.customerNo = ?1 and d.deptLsh =?2 ")
    @Modifying
    public void updateLastBindPhone(String customerNo, String deptLsh, String lastBindPhoneLsh);
    
    @Query(value = "select d.* from t_base_dept d where d.customer_No = ?1 and ?2 REGEXP d.AUDIO_KEYWORDS", nativeQuery = true)
    public List<Dept> findDeptByCustomerNoAndAudioKeywords(String customerNo, String audioKeywords);
    
    public Dept findFirstByCustomerNoAndIvrKeyOrderByDepth(String customerNo, String ivrKey);
    
    public Dept findFirstByCustomerNoAndIvrKeyAndDepth(String customerNo, String ivrKey, Integer depth);
    
    public Dept findFirstByCustomerNoAndAreaCode(String customerNo, String areaCode);
}
