/*
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.sungoin.netphoneLocal.business.po.BindPhone;
import com.sungoin.netphoneLocal.business.po.BindPhoneTimePlan;

/**
 * <AUTHOR>
 */
public interface PhoneTimePlanRepository extends
    CrudRepository<BindPhoneTimePlan, String> {

    @Query("update BindPhoneTimePlan d set d.bindPhone = null where d.bindPhone = ?1")
    @Modifying
    public void removeBindPhone(BindPhone phone);

    @Query("update BindPhoneTimePlan d set d.bindPhone = ?1 where d.customerNo = ?2 and d.bindPhoneLsh = ?3")
    @Modifying
    public void attachBindPhone(BindPhone phone, String number,
        String bindPhoneLsh);

    public List<BindPhoneTimePlan> findByCustomerNoAndBindPhoneLsh(
        String customerNo, String bindPhoneLsh);

    @Query("delete from BindPhoneTimePlan d where d.bindPhone = ?1")
    @Modifying
    public void deleteBindPhoneTimePlan(BindPhone phone);

}
