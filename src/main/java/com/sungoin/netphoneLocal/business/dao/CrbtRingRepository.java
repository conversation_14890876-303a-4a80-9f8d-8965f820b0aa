/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.sungoin.netphoneLocal.business.po.ColorRing;
import com.sungoin.netphoneLocal.business.po.CrbtRing;
import com.sungoin.netphoneLocal.business.po.Dept;

/**
 * <AUTHOR>
 */
public interface CrbtRingRepository extends CrudRepository<CrbtRing, String> {

    @Query("update CrbtRing d set d.colorRing = null where d.colorRing = ?1")
    @Modifying
    public void removeColorRing(ColorRing ring);

    @Query("update CrbtRing d set d.dept = null where d.dept = ?1")
    @Modifying
    public void removeDept(Dept dept);

    @Query("update CrbtRing d set d.colorRing = ?1 where d.customerNo = ?2 and d.colorRingLsh = ?3 ")
    @Modifying
    public void attachColorRing(ColorRing ring, String number,
        String colorRingLsh);

    @Query("update CrbtRing d set d.dept = ?1 where d.customerNo = ?2 and d.deptLsh = ?3 ")
    @Modifying
    public void attachDept(Dept dept, String number, String deptLsh);

    public List<CrbtRing> findCrbtRingByCustomerNo(String customerNo);

}
