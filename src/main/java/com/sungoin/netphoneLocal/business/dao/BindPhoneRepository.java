/*
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.sungoin.netphoneLocal.business.po.BindPhone;
import com.sungoin.netphoneLocal.business.po.Dept;

/**
 * <AUTHOR>
 */
public interface BindPhoneRepository extends CrudRepository<BindPhone, String> {

    @Query("update BindPhone d set d.dept = null where d.dept = ?1")
    @Modifying
    public void removeDept(Dept dept);

    @Query("update BindPhone d set d.dept = ?1 where d.customerNo = ?2 and d.deptLsh = ?3")
    @Modifying
    public void attachDept(Dept dept, String number, String deptLsh);

    @Query("update BindPhone d set d.status = ?1 where d.customerNo = ?2 and lsh=?3")
    @Modifying
    public void modifyAgentStatus(String status, String customerNo, String lsh);

    @Query("update BindPhone d set d.status = ?1 where d.agentId=?2")
    @Modifying
    public int modifyAgentStatus(String status, String agentId);
    
    @Query("update BindPhone d set d.status = ?1 where d.agentId=?2 and d.status = ?3")
    @Modifying
    public int modifyAgentStatusByOldStatus(String status, String agentId, String oldStatus);
    
    @Query("update BindPhone d set d.status = ?1 where d.bindPhoneNo = ?2 and d.customerNo in (select u.number from User u where u.uniqueName = ?3)")
    @Modifying
    public int modifyAgentStatusByUniqueName(String status, String bindPhone, String uniqueName);
    
    @Query("update BindPhone d set d.status = ?1 where d.bindPhoneNo = ?2 and d.status = ?3 and d.customerNo in (select u.number from User u where u.uniqueName = ?4)")
    @Modifying
    public int modifyAgentStatusByOldStatusAndUniqueName(String status, String bindPhone, String oldStatus, String uniqueName);

    @Query("update BindPhone d set d.callnum = callnum + 1 where d.id = ?1 ")
    @Modifying
    public void modifyAgentCallNum(String bindPhoneId);

    @Query("update BindPhone d set d.status = ?1 where d.status = ?2 ")
    @Modifying
    public void resetAgentStatus(String status, String currentStatus);
    
    @Query("update BindPhone d set d.status = ?1 where d.status = ?2 or d.status like 'order_%'")
    @Modifying
    public void resetAgentStatusWithOrder(String status, String currentStatus);

    public List<BindPhone> findBindPhoneByDept(Dept dept);

    public List<BindPhone> findBindPhoneByCustomerNo(String customerNo);

    @Query("select o from  BindPhone o where o.dept=?1 and o.status=?2 order by o.orderBy asc ")
    public List<BindPhone> findBindPhoneByDeptAndStatus(Dept dept, String status);

    public BindPhone getBindPhoneByCustomerNoAndLsh(String customerNo, String lsh);

    public BindPhone getBindPhoneByCustomerNoAndAgentIdAndLsh(String customerNo, String agentId, String lsh);

    @Query("update BindPhone d set d.status = ?1 , d.needLogin = ?2 where agentId=?3 and d.status not in ('calling_true', 'calling_false', 'connect')")
    @Modifying
    public int modifyAgentStatusAndNeedlogin(String status, boolean isNeedlogin, String agentId);
    
    @Query("update BindPhone d set d.status = ?1 , d.needLogin = ?2 where d.bindPhoneNo=?3  and d.status not in ('calling_true', 'calling_false', 'connect') and d.customerNo in (select u.number from User u where u.uniqueName = ?4)")
    @Modifying
    public int modifyBatchAgentStatusAndNeedlogin(String status, boolean isNeedlogin, String bindPhone, String uniqueName);

    @Query("update BindPhone d set d.needLogin = ?1 where agentId=?2 ")
    @Modifying
    public void modifyAgentNeedlogin(boolean isNeedlogin, String agentId);
    
    @Query("update BindPhone d set d.needLogin = ?1 where d.bindPhoneNo=?2 and d.customerNo in (select u.number from User u where u.uniqueName = ?3)")
    @Modifying
    public int modifyBatchAgentNeedlogin(boolean isNeedlogin, String bindPhone, String uniqueName);

    @Query("select distinct o.agentId, o.status, o.needLogin from  BindPhone o where o.customerNo=?1 and agentId is not null ")
    public List<Object[]> getStatusByCustomerNo(String customerNo);

    public List<BindPhone> findBindPhoneByCustomerNoAndReportNumAndStatus(String customerNo, String reportNum,
        String status);

    @Query("update BindPhone d set d.callnum = 0 where d.customerNo = ?1 ")
    @Modifying
    public void cleanAgentCallNumByCustomerNo(String customerNo);

    @Query("update BindPhone d set d.callnum = 0 where d.dept = ?1 ")
    @Modifying
    public void cleanAgentCallNumByDeptId(Dept dept);

    public List<BindPhone> getBindPhoneByCustomerNoAndAgentId(String customerNo, String agentId);

    public List<BindPhone> getBindPhoneByCustomerNoAndAgentIdAndDeptLsh(String customerNo, String agentId, String deptLsh);
	
	@Query("select status from BindPhone o where o.agentId = ?1")
	public List<Object> getAgentStatusByAgentId(String agentId);
	
	@Query("select o.recordFlag from BindPhone o where o.customerNo = ?1 and o.lsh=?2")
	public List<Object> getRecordFlag(String customerNo, String lsh);
	
    @Query("select count(1) from  BindPhone o where o.dept.id=?1 and o.status=?2 ")
    public Integer findIdleAgentCountByDeptIdAndStatus(String deptId, String status);
    
    @Query("select o from BindPhone o where o.bindPhoneNo = ?1 and o.customerNo in (select u.number from User u where u.uniqueName = ?2 and u.number <> ?3)")
    public List<BindPhone> findByUniqueName(String bindPhone, String uniqueName, String excludeNo);
    
    @Query("select o from BindPhone o where o.bindPhoneNo = ?1 and o.customerNo in (select u.number from User u where u.uniqueName = ?2)")
    public List<BindPhone> findByUniqueName(String bindPhone, String uniqueName);
    
    @Query("select o from BindPhone o where length(o.bindPhoneNo) < 20")
    public List<BindPhone> findInitBindPhone();
}
