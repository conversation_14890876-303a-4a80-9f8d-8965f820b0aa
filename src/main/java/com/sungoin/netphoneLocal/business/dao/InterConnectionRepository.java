/*
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import com.sungoin.netphoneLocal.business.po.InterConnection;

/**
 * <AUTHOR>
 */
public interface InterConnectionRepository extends CrudRepository<InterConnection, String> {

	@Query("delete from InterConnection d where d.customerNo = ?1")
	@Modifying
	public void deleteInterConnection(String customerNo);
	
	@Query("select o from InterConnection o where o.customerNo = ?1 and o.useFlag = ?2 order by o.calledPhone desc, o.callerCode desc, o.callerType desc")
	public List<InterConnection> findInterConnectionByCustomerNoAndUseFlag(String customerNo, boolean useFalg);
	
}
