package com.sungoin.netphoneLocal.business.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.sungoin.netphoneLocal.util.DateTimeUtil;


public class TalkNoteDto implements Comparable<TalkNoteDto>, Serializable{
	
	private static final long serialVersionUID = 8834929758455214132L;

	private String calleeNo;
	
	private Date incomingTime;

	public String getCalleeNo() {
		return calleeNo;
	}

	public void setCalleeNo(String calleeNo) {
		this.calleeNo = calleeNo;
	}

	public Date getIncomingTime() {
		return incomingTime;
	}

	public void setIncomingTime(Date incomingTime) {
		this.incomingTime = incomingTime;
	}

	@Override
	public int compareTo(TalkNoteDto dto) {
		return dto.getIncomingTime().compareTo(this.incomingTime);
	}

	@Override
	public String toString() {
		return "{calleeNo: "+calleeNo+",incomingTime: "+(incomingTime!=null?DateTimeUtil.formatDateTime(incomingTime):"")+"}";
	}
}
