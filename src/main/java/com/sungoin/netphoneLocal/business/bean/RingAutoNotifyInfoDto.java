/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.business.bean;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RingAutoNotifyInfoDto {
    
    private String id; 
	
    private String numberCode; 
    
    private String numberId; 
	
    private String testType; 
    
    private String testId; 
    
    private Date outCallTime;
    
    private Integer result; 
    
    private Integer finishStatus; 
    
    private String caller; 
    
    private String callee;

    /**
     * 新平台标识
     */
    private Boolean plusFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNumberCode() {
        return numberCode;
    }

    public void setNumberCode(String numberCode) {
        this.numberCode = numberCode;
    }

    public String getNumberId() {
        return numberId;
    }

    public void setNumberId(String numberId) {
        this.numberId = numberId;
    }

    public String getTestType() {
        return testType;
    }

    public void setTestType(String testType) {
        this.testType = testType;
    }

    public String getTestId() {
        return testId;
    }

    public void setTestId(String testId) {
        this.testId = testId;
    }

    public Date getOutCallTime() {
        return outCallTime;
    }

    public void setOutCallTime(Date outCallTime) {
        this.outCallTime = outCallTime;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public Integer getFinishStatus() {
        return finishStatus;
    }

    public void setFinishStatus(Integer finishStatus) {
        this.finishStatus = finishStatus;
    }

    public String getCaller() {
        return caller;
    }

    public void setCaller(String caller) {
        this.caller = caller;
    }

    public String getCallee() {
        return callee;
    }

    public void setCallee(String callee) {
        this.callee = callee;
    }

    public Boolean getPlusFlag() {
        return plusFlag;
    }

    public void setPlusFlag(Boolean plusFlag) {
        this.plusFlag = plusFlag;
    }
}
