package com.sungoin.netphoneLocal.business.bean.baidu;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class BaiduResult {
    
    public static final String IVR_RESULT_NONE = "0";
    public static final String IVR_RESULT_HAS = "1";
    public static final String IVR_RESULT_ERROR = "-1";

	/** 状态码 0-成功，-1-异常 1-存在分机情况. */
	private String result;
	
	/** 返回400绑定的坐席号码列表*. */
	private String destNum;
	
	/** 用户自定义的字段. */
	private String userField;
	
	/** IVR语音提示内容*. */
	private String ivrPlayContent;
	
	/** 欢迎语. */
	private String welcomeContent;
	
	/** 用于在 result=1时，用户按键之后播放. */
	private String transferringIVRContent;

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}

	public String getDestNum() {
		return destNum;
	}

	public void setDestNum(String destNum) {
		this.destNum = destNum;
	}

	public String getUserField() {
		return userField;
	}

	public void setUserField(String userField) {
		this.userField = userField;
	}

	public String getIvrPlayContent() {
		return ivrPlayContent;
	}

	public void setIvrPlayContent(String ivrPlayContent) {
		this.ivrPlayContent = ivrPlayContent;
	}

	public String getWelcomeContent() {
		return welcomeContent;
	}

	public void setWelcomeContent(String welcomeContent) {
		this.welcomeContent = welcomeContent;
	}

	public String getTransferringIVRContent() {
		return transferringIVRContent;
	}

	public void setTransferringIVRContent(String transferringIVRContent) {
		this.transferringIVRContent = transferringIVRContent;
	}
	
    @Override
	public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
    
    public String getErrorMsg() {
        String msg1 = this.welcomeContent == null ? "" : this.welcomeContent;
        String msg2 = this.ivrPlayContent == null ? "" : this.ivrPlayContent;
        return msg1 + msg2;
    }
}
