package com.sungoin.netphoneLocal.business.bean;

import java.io.Serializable;

public class AgentBean implements Serializable,Comparable<AgentBean> {

    private static final long serialVersionUID = 3296368784548382402L;

    private String callerId;

    /** 排队状态. */
    private volatile int queueStatus;

    /** 排队开始时间. */
    private volatile long queueStratTime;
    
    /** 排队初始时间. */
    private final long queueInitTime;

    public AgentBean() {
        this.queueInitTime=System.currentTimeMillis();
    }

    public String getCallerId() {
        return this.callerId;
    }

    public void setCallerId(String callerId) {
        this.callerId = callerId;
    }

    public int getQueueStatus() {
        return this.queueStatus;
    }

    public void setQueueStatus(int queueStatus) {
        this.queueStatus = queueStatus;
    }

    public long getQueueStratTime() {
        return this.queueStratTime;
    }

    public void setQueueStratTime(long queueStratTime) {
        this.queueStratTime = queueStratTime;
    }

    public long getQueueInitTime() {
        return this.queueInitTime;
    }

    @Override
    public int compareTo(AgentBean o) {
        return (int) (this.getQueueInitTime()-o.getQueueInitTime());
    }

  
    
}
