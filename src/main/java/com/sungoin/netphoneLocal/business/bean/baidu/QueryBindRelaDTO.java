package com.sungoin.netphoneLocal.business.bean.baidu;

public class QueryBindRelaDTO {
	
	/** 400号码. */
	private String hotline;
	
	/** 400分机号*. */
	private String subNum;
	
	/** 供应商唯一ID - 关联话单. */
	private String uniqueId;
	
	/** 主叫号码. */
	private String customerNumber;
	
	/** MD5算法(固定值#10位时间戳)，其中固定值是VFJSVDQwMA. */
	private String authKey;
	
	/** 10位时间戳(话单回调接口对应startTime). */
	private String timestamp;

	public String getHotline() {
		return hotline;
	}

	public void setHotline(String hotline) {
		this.hotline = hotline;
	}

	public String getSubNum() {
		return subNum;
	}

	public void setSubNum(String subNum) {
		this.subNum = subNum;
	}

	public String getUniqueId() {
		return uniqueId;
	}

	public void setUniqueId(String uniqueId) {
		this.uniqueId = uniqueId;
	}

	public String getCustomerNumber() {
		return customerNumber;
	}

	public void setCustomerNumber(String customerNumber) {
		this.customerNumber = customerNumber;
	}

	public String getAuthKey() {
		return authKey;
	}

	public void setAuthKey(String authKey) {
		this.authKey = authKey;
	}

	public String getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}
}
