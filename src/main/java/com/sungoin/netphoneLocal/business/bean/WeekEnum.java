package com.sungoin.netphoneLocal.business.bean;

/**
 * 周期枚举.
 */
public enum WeekEnum {

    MONDAY(1, "星期一"), TUESDAY(2, "星期二"), WEDNESDAY(3, "星期三"), THURSDAY(4, "星期四"), FRIDAY(5, "星期五"), 
    SATURDAY(6, "星期六"), SUNDAY(7, "星期七"), CUSTOM(9, "自定义"), ALLDAY(0,"全部"), WORKDAY(8, "周一到周五"), HOLIDAY(10, "节假日"),NOTHOLIDAY(11, "非节假日");

    /** 标识值. */
    private final int code;

    /** 描述. */
    private final String desc;

    WeekEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

}
