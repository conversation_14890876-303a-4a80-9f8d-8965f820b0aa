package com.sungoin.netphoneLocal.business.bean;

public class BusinessConstants {

    public static final int ALL_DAY = 1;
    public static final int WORKING_DAY = 2;
    public static final int HOLIDAY = 3;
    public static final int CATEGORY_VOICEBOX = 15;
    public static final int CATEGORY_RING = 1;

    public static final int QUEUE_WAIT = 0;
    public static final int WAIT_CONNECT = 1;
    public static final int QUEUE_TIMEOUT = 2;

    public static final long TIME_SECONDS = 1000;

    public static final String GLOABLE_SWITCHBOARD_TYPE = "gloable";
    public static final String SEPARATE_SWITCHBOARD_TYPE = "separate";

    public static final String CALLEE_ALL = "0000";
    public static final String CALLEE_MOBILE = "0001";
    public static final String CALLEE_PHONE = "0002";

    public static final String ALL_AREA = "0000";
    public static final String CALLEE_AREA = "0001";
    public static final String CALLEE_NO_AREA = "0002";

    public static final String CALL_ALL = "0";
    public static final String CALL_MOBILE = "1";
    public static final String CALL_PHONE = "2";

    public static final int TX_MODE_DEFAULT = -1;
    public static final int TX_MODE_0 = 0;
    public static final int TX_MODE_1 = 1;
    public static final int TX_MODE_2 = 2;
    public static final int TX_MODE_3 = 3;
    public static final int TX_MODE_4 = 4;

    public static final String TX_CODE_0 = "0";
    public static final String TX_CODE_1 = "1";
    public static final String TX_CODE_2 = "2";

    public static final int CALL_TYPE_0 = 0;
    public static final int CALL_TYPE_1 = 1;
    public static final int CALL_TYPE_2 = 2;

    public static final int TRANS_CODE_0 = 0;
    public static final int TRANS_CODE_1 = 1;
    public static final int TRANS_CODE_2 = 2;
    
    public static final String GLOBAL_IONN = "000000";
    
    public static final String YYS_MODE_0 = "0";
    public static final String YYS_MODE_1 = "1";
    public static final String YYS_MODE_2 = "2";
    
    public static final String TO_ONHOOK = "2";
    
    public static long CUSTOMER_WHITE_LIST_NOT_IN = -1;
    public static long CUSTOMER_WHITE_LIST_COUNT_LIMIT = -2;
}
