package com.sungoin.netphoneLocal.business.bean;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
public class CallNumDto implements Serializable{

    private static final long serialVersionUID = 7422598758187854262L;
	
	public static final int CALL_TYPE_DEFAULT = 0;
	public static final int CALL_TYPE_DIRECT = 1;
	public static final int CALL_TYPE_TRANS = 2;
	
	public static final int TRANS_PREFIX_DEFAULT = 0;
	public static final int TRANS_PREFIX_ON = 1;
	public static final int TRANS_PREFIX_OFF = 2;

	/** 互联互通Id. */
	private String id;
	
    private String callerNo;
    
    private String calleeNo;
    
    /** 0：可选默认:1：直接呼叫:2：转移呼叫. */
    private int callType;
    
    /** 转移呼叫是否加区号，0：可选择默认:1：加区号:2：不加区号 . */
    private int transPrefix;
    
    /** 原始小号. */
    private String originalNo;
        
    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCallerNo() {
        return this.callerNo;
    }

    public void setCallerNo(String callerNo) {
        this.callerNo = callerNo;
    }

    public String getCalleeNo() {
        return this.calleeNo;
    }

    public void setCalleeNo(String calleeNo) {
        this.calleeNo = calleeNo;
    }

    public int getCallType() {
        return this.callType;
    }

    public void setCallType(int callType) {
        this.callType = callType;
    }

    public int getTransPrefix() {
        return this.transPrefix;
    }

    public void setTransPrefix(int transPrefix) {
        this.transPrefix = transPrefix;
    }
    
    public String getOriginalNo() {
        return this.originalNo;
    }

    public void setOriginalNo(String originalNo) {
        this.originalNo = originalNo;
    }

    @Override
    public String toString() {
        return "CallNumDto [id=" + this.id + ", callerNo=" + this.callerNo + ", calleeNo=" + this.calleeNo
            + ", callType=" + this.callType + ", transPrefix=" + this.transPrefix + "]";
    }
    
}
