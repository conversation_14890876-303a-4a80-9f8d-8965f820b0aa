package com.sungoin.netphoneLocal.business.bean;

/**
 * 坐席状态.
 */
public enum AgentStatusEnum {

    IDLE("idle", "空闲"), BUSY("busy", "忙碌"), CONNECT("connect", "通话中"), OFFLINE(
        "offline", "离线"), CALLING("calling", "呼叫中");

    /** 标识值. */
    private final String value;

    /** 描述. */
    private final String desc;

    AgentStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return this.value;
    }

    public String getDesc() {
        return this.desc;
    }

}
