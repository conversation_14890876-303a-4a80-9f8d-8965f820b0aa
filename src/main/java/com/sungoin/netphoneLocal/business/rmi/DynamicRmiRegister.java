/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.business.rmi;

import com.sungoin.netphoneLocal.config.CommonSettings;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.remoting.rmi.RmiProxyFactoryBean;
import org.springframework.remoting.rmi.RmiServiceExporter;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class DynamicRmiRegister {
    private static final Logger log = LoggerFactory.getLogger(DynamicRmiRegister.class);

    @Autowired
    private ConfigurableApplicationContext applicationContext;

    @Autowired
    private CommonSettings commonSetting;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public void registerRmi() {
        try {
            List<Map<String, Object>> list = jdbcTemplate.queryForList("SELECT * FROM t_base_config");
            if (!list.isEmpty()) {
                int mainServer = Integer.parseInt(list.get(0).get("main_server").toString());
                String backupUrl = (String) list.get(0).get("backup_url");
                String serviceUrl = (String) list.get(0).get("rmi_service");
                commonSetting.setMainServer(mainServer == 1);
                commonSetting.setBackupServerEndpoint(backupUrl);
                if (!commonSetting.isMainServer()) {
                    log.info("当前服务为备用机，动态生成Rmi客户端");
                    DefaultListableBeanFactory beanFactory
                            = (DefaultListableBeanFactory) applicationContext.getBeanFactory();
                    // 创建RmiProxyFactoryBean定义
                    BeanDefinitionBuilder builder = BeanDefinitionBuilder
                            .genericBeanDefinition(RmiProxyFactoryBean.class);
                    builder.addPropertyValue("serviceUrl", serviceUrl);
                    builder.addPropertyValue("serviceInterface", RmiService.class);
                    builder.addPropertyValue("refreshStubOnConnectFailure", true);
                    //移除原有的Bean定义
                    if (beanFactory.containsBeanDefinition("RmiService")) {
                        log.info("移除原有的客户端定义");
                        beanFactory.removeBeanDefinition("RmiService");
                    }
                    //注册Rmi客户端
                    beanFactory.registerBeanDefinition("RmiService", builder.getBeanDefinition());
                    log.info("Rmi 客户端注册成功：{} " , applicationContext.getBean("RmiService"));
                } else {
                    log.info("当前服务为主用机，动态生成Rmi服务端");
                    DefaultListableBeanFactory beanFactory = 
                    (DefaultListableBeanFactory) applicationContext.getBeanFactory();

                    // 创建RmiServiceExporter Bean定义
                    BeanDefinitionBuilder builder = BeanDefinitionBuilder
                        .genericBeanDefinition(RmiServiceExporter.class);

                    builder.addPropertyValue("serviceName", "RmiService");
                    builder.addPropertyValue("service", applicationContext.getBean(RmiService.class));
                    builder.addPropertyValue("serviceInterface", RmiService.class);
                    builder.addPropertyValue("registryPort", 8088);
                    //移除原有的Bean定义
                    if (beanFactory.containsBeanDefinition("serviceExporter")) {
                        log.info("移除原有的服务端定义");
                        beanFactory.removeBeanDefinition("serviceExporter");
                    }
                    // 注册Bean
                    beanFactory.registerBeanDefinition("serviceExporter", builder.getBeanDefinition());
                    log.info("Rmi 服务端注册成功：{} " , applicationContext.getBean("serviceExporter"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
