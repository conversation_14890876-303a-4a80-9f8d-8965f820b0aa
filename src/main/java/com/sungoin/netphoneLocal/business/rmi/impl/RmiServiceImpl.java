/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.sungoin.netphoneLocal.business.rmi.impl;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sungoin.netphoneLocal.business.po.BindPhone;
import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.business.po.WhiteList;
import com.sungoin.netphoneLocal.business.rmi.RmiService;
import com.sungoin.netphoneLocal.business.service.BaseService;
import com.sungoin.netphoneLocal.business.service.BusinessService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 *         2015-8-12
 */
@Service(value = "RmiService")
public class RmiServiceImpl implements RmiService {
	private static final Logger LOGGER = LoggerFactory.getLogger(RmiServiceImpl.class);
	
    @Resource
    private BaseService baseService;

    @Resource
    private BusinessService businessService;

    @Override
    public String hello() {
        return "hello world";
    }

    @Override
    public int modifyAgentStatus(String customerNo, String lsh, String agengtStatus) {
		LOGGER.debug("备用提交更新坐席状态：400号码：{}, 绑定号码流水号：{}, 坐席状态：{}", customerNo, lsh, agengtStatus);
        return this.baseService.modifyAgentStatus(customerNo, lsh, agengtStatus);
    }

    @Override
    public List<BindPhone> getIdleAgent(String customerNo, String deptLsh,
        String callerNo) {
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo,
            deptLsh);
        return this.baseService.getIdleAgent(dept, callerNo);
    }

    @Override
    public User getUserByNumber(String number) {
        return this.baseService.getUserByNumber(number);
    }

    @Override
    public BindPhone getBindPhoneByCustomerNoAndLsh(String customerNo,
        String lsh) {
        return this.baseService.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
    }

    @Override
    public String getAgentStatus(String customerNo, String lsh) {
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(
            customerNo, lsh);
        return this.baseService.getAgentStatus(phone.getId());
    }

    @Override
    public void modifyAgentCallNum(String customerNo, String lsh) {
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(
            customerNo, lsh);
        this.baseService.modifyAgentCallNum(phone.getId());
    }

    @Override
    public void createQueue(String customerNo, String deptLsh) {
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo,
            deptLsh);
        this.businessService.createQueue(dept.getId());
    }

    @Override
    public int getQueueSizeByDept(String customerNo, String deptLsh) {
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo,
            deptLsh);
        return this.businessService.getQueueSizeByDept(dept.getId());
    }

    @Override
    public int addToQueue(String customerNo, String deptLsh, String callerId) {
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo,
            deptLsh);
        return this.businessService.addToQueue(dept.getId(), callerId);
    }

    @Override
    public int removeQueue(String customerNo, String deptLsh, String callerId,
        boolean spreadState) {
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo,
            deptLsh);
        return this.businessService.removeQueue(dept.getId(), callerId,
            spreadState);
    }

    @Override
    public int continueWait(String customerNo, String deptLsh, String callerId) {
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo,
            deptLsh);
        return this.businessService.continueWait(dept.getId(), callerId);
    }

    @Override
    public void MessageAgentOnHook(String customerNo, String deptLsh) {
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo,
            deptLsh);
        this.businessService.MessageAgentOnHook(dept.getId());
    }

    @Override
    public void MessageCallerOnHook(String customerNo, String deptLsh,
        String callerId) {
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo,
            deptLsh);
        this.businessService.MessageCallerOnHook(dept.getId(), callerId);
    }

    @Override
    public int[] getQueueStatus(String customerNo, String deptLsh,
        String callerId) {
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo,
            deptLsh);
        return this.businessService.getQueueStatus(dept.getId(), callerId);
    }

    @Override
    public String getAgentStatus(String customerNo, String agentId, String lsh) {
        return this.baseService.getAgentStatus(customerNo, agentId, lsh);
    }

    @Override
    public int modifyAgentStatusAndNeedlogin(String customerNo, String lsh,
        String status, boolean isNeedlogin) {
		LOGGER.debug("备用提交更新坐席状态和登录标识：400号码：{}, 绑定号码流水号：{}, 坐席状态：{}, 登录标识：{}", customerNo, lsh, status, isNeedlogin);
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(
            customerNo, lsh);
        return this.baseService.modifyAgentStatusAndNeedlogin(phone.getId(), status,
            isNeedlogin);
    }
    
    @Override
    public void modifyAgentNeedlogin(String customerNo, String lsh, boolean isNeedlogin) {
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(
            customerNo, lsh);
        this.baseService.modifyAgentNeedlogin(phone.getId(), isNeedlogin);
    }

    @Override
    public void addConnect(String customerNo, String lsh) {
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(
            customerNo, lsh);
        this.businessService.addConnect(phone.getId());
    }

    @Override
    public int getConnectCount(String customerNo, String lsh) {
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(
            customerNo, lsh);
        return this.businessService.getConnectCount(phone.getId());
    }

    @Override
    public void subtractConnect(String customerNo, String lsh) {
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(
            customerNo, lsh);
        this.businessService.subtractConnect(phone.getId());
    }
    
    @Override
    public void subtractGloableConnect(String customerNo, String bindPhone) {
        this.businessService.subtractGloableConnect(customerNo, bindPhone);
    }

    @Override
    public BindPhone getBindPhoneByCustomerNoAndReportNum(String customerNo,
        String reportNum) {
        return this.baseService.getBindPhoneByCustomerNoAndReportNum(
            customerNo, reportNum);
    }

    @Override
    public boolean isBindPhoneEmpty(String customerNo, String deptLsh) {
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo,
            deptLsh);
        return this.baseService.isBindPhoneEmpty(dept.getId());
    }

    @Override
    public void notifyQueueAgentOnhook(String customerNo, String agentId) {
        this.businessService.notifyQueueAgentOnhook(customerNo, agentId);
    }
    
    @Override
    public void startResetAgentStatus(String currentStatus) {
        this.baseService.startResetAgentStatus(currentStatus);
    }

	@Override
	public void modifyAgentStatusByAgentId(String customerNo, String agentId, String agengtStatus) {
		LOGGER.debug("备用提交更新坐席状态：400号码：{}, 坐席ID：{}, 坐席状态：{}", customerNo, agentId, agengtStatus);
		this.baseService.modifyAgentStatusByAgentId(customerNo, agentId, agengtStatus);
	}

	@Override
	public String getAgentStatusByAgentId(String agentId) {
		return this.baseService.getAgentStatusByAgentId(agentId);
	}
	
	@Override
	public String getDeptBindphoneDetails(String customerNo, String deptLsh) {
		return this.baseService.getDeptBindphoneDetails(customerNo, deptLsh);
	}
    
    @Override
    public String getDeptLastBindPhone(String customNo, String deptLsh) {
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customNo, deptLsh);
        return dept.getLastBindPhoneLsh();
    }
    
    @Override
    public void updateDeptLastBindPhone(String customNo, String deptLsh, String bindPhoneLsh) {
        this.baseService.updateDeptLastBindPhone(customNo, deptLsh, bindPhoneLsh);
    }

    @Override
    public int getQueueIndex(String customerNo, String deptLsh, String callId) {
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo,
            deptLsh);
        return this.businessService.getQueueIndex(dept.getId(), callId);
    }

    @Override
    public void addOfflineCache(String numberCode, String deptLsh, String caller) {
        this.baseService.addOfflineCache(numberCode, deptLsh, caller);
    }
    
    @Override
    public long checkCustomerWhiteList(String numberCode, String caller) {
        return this.baseService.checkCustomerWhiteList(numberCode, caller);
    }
    
    @Override
    public WhiteList subtractWhiteListCount(String numberCode, Long xh) {
        return this.baseService.subtractWhiteListCount(numberCode, xh);
    }
    
    @Override
    public int batchModifyAgentStatus(String customerNo, String lsh, String agengtStatus, String uniqueName) {
		LOGGER.debug("备用提交批量更新坐席状态：400号码：{}, 绑定号码流水号：{}, 坐席状态：{}， uniqueName：{}", customerNo, lsh, agengtStatus, uniqueName);
        return this.baseService.batchModifyAgentStatus(customerNo, lsh, agengtStatus, uniqueName);
    }
    
    @Override
    public void modifyBatchAgentNeedlogin(String customerNo, String lsh, boolean isNeedlogin, String uniqueName) {
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(
            customerNo, lsh);
        this.baseService.modifyBatchAgentNeedlogin(phone.getId(), isNeedlogin, uniqueName);
    }

    @Override
    public int modifyBatchAgentStatusAndNeedlogin(String customerNo, String lsh, String status, boolean isNeedlogin, String uniqueName) {
        BindPhone phone = this.baseService.getBindPhoneByCustomerNoAndLsh(
            customerNo, lsh);
        return this.baseService.modifyBatchAgentStatusAndNeedlogin(phone.getId(), status, isNeedlogin, uniqueName);
    }

    @Override
    public int batchModifyAgentStatusByBindphone(String bindPhone, String agengtStatus, String uniqueName) {
        return this.baseService.batchModifyAgentStatusByBindphone(bindPhone, agengtStatus, uniqueName);
    }

    @Override
    public void updateQueueState(String customerNo, String deptLsh, String callerId, int queueState) {
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo,
            deptLsh);
        this.businessService.updateQueueState(dept.getId(), callerId, queueState);
    }

    @Override
    public boolean queueExist(String customerNo, String deptLsh, String callerId) {
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo,
            deptLsh);
        return this.businessService.queueExist(dept.getId(), callerId);
    }

    @Override
    public String getQueueInfo(String customerNo, String deptLsh) {
        Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo,
            deptLsh);
        return this.businessService.getQueueInfo(dept.getId());
    }

}
