package com.sungoin.netphoneLocal.business.mq;

import java.io.Serializable;
import java.util.Date;

/**   
 * @author: chenfe<PERSON>
 * @date:2023年6月8日 下午1:12:28      
 */
public class CallTraceDto implements Serializable {

	public String uqid;
	
	public String numberCode;
	
	public Date createTime;
	
	public String caller;
	
	public String callee;
	
	public String dist;
	
	public String route;
	
	public String remark;
    
    public String deptLsh;

    public CallTraceDto() {
    }

    public CallTraceDto(String numberCode, String caller, String callee, String dist, String route, String remark, String deptLsh) {
        this.numberCode = numberCode;
        this.caller = caller;
        this.callee = callee;
        this.dist = dist;
        this.route = route;
        this.remark = remark;
        this.deptLsh = deptLsh;
        this.createTime = new Date();
    }

	public String getUqid() {
		return uqid;
	}

	public void setUqid(String uqid) {
		this.uqid = uqid;
	}

	public String getNumberCode() {
		return numberCode;
	}

	public void setNumberCode(String numberCode) {
		this.numberCode = numberCode;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCaller() {
		return caller;
	}

	public void setCaller(String caller) {
		this.caller = caller;
	}

	public String getCallee() {
		return callee;
	}

	public void setCallee(String callee) {
		this.callee = callee;
	}

	public String getDist() {
		return dist;
	}

	public void setDist(String dist) {
		this.dist = dist;
	}

	public String getRoute() {
		return route;
	}

	public void setRoute(String route) {
		this.route = route;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

    public String getDeptLsh() {
        return deptLsh;
    }

    public void setDeptLsh(String deptLsh) {
        this.deptLsh = deptLsh;
    }
	
}
