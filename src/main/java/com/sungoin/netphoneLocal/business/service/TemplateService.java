/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.business.service;

import com.sungoin.netphoneLocal.business.dao.AreaStrategyTemplateDao;
import com.sungoin.netphoneLocal.business.dao.BindPhoneRelateTemplateDao;
import com.sungoin.netphoneLocal.business.dao.DeptRelateTemplateDao;
import com.sungoin.netphoneLocal.business.dao.SecondSaleNoRepository;
import com.sungoin.netphoneLocal.business.dao.TimeStrategyTemplateDao;
import com.sungoin.netphoneLocal.business.po.AreaStrategyTemplate;
import com.sungoin.netphoneLocal.business.po.BindPhoneRelateTemplate;
import com.sungoin.netphoneLocal.business.po.DeptRelateTemplate;
import com.sungoin.netphoneLocal.business.po.TimeStrategyTemplate;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class TemplateService {
        
    private static final String TEMPLATE_TYPE_AREA = "0";
    private static final String TEMPLATE_TYPE_TIME = "1";
    
    @Resource
    private SecondSaleNoRepository secondSaleDao;

    @Resource
    private DeptRelateTemplateDao deptRelateTemplateDao;
    
    @Resource
    private BindPhoneRelateTemplateDao bindPhoneRelateTemplateDao;
    
    @Resource
    private AreaStrategyTemplateDao areaStrategyTemplateDao;
    
    @Resource
    private TimeStrategyTemplateDao timeStrategyTemplateDao;
    
    public AreaStrategyTemplate findDeptAreaTemplate(String customerNo, String deptLsh) {
        List<DeptRelateTemplate> templates = this.deptRelateTemplateDao.findByNumberCodeAndDeptLshAndTemplateType(customerNo, deptLsh, TEMPLATE_TYPE_AREA);
        if(templates.isEmpty()) {
            return null;
        }
        DeptRelateTemplate template = templates.get(0);
        List<AreaStrategyTemplate> list = areaStrategyTemplateDao.findByTemplateId(template.getTemplateId());
        return list.isEmpty() ? null : list.get(0);
    }
    
    public List<TimeStrategyTemplate> findDeptTimeTemplate(String customerNo, String deptLsh) {
        List<DeptRelateTemplate> templates = this.deptRelateTemplateDao.findByNumberCodeAndDeptLshAndTemplateType(customerNo, deptLsh, TEMPLATE_TYPE_TIME);
        if(templates.isEmpty()) {
            return null;
        }
        List<String> templateIds = templates.stream().map(DeptRelateTemplate::getTemplateId).collect(Collectors.toList());
        return this.timeStrategyTemplateDao.findByTemplateIdIn(templateIds);
    }
    
    public AreaStrategyTemplate findBindPhoneAreaTemplate(String customerNo, String bindPhoneLsh) {
        List<BindPhoneRelateTemplate> templates = this.bindPhoneRelateTemplateDao.findByNumberCodeAndBindPhoneLshAndTemplateType(customerNo, bindPhoneLsh, TEMPLATE_TYPE_AREA);
        if(templates.isEmpty()) {
            return null;
        }
        BindPhoneRelateTemplate template = templates.get(0);
        List<AreaStrategyTemplate> list = areaStrategyTemplateDao.findByTemplateId(template.getTemplateId());
        return list.isEmpty() ? null : list.get(0);
    }
    
    public List<TimeStrategyTemplate> findBindPhoneTimeTemplate(String customerNo, String bindPhoneLsh) {
        List<BindPhoneRelateTemplate> templates = this.bindPhoneRelateTemplateDao.findByNumberCodeAndBindPhoneLshAndTemplateType(customerNo, bindPhoneLsh, TEMPLATE_TYPE_TIME);
        if(templates.isEmpty()) {
            return null;
        }
        List<String> templateIds = templates.stream().map(BindPhoneRelateTemplate::getTemplateId).collect(Collectors.toList());
        return this.timeStrategyTemplateDao.findByTemplateIdIn(templateIds);
    }
    
    public List<TimeStrategyTemplate> findCrbtTimeTemplate(List<String> templateIds) {
        return this.timeStrategyTemplateDao.findByTemplateIdIn(templateIds);
    }
}
