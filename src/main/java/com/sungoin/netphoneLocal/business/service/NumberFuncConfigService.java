/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.service;

import com.sungoin.netphoneLocal.business.dao.NumberFuncConfigRepository;
import com.sungoin.netphoneLocal.business.po.NumberFuncConfig;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class NumberFuncConfigService {
    @Resource
    private NumberFuncConfigRepository repo;
    
    public NumberFuncConfig getByNumberCode(String numberCode) {
        List<NumberFuncConfig> list = repo.findByNumberCode(numberCode);
        return list.isEmpty() ? null : list.get(0);
    }
}
