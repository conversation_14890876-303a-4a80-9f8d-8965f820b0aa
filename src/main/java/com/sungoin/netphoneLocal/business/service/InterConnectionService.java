/*
 * 
 */
package com.sungoin.netphoneLocal.business.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.sungoin.netphoneLocal.business.bean.BusinessConstants;
import com.sungoin.netphoneLocal.business.bean.CallNumDto;
import com.sungoin.netphoneLocal.business.dao.InterConnectionRepository;
import com.sungoin.netphoneLocal.business.dao.MobileLocationRepository;
import com.sungoin.netphoneLocal.business.dao.UserRepository;
import com.sungoin.netphoneLocal.business.po.InterConnection;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.config.CommonSettings;

/**
 * <AUTHOR>
 */
@Service
public class InterConnectionService {
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(InterConnectionService.class);

    @Autowired
    private InterConnectionRepository interConnDao;

    @Resource
    CommonSettings commonSettings;

    @Resource
    private MobileLocationRepository mobileLocationDao;

    @Resource
    private UserRepository userDao;

    @Autowired
    private BaseService baseService;

    private static final Map<String, String> map = new HashMap<String, String>();
    
    private static final String YD_YYS="134,135,136,137,138,139,141,147,150,151,152,157,158,159,172,178,182,183,184,187,188,198";
    private static final String LT_YYS="130,131,132,145,146,155,156,166,175,176,185,186";
    private static final String DX_YYS="133,149,153,173,174,177,180,181,189,191,199";
    

    @PostConstruct
    public void initDistrictMap() {
        map.put("010", "010");
        map.put("020", "020");
        map.put("021", "021");
        map.put("022", "022");
        map.put("023", "023");
        map.put("024", "024");
        map.put("025", "025");
        map.put("026", "026");
        map.put("027", "027");
        map.put("028", "028");
        map.put("029", "029");
    }

    @Transactional
    public CallNumDto interconnectionProcess(String customerNo, String caller, String callee) {
        CallNumDto dto = null;
        User user = this.userDao.getUserByNumber(customerNo);
        if (user == null) {
            throw new IllegalArgumentException("根据:" + customerNo + "未找到对应用户信息");
        }
        List<InterConnection> list = user.getInterConns();
        String processCallee = this.baseService.processCallerNo(callee);
        for (InterConnection conn : list) {
            dto = this.getCallNumDto(user, conn, caller, callee, processCallee);
            if (dto != null) {
                break;
            }
        }
        if (dto == null) {
            dto = this.globalInterconnectionProcess(user, caller, callee, processCallee);
        }
        return dto;
    }

    /**
     * 处理全局的互联互通配置.
     *
     * @param user the user
     * @param caller the caller
     * @param callee the callee
     * @param processCallee the process callee
     * @return the call num dto
     */
    private CallNumDto globalInterconnectionProcess(User user, String caller, String callee, String processCallee) {
        CallNumDto dto = null;
        List<InterConnection> list = this.interConnDao.findInterConnectionByCustomerNoAndUseFlag(
            BusinessConstants.GLOBAL_IONN, true);
        for (InterConnection conn : list) {
            dto = this.getCallNumDto(user, conn, caller, callee, processCallee);
            if (dto != null) {
                break;
            }
        }
        return dto;
    }

    public CallNumDto httpProcess(InterConnection conn, String caller, String callee) {
        LOGGER.debug("InterConnection info:{},caller:{},callee:{},", conn, caller, callee);
        String processCaller = this.baseService.processCallerNo(caller);
        String processCallee = this.baseService.processCallerNo(callee);
        User user = this.userDao.getUserByNumber(conn.getCustomerNo());
        if (user == null) {
            throw new IllegalArgumentException("根据:" + conn.getCustomerNo() + "未找到对应用户信息");
        }
        return this.getCallNumDto(user, conn, processCaller, callee, processCallee);
    }

    /**
     * 获取符合规则的互联互通配置.
     * 
     * @param user 对应400
     * @param conn 互联互通规则
     * @param caller 处理过的主叫
     * @param callee 原始被叫
     * @param processCallee 处理过的被叫
     * @return
     */
    private CallNumDto getCallNumDto(User user, InterConnection conn, String caller, String callee, String processCallee) {
        CallNumDto dto = null;
        if (this.judgeCallee(conn, callee, processCallee) && this.judgeCaller(conn, caller)
            && this.judgeCallerArea(conn, caller, processCallee) && this.judgeCallerYys(conn, caller)) {
            LOGGER.debug("找到互联呼通配置，ID：{}", conn.getId());
            dto = this.processTxCode(conn, caller, processCallee);
            dto = this.processRule(dto, conn, callee, processCallee);
            dto.setOriginalNo(user.getOriginalNo());
            dto.setId(conn.getId());
        }
        LOGGER.debug("CallNumDto info:{}", dto);
        return dto;
    }

    /**
     * 判断转接号码.
     *
     * @param conn 互联互通规则
     * @param origCallee 原始被叫
     * @param callee 处理过的被叫（加区号）
     * @return true, if successful
     */
    private boolean judgeCallee(InterConnection conn, String origCallee, String callee) {
        boolean judgeResult = false;
        if (BusinessConstants.CALLEE_ALL.equals(conn.getCalledPhone())) {
            judgeResult = true;
        } else if (BusinessConstants.CALLEE_MOBILE.equals(conn.getCalledPhone()) && this.isMobile(callee)) {
            judgeResult = true;
        } else if (BusinessConstants.CALLEE_PHONE.equals(conn.getCalledPhone()) && this.isPhone(callee)) {
            judgeResult = true;
        } else if (conn.getCalledPhone().contains(origCallee)) {
            judgeResult = true;
        } else {
            String[] codeArray = conn.getCalledPhone().split(",");
            for (String code : codeArray) {
                if(callee.startsWith(code)){
                    judgeResult = true;
                    break;
                }
            }
        }
        return judgeResult;
    }

    private boolean isMobile(String callee) {
        return callee.length() > 13;
    }

    private boolean isPhone(String callee) {
        return callee.length() < 13;
    }

    /**
     * 判断主叫来电地区
     * 
     * @param conn
     * @param caller 处理过的主叫
     * @param callee 处理过的被叫（加区号）
     * @return
     */
    private boolean judgeCallerArea(InterConnection conn, String caller, String callee) {
        boolean judgeResult = false;
        if (BusinessConstants.ALL_AREA.equals(conn.getCallerCode())) {
            judgeResult = true;
        } else if (BusinessConstants.CALLEE_AREA.equals(conn.getCallerCode()) && this.compareDistrictNo(caller, callee)) {
            judgeResult = true;
        } else if (BusinessConstants.CALLEE_NO_AREA.equals(conn.getCallerCode())
            && !this.compareDistrictNo(caller, callee)) {
            judgeResult = true;
        } else {
            String[] areaArr = conn.getCallerCode().split(",");
            for (String str : areaArr) {
                if (caller.startsWith(str)) {
                    judgeResult = true;
                    break;
                }
            }
        }
        return judgeResult;
    }

    /**
     * 判断主被叫区号是否相同.
     *
     * @param caller 处理过的主叫（加区号）
     * @param callee 处理过的被叫（加区号）
     * @return true, if successful
     */
    private boolean compareDistrictNo(String caller, String callee) {
        String districtNo = caller.substring(0, 3);
        String districtNo2 = callee.substring(0, 3);

        String districtNo3 = caller.substring(0, 4);
        String districtNo4 = callee.substring(0, 4);

        return districtNo3.equals(districtNo4) || (districtNo.equals(districtNo2) && this.isDistrictNo(districtNo));
    }

    /**
     * 判断主叫来电类型.
     *
     * @param conn the conn
     * @param caller 处理过的主叫
     * @return true, if successful
     */
    private boolean judgeCaller(InterConnection conn, String caller) {
        boolean judgeResult = false;
        if (BusinessConstants.CALL_ALL.equals(conn.getCallerType())) {
            judgeResult = true;
        } else if (BusinessConstants.CALL_MOBILE.equals(conn.getCallerType()) && caller.length() > 13) {
            judgeResult = true;
        } else if (BusinessConstants.CALL_PHONE.equals(conn.getCallerType()) && caller.length() < 13) {
            judgeResult = true;
        }
        return judgeResult;
    }
    
    /**
     * 判断主叫来电运营商.
     *
     * @param conn the conn
     * @param caller 处理过的主叫
     * @return true, if successful
     */
    private boolean judgeCallerYys(InterConnection conn, String caller) {
        boolean judgeResult = false;
        if(this.isMobile(caller)){
        	String districtNo = caller.substring(0, 3);
        	String no="";
        	if(map.get(districtNo)!=null){
        		no=caller.substring(3, 6);
        	}else{
        		no=caller.substring(4, 7);
        	}
	        if (BusinessConstants.YYS_MODE_0.equals(conn.getYysMode())) {
	        	return YD_YYS.contains(no);
	        } else if (BusinessConstants.CALL_MOBILE.equals(conn.getYysMode())) {
	        	return LT_YYS.contains(no);
	        } else if (BusinessConstants.CALL_PHONE.equals(conn.getYysMode())) {
	        	return DX_YYS.contains(no);
	        }else{
	        	judgeResult = true;
	        }
        }else{
        	judgeResult = true;
        }
        return judgeResult;
    }
    
    /**
     * 配置规则处理.
     *
     * @param dto the dto
     * @param conn the conn
     * @param callee 原始被叫
     * @param processCallee 处理过的被叫（加区号）
     * @return the call num dto
     */
    private CallNumDto processRule(CallNumDto dto, InterConnection conn, String callee, String processCallee) {
        //        if (BusinessConstants.TX_MODE_DEFAULT == conn.getTxMode()) {
        //           
        //        } else if (BusinessConstants.TX_MODE_0 == conn.getTxMode()) {
        //
        //        }  
//        dto.setCalleeNo(callee);
        if (null != conn.getTxMode())
            switch (conn.getTxMode()) {
            case BusinessConstants.TX_MODE_1:
                dto.setCallerNo(conn.getPrefix() + dto.getCallerNo());
                break;
            case BusinessConstants.TX_MODE_2:
                dto.setCallerNo(conn.getPrefix() + conn.getDisplay());
                break;
            case BusinessConstants.TX_MODE_3:
                dto.setCalleeNo(processCallee);
                break;
            case BusinessConstants.TX_MODE_4:
                if(processCallee.startsWith(this.commonSettings.getLocalNo())) {
                    dto.setCalleeNo("0" + processCallee.substring(this.commonSettings.getLocalNo().length()));
                }
                break;
            default:
                break;
            }
        dto.setCallType(conn.getCallType());
        dto.setTransPrefix(conn.getTransCode());
        return dto;
    }

    //    public CallNumDto processTransCall(CallNumDto dto, InterConnection conn) {
    //        dto.setCallType(conn.getCallType().intValue());
    //       
    //        return dto;
    //    }

    /**
     * 号码加减区号处理.
     *
     * @param conn the conn
     * @param caller 处理过的主叫（加区号）
     * @param callee 处理过的被叫（加区号）
     * @return the call num dto
     * 
     */
    private CallNumDto processTxCode(InterConnection conn, String caller, String callee) {
        CallNumDto dto = new CallNumDto();
        if (null != conn.getTxCode()) switch (conn.getTxCode()) {
			case BusinessConstants.TX_CODE_0:
				if (this.compareDistrictNo(caller, callee)) {
					dto.setCallerNo(this.subtractDistrictNo(caller));
				} else {
					dto.setCallerNo(this.getCaller(caller));
				}	break;
			case BusinessConstants.TX_CODE_1:
				dto.setCallerNo(this.getCaller(caller));
				break;
			case BusinessConstants.TX_CODE_2:
				dto.setCallerNo(this.subtractDistrictNo(caller));
				break;
			default:
				break;
		}
        return dto;
    }

    private String subtractDistrictNo(String caller) {
        String districtNo = caller.substring(0, 3);
        if ("000".equals(districtNo) || this.isDistrictNo(districtNo)) {
            return caller.substring(3);
        }
        return caller.substring(4);
    }

    private boolean isDistrictNo(String key) {
        return map.containsKey(key);
    }

    private String getCaller(String caller) {
        if (caller.length() > 13) {
            caller = "0" + this.subtractDistrictNo(caller);
        }
        return caller;
    }
}
