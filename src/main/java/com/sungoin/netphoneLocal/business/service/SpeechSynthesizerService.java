package com.sungoin.netphoneLocal.business.service;

import it.sauronsoftware.jave.EncoderException;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Date;

import com.alibaba.nls.client.AccessToken;
import com.alibaba.nls.client.protocol.NlsClient;
import com.alibaba.nls.client.protocol.OutputFormatEnum;
import com.alibaba.nls.client.protocol.SampleRateEnum;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizer;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerListener;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerResponse;
import com.sungoin.netphoneLocal.business.dao.BaiduRingRepository;
import com.sungoin.netphoneLocal.business.po.BaiduRing;
import com.sungoin.netphoneLocal.config.BaiduSettings;
import com.sungoin.netphoneLocal.midware.config.MidwareSettings;
import com.sungoin.netphoneLocal.util.VoiceUtil;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
/**
 * 此示例演示了：
 *      语音合成API调用。
 *      动态获取token。
 *      流式合成TTS。
 *      首包延迟计算。
 */
@Service
public class SpeechSynthesizerService {
	
    private static final Logger LOGGER = LoggerFactory.getLogger(SpeechSynthesizerService.class);
    
    @Autowired
    private BaiduSettings baiduSettings;
    
    @Autowired
    private MidwareSettings midwareSettings;
    
    @Autowired
    private BaiduRingRepository repo; 
    
    private static SpeechSynthesizerListener getSynthesizerListener(File scrFile, File destFile, long startTime) {
        SpeechSynthesizerListener listener = null;
        try {
            listener = new SpeechSynthesizerListener() {
                FileOutputStream fout = new FileOutputStream(scrFile);
                private boolean firstRecvBinary = true;
                //语音合成结束
                @Override
                public void onComplete(SpeechSynthesizerResponse response) {
                    //调用onComplete时表示所有TTS数据已接收完成，因此为整个合成数据的延迟。该延迟可能较大，不一定满足实时场景。
                    LOGGER.info("name: " + response.getName() +
                        ", status: " + response.getStatus()+
                        ", output file :"+scrFile.getAbsolutePath()
                    );
                    try {
                        fout.close();
                    } catch (Exception e) {
                        LOGGER.error(e.getMessage(),e);
                    }
                    int code=VoiceUtil.wav2wav(scrFile, destFile);
                    if(code<0){
                    	try {
                        	LOGGER.info("转换失败进行二次转换");
                        	Thread.sleep(500L);
							VoiceUtil.wav2wav(scrFile, destFile);
							LOGGER.info("二次转换结果:{}",code);
						} catch (Exception e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
                    }
                }
                //语音合成的语音二进制数据
                @Override
                public void onMessage(ByteBuffer message) {
                    try {
                        if(firstRecvBinary) {
                            //计算首包语音流的延迟，收到第一包语音流时，即可以进行语音播放，以提升响应速度（特别是实时交互场景下）。
                            firstRecvBinary = false;
                            long now = System.currentTimeMillis();
                            LOGGER.info("tts first latency : " + (now - startTime) + " ms");
                        }
                        byte[] bytesArray = new byte[message.remaining()];
                        message.get(bytesArray, 0, bytesArray.length);
                        fout.write(bytesArray);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                @Override
                public void onFail(SpeechSynthesizerResponse response){
                    //task_id是调用方和服务端通信的唯一标识，当遇到问题时需要提供task_id以便排查。
                    LOGGER.info(
                        "task_id: " + response.getTaskId() +
                            //状态码 20000000 表示识别成功
                            ", status: " + response.getStatus() +
                            //错误信息
                            ", status_text: " + response.getStatusText());
                }
            };
        } catch (Exception e) {
        	LOGGER.error("TTS合成回调处理异常:",e);
        }
        return listener;
    }
  
    public String process(String userId,String content) {
    	LOGGER.info("{}:需要合成的语音文字:{}",userId,content);
        SpeechSynthesizer synthesizer = null;
        NlsClient client = null;
        File srcFile=null;
        File destFile=null;
        String filePath=null;
        String destFilePath=null;
        try {
        	BaiduRing ring=repo.findByCustomerNoAndContent(userId, content);
        	Date now=new Date();
        	if(ring!=null){
        		LOGGER.info("{}:查找到历史合成文件",userId);
        		filePath=ring.getFilePath();
        		destFilePath=ring.getFilePath();
        	}else{
        		LOGGER.info("{}:未查找到历史合成文件",userId);
        		filePath=this.midwareSettings.getRingPath() + File.separator + userId + File.separator + now.getTime()+".wav";
        		destFilePath=this.midwareSettings.getRingPath() + File.separator + userId + File.separator + now.getTime()+"_dest.wav";
        	}
        	LOGGER.info("{}:合成语音路径:{}",userId,filePath);
        	srcFile = new File(filePath);
        	if(srcFile.exists()){
        		return filePath;
        	}else{
        		createFile(srcFile);
        		destFile = new File(destFilePath);
        		createFile(destFile);
        	}
        	LOGGER.info("akId:{},akSecret:{}",baiduSettings.getAkId(), baiduSettings.getAkSecret());
        	AccessToken accessToken = new AccessToken(baiduSettings.getAkId(), baiduSettings.getAkSecret());
            accessToken.apply();
            LOGGER.info("get token: " + accessToken.getToken() + ", expire time: " + accessToken.getExpireTime());
            String url=baiduSettings.getTtsUrl();
            if(StringUtils.isEmpty(url)) {
                client = new NlsClient(accessToken.getToken());
            }else {
                client = new NlsClient(url, accessToken.getToken());
            }
            //创建实例，建立连接。
            long startTime = System.currentTimeMillis();
            synthesizer = new SpeechSynthesizer(client, getSynthesizerListener(srcFile,destFile,startTime));
            synthesizer.setAppKey(baiduSettings.getAppKey());
            //设置返回音频的编码格式
            synthesizer.setFormat(OutputFormatEnum.WAV);
            //设置返回音频的采样率
            synthesizer.setSampleRate(SampleRateEnum.SAMPLE_RATE_8K);
            //发音人
            synthesizer.setVoice("aiqi");
            //语调，范围是-500~500，可选，默认是0。
            synthesizer.setPitchRate(0);
            //语速，范围是-500~500，默认是0。
            synthesizer.setSpeechRate(0);
            //设置用于语音合成的文本
            synthesizer.setText(content);
            // 是否开启字幕功能（返回相应文本的时间戳），默认不开启，需要注意并非所有发音人都支持该参数。
            synthesizer.addCustomedParam("enable_subtitle", false);
            //此方法将以上参数设置序列化为JSON格式发送给服务端，并等待服务端确认。
            long start = System.currentTimeMillis();
            synthesizer.start();
            LOGGER.info("tts start latency " + (System.currentTimeMillis() - start) + " ms");
            //等待语音合成结束
            synthesizer.waitForComplete();
            LOGGER.info("tts stop latency " + (System.currentTimeMillis() - start) + " ms");
            if(ring==null){
            	 ring=new BaiduRing();
            	 ring.setCreateTime(now);
            	 ring.setCustomerNo(userId);
            	 ring.setContent(content);
            }
            ring.setFilePath(destFilePath);
            repo.save(ring);
        } catch (Throwable e) {
        	filePath=null;
        	LOGGER.error("TTS合成回调执行异常:",e);
        } finally {
            //关闭连接
            if (null != synthesizer) {
				synthesizer.close();
            }
            if (null != client) {
                client.shutdown();
            }
        }
        return destFilePath;
    }
    
    private static void createFile(File file) throws IOException {
        if (file.exists() && file.isFile()) {
            file.delete();
            file.createNewFile();
            return;
        }
        File parentFile = file.getParentFile();
        if (parentFile.exists()) {
            if (parentFile.isFile()) {
                parentFile.delete();
                parentFile.mkdirs();
            }
        } else {
            parentFile.mkdirs();
        }
        file.createNewFile();
    }
}
