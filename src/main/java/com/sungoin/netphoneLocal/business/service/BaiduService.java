package com.sungoin.netphoneLocal.business.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.sungoin.netphoneLocal.business.bean.baidu.BaiduResult;
import com.sungoin.netphoneLocal.business.bean.baidu.QueryBindRelaDTO;
import com.sungoin.netphoneLocal.business.po.TalkNote;
import com.sungoin.netphoneLocal.config.BaiduSettings;
import com.sungoin.netphoneLocal.util.CtiDes;
import com.sungoin.netphoneLocal.util.HttpHelper;
import com.sungoin.netphoneLocal.util.JsonHelper;
import com.sungoin.netphoneLocal.util.MD5Util;

@Service
public class BaiduService {

	private static final Logger LOGGER = LoggerFactory.getLogger(BaiduService.class);
	
	@Autowired
	private BaiduSettings baiduSettings;
	
	public BaiduResult queryIvrInfo(TalkNote talk,String ivrKey){
		try {
			QueryBindRelaDTO dto=new QueryBindRelaDTO();
			dto.setCustomerNumber(CtiDes.getInstance().decrypt(talk.getCallerNo()));
			dto.setHotline(talk.getCustomerNo());
			dto.setSubNum(ivrKey);
			dto.setUniqueId(talk.getId());
			dto.setTimestamp(talk.getIncomingTime().getTime()/1000+"");
			dto.setAuthKey(MD5Util.encoderByMd5("VFJSVDQwMA#"+dto.getTimestamp()));
            LOGGER.debug("queryIvrInfo url:{}, dto:{}", baiduSettings.getQueryIvrUrl(), JsonHelper.Object2Json(dto));
			return HttpHelper.postBaidu(baiduSettings.getQueryIvrUrl(), dto);
		} catch (Exception e) {
			LOGGER.error("{}:查询IVR异常:",talk.getCustomerNo(),e);
			return null;
		}
	}
	
	public BaiduResult queryBindPhone(TalkNote talk,String ivrKey){
		try {
			QueryBindRelaDTO dto=new QueryBindRelaDTO();
			dto.setCustomerNumber(CtiDes.getInstance().decrypt(talk.getCallerNo()));
			dto.setHotline(talk.getCustomerNo());
			dto.setSubNum(ivrKey);
			dto.setUniqueId(talk.getId());
			dto.setTimestamp(talk.getIncomingTime().getTime()+"");
			dto.setAuthKey(MD5Util.encoderByMd5("VFJSVDQwMA#"+dto.getTimestamp()));
            LOGGER.debug("queryBindPhone url:{}, dto:{}", baiduSettings.getQueryBindPhoneUrl(), JsonHelper.Object2Json(dto));
			return HttpHelper.postBaidu(baiduSettings.getQueryBindPhoneUrl(), dto);
		} catch (Exception e) {
			LOGGER.error("{}:查询绑定号码异常:",talk.getCustomerNo(),e);
			return null;
		}
	}
	
}
