/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.hibernate.Cache;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.jpa.HibernateEntityManagerFactory;
import org.springframework.stereotype.Service;

import com.sungoin.netphoneLocal.business.po.BindPhone;
import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.business.po.User;

/**
 *
 * <AUTHOR> 2015-9-6
 */
@Service
public class HibernateCacheService {

	@PersistenceContext
	private EntityManager em;

	public Session getSession() {
		return (Session) this.em.getDelegate();
	}

	public SessionFactory getSessionFactory() {
		return ((HibernateEntityManagerFactory) (this.em.getEntityManagerFactory())).getSessionFactory();
	}

	private Cache getHibernateCache() {
		SessionFactory sf = this.getSessionFactory();
		return sf.getCache();
	}

	private void clearUserCache(String userId) {
		Cache cache = this.getHibernateCache();
		Class type = User.class;
		if (userId == null) {
			cache.evictEntityRegion(type);
			cache.evictNaturalIdRegion(type);
			cache.evictCollectionRegion(type.getName() + ".depts");
			cache.evictCollectionRegion(type.getName() + ".whiteList");
			cache.evictCollectionRegion(type.getName() + ".blackList");
			cache.evictCollectionRegion(type.getName() + ".interConns");
		} else {
			cache.evictEntity(type, userId);
			cache.evictCollection(type.getName() + ".depts", userId);
			cache.evictCollection(type.getName() + ".whiteList", userId);
			cache.evictCollection(type.getName() + ".blackList", userId);
			cache.evictCollection(type.getName() + ".interConns",userId);
		}
	}

	private void clearDeptCache(String deptId) {
		Cache cache = this.getHibernateCache();
		Class type = Dept.class;
		if (deptId == null) {
			cache.evictEntityRegion(type);
			cache.evictNaturalIdRegion(type);
			cache.evictCollectionRegion(type.getName() + ".childDepts");
			cache.evictCollectionRegion(type.getName() + ".bindList");
			cache.evictCollectionRegion(type.getName() + ".crbtRingList");
		} else {
			cache.evictEntity(type, deptId);
			cache.evictCollection(type.getName() + ".childDepts", deptId);
			cache.evictCollection(type.getName() + ".bindList", deptId);
			cache.evictCollection(type.getName() + ".crbtRingList", deptId);
		}
	}

	private void clearBindPhoneCache(String bindPhoneId) {
		Cache cache = this.getHibernateCache();
		Class type = BindPhone.class;
		if (bindPhoneId == null) {
			cache.evictEntityRegion(type);
			cache.evictNaturalIdRegion(type);
			cache.evictCollectionRegion(type.getName() + ".timePlanList");
		} else {
			cache.evictEntity(type, bindPhoneId);
			cache.evictCollection(type.getName() + ".timePlanList", bindPhoneId);
		}
	}

	public void clearCache(Class type) {
		this.clearCache(type, null);
	}
	
	public void clearCache(Class type, String id) {
		if (type.equals(User.class)) {
			this.clearUserCache(id);
		} else if (type.equals(Dept.class)) {
			this.clearDeptCache(id);
		} else if (type.equals(BindPhone.class)) {
			this.clearBindPhoneCache(id);
		} else {
			Cache cache = this.getHibernateCache();
			cache.evictEntityRegion(type);
			cache.evictNaturalIdRegion(type);
		}
	}

	public void clearAllCache() {
		Cache cache = this.getHibernateCache();
		cache.evictAllRegions();
	}

}
