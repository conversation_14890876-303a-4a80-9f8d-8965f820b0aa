package com.sungoin.netphoneLocal.task.impl;

import java.util.Date;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.sungoin.netphoneLocal.business.service.BaseService;
import com.sungoin.netphoneLocal.config.CommonSettings;
import com.sungoin.netphoneLocal.midware.service.ProcessService;
import com.sungoin.netphoneLocal.task.CommonTask;
import com.sungoin.netphoneLocal.util.DateTimeUtil;

@Component
@EnableScheduling
public class CommonTaskImpl implements CommonTask {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonTaskImpl.class);

    @Resource
    private BaseService baseService;
    
    @Resource
    private CommonSettings commonSettings;
    
    @Resource
    private ProcessService processService;
	
	@Scheduled(cron = "0 10 0 * * ?")
    @Override
	public void doResetCallNum() {
		LOGGER.debug("开始清除坐席通话数任务...");
		//每天清除按天平均的通话数
        this.baseService.resetAgentCallNum(0);

        Date now = new Date();
        int week = DateTimeUtil.getWeek(now);
        LOGGER.debug("当前星期数:{}",week);
        //判断今天是否为周一
        if (week == 1) {
        	LOGGER.debug("星期一执行周度通话数清除...");
            this.baseService.resetAgentCallNum(1);
        }

        //判断今天是否为1号码
        int day = DateTimeUtil.getMonthOfDay(now);
        if (day == 1) {
            this.baseService.resetAgentCallNum(2);
        }
		LOGGER.debug("清除坐席通话数任务结束...");
	}
	
	@Scheduled(cron = "0 0 3 * * ?")
    @Override
    public void doBakNoteAndRecord() {
        LOGGER.debug("开始执行转移话单记录...");
        try {
            this.baseService.doBakNoteAndRecord();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        LOGGER.debug("执行转移话单记录结束...");
    }
	
    @Scheduled(cron = "0 0 4 * * ?")
    @Override
    public void doResetAgentStatus() {
        LOGGER.debug("开始执行坐席状态置闲扫描更新任务...");
        if(this.commonSettings.isMainServer()) {
            this.baseService.resetAgentStatusWithOrder();
        }
        processService.clearOrderMap();
        LOGGER.debug("结束执行坐席状态置闲扫描更新任务...");
    }
}
