/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.outcall;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE_OUTCALL;
import java.util.concurrent.Callable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class AutoTestCallThread implements Callable<String> {

    private static final Logger log = LoggerFactory.getLogger(AutoTestCallThread.class);
    private final String caller;
	private final String callee;

    public AutoTestCallThread(String caller, String callee) {
        this.caller = caller;
        this.callee = callee;
    }
    
    @Override
    public String call() throws Exception {
        Call call = null;
        try {
            log.debug("自动测试开始呼叫，主叫：{}， 被叫：{}", caller, callee);
            call = CallManager.getInstance().makeCall(caller, callee, null, false, 30, null);
            if(call == null) {
                log.debug("呼叫失败！");
				return "0";
			} else {
                call.setParam(CALL_TYPE, CALL_TYPE_OUTCALL);
                log.debug("呼叫成功！3秒后自动挂机");
                Thread.sleep(3000);
				if(call.getState() == Constants.CALL_CONTENT) {
                    call.onHook();
                }
				return "1";
			}
        } catch (Exception ex) {
			log.error(ex.getMessage(), ex);
			return ex.getMessage();
		} 
    }
    
}
