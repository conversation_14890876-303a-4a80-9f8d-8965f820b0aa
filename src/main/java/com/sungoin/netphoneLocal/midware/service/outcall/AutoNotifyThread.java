/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.outcall;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.netphoneLocal.business.bean.RingAutoNotifyInfoDto;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE_OUTCALL;
import com.sungoin.netphoneLocal.midware.service.ProcessService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class AutoNotifyThread implements Runnable {

    private static final Logger log = LoggerFactory.getLogger(AutoNotifyThread.class);
    private static final String SIP_PREFIX = "A";
    
    private final RingAutoNotifyInfoDto dto;
    private final ProcessService service;

    public AutoNotifyThread(RingAutoNotifyInfoDto dto, ProcessService service) {
        this.dto = dto;
        this.service = service;
    }
    
    @Override
    public void run() {
        Call call = null;
        try {
            dto.setResult(0);
            String caller = dto.getCaller();
            String callee = this.service.getFixedCalleeNo(dto.getCallee());
            log.debug("{}-自动通知开始呼叫，主叫：{}， 被叫：{}，类型：{}", dto.getNumberCode(), caller, callee, dto.getTestType());
            if(service.isSipFlag()) {
                //sip平台添加自动通知前缀,以便使用自动通知的网关
                callee = SIP_PREFIX + callee;
            }
            call = CallManager.getInstance().makeCall(caller, callee, null, false, 30, null);
            if(call != null) {
                call.setParam(CALL_TYPE, CALL_TYPE_OUTCALL);
                dto.setResult(1);
                String ring = service.getDefaultRing("NUMBER_NEW_OPEN".equals(dto.getTestType()) ? MidwareConstants.DEFAULT_RING_AUTO_NOTIFY_OPEN : MidwareConstants.DEFAULT_RING_AUTO_NOTIFY_RING);
                log.debug("{}-自动通知播放音频：{}", dto.getNumberCode(), ring);
                call.play(ring, false, false, false, 25, 0);
                if(call.getState() == Constants.CALL_CONTENT) {
                    call.onHook();
                }
            }
            service.sendAutoNotifyMessage(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } 
    }
    
}
