/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.task;

import com.sungoin.netphoneLocal.business.mq.SmsSendDto;
import com.sungoin.netphoneLocal.midware.service.ProcessService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class OfflineMessageTask implements Runnable {
    private static final Logger log = LoggerFactory.getLogger(OfflineMessageTask.class);
    
    private final String numberCode;
    private final String deptLsh;
    private final String caller;
    private final ProcessService service;

    public OfflineMessageTask(String numberCode, String deptLsh, String caller, ProcessService service) {
        this.numberCode = numberCode;
        this.deptLsh = deptLsh;
        this.caller = caller;
        this.service = service;
    }
    
    @Override
    public void run() {
        log.debug("{} 开始通知客户可以拨打电话进线 {}", this.numberCode, this.caller);
        //发送消息
        SmsSendDto dto = new SmsSendDto();
        dto.setType(4);
        dto.setNumberCode(numberCode);
        dto.setCallerNo(caller);
        dto.setDeptId(deptLsh);
        this.service.sendSmsMessage(dto);
        //添加缓存
        this.service.addOfflineCache(numberCode, deptLsh, this.service.getFixedCallerNo(caller));
    }
    
}
