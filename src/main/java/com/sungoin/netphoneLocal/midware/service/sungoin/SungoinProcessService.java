/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.sungoin;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.SendResult;
import com.sungoin.netphoneLocal.business.bean.CallNumDto;
import com.sungoin.netphoneLocal.business.bean.TalkNoteDto;
import com.sungoin.netphoneLocal.business.mq.SmsSendDto;
import com.sungoin.netphoneLocal.business.po.*;
import com.sungoin.netphoneLocal.business.rmi.RmiService;
import com.sungoin.netphoneLocal.business.service.BaseService;
import com.sungoin.netphoneLocal.business.service.BusinessService;
import com.sungoin.netphoneLocal.business.service.InterConnectionService;
import com.sungoin.netphoneLocal.config.CommonSettings;
import com.sungoin.netphoneLocal.config.SungoinSetting;
import com.sungoin.netphoneLocal.constants.MqConstants;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import com.sungoin.netphoneLocal.midware.config.MidwareSettings;
import com.sungoin.netphoneLocal.socket.message.AgentMessage;
import com.sungoin.netphoneLocal.socket.message.CalleeOnhookMessage;
import com.sungoin.netphoneLocal.socket.message.CallincomeMessage;
import com.sungoin.netphoneLocal.socket.message.SocketMessage;
import com.sungoin.netphoneLocal.util.DateTimeUtil;
import com.sungoin.netphoneLocal.util.HttpHelper;
import com.sungoin.netphoneLocal.util.JsonHelper;
import com.sungoin.netphoneLocal.util.VoiceUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sungoin.netphoneLocal.midware.service.ProcessService.MONITOR_PREFIX;

/**
 *
 * <AUTHOR>
 */
@Service
public class SungoinProcessService {
    private static final Logger log = LoggerFactory.getLogger(SungoinProcessService.class);
    
    @Resource
    private SungoinSetting setting;
    
    @Resource
    private MidwareSettings midwareSettings;

    @Resource
    private CommonSettings commonSettings;
    
    @Resource
    private BaseService baseService;
    
    @Resource
    private BusinessService businessService;
    
    @Resource
    InterConnectionService ics;
    
    @Resource
    RmiService rmiService;
    
    ExecutorService es = Executors.newCachedThreadPool();
    Map<String, String> specialOrigNoMap;

    @Resource
    @Qualifier("PlatformMessageProducer")
    private Producer mqProducer;
    
    @PostConstruct
    public void init() {
        this.specialOrigNoMap = new HashMap();
        String speNoStr = this.commonSettings.getSpecialOrig();
        if (StringUtils.isNotEmpty(speNoStr)) {
            String[] array = speNoStr.split(",");
			Stream.of(array).filter(s -> s.length() > 11).forEach(s -> {
				 this.specialOrigNoMap.put(s.substring(0, 10), s.substring(11));
			});
        }
    }
    
    public String getHotlineCallee(HotlineCallInDto dto) {
        try {
            String callee = null;
            SungoinResponse resp = HttpHelper.postSungoinHttps(setting.getSungoinApiEndpoint(), dto);
            if(resp != null && resp.getData() != null) {
                callee = (String) ((Map)resp.getData()).get("callee");
            }
            return callee;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }
    
    @Transactional(readOnly = true)
    public Dept getChildDeptByDivide(Dept parent, String callerNo) {
        parent = this.baseService.getDept(parent.getId());
        return this.baseService.getChildDeptBySatisfyTimePlan(parent, callerNo);
    }
    
    public boolean hasChildDeptsBySql(Dept parent) {
		long count = this.baseService.findDeptCountByCustomerNoAndParentDeptLsh(parent.getCustomerNo(),
            parent.getDeptLsh());
		return count > 0;
	}
    
    public String getFixedCallerNo(String callNo) {
        //118开头的号码特殊处理，不改变主叫
        if(callNo.length() == 8 && callNo.startsWith("118")) {
            return callNo;
        }
        return this.baseService.processCallerNo(callNo);
    }
    
    public String getDistrictDesc(String caller) {
        return this.baseService.getDistrictDesc(caller);
    }
    
    public void saveOrUpdateTalkNote(final TalkNote talkNote) {
        this.es.submit(() -> {
            try {
                SungoinProcessService.this.businessService.saveOrUpdate(talkNote);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        });
    }
    
    public void saveOrUpdateVoiceBox(final VoiceBox vb) {
        this.es.submit(() -> {
            SungoinProcessService.this.businessService.saveOrUpdateVoiceBox(vb);
        });
    }
    
    @Transactional(readOnly = true)
    public Dept getRootDept(User user) {
        long deptCount = this.baseService.findDeptCountByCustomerNo(user.getNumber());
        log.debug("{}:导航总数:{}", user.getNumber(), deptCount);
        if (deptCount > this.midwareSettings.getMaxDeptCount()) {
            return this.baseService.getDeptByCustomerNoAndDeptLsh(user.getNumber(), user.getNumber());
        } else {
            user = this.baseService.getUser(user.getId());
            return user.getRootDept();
        }
    }
    
    @Transactional(readOnly = true)
    public CrbtRing getCrbtRingFile(Dept dept) {
        dept = this.baseService.getDept(dept.getId());
        CrbtRing cring = this.baseService.findCrbtRingWithTemplate(dept);
        log.debug("根据时间模板找到的炫铃：{}", cring);
        if(cring != null) {
            return cring;
        }
        return dept.getCurrentCrbtRing(this.baseService.isHoliday(new Date()));
    }
    
    public String getDefaultRing(String ringName) {
        return this.midwareSettings.getDefaultRingFile(ringName);
    }
    
    @Transactional
    public String getRingFile(Dept dept) {
        CrbtRing cring = null;
        if(dept.isRootDept()) {
            cring = this.getCrbtRingFile(dept);
        }
        ColorRing ring = null;
        if (cring != null && cring.getColorRing() != null) {
            ring = cring.getColorRing();
        } else {
            User user=baseService.getUserByNumber(dept.getCustomerNo());
            if(user.isIvrFlag() || user.isColorringFlag()){
            	ring = dept.getColorRing();
            } else {
            	log.info("{}:导航、彩铃开关均关闭，彩铃返回空", dept.getCustomerNo());
            }
        }
        return ring != null ? this.midwareSettings.getVocDataPath() + ring.getFilePath() : null;
    }
    
    @Transactional
    public String getRingFile(ColorRing ring) {
        ColorRing localRing = this.baseService.getColorRing(ring.getId());
        return this.midwareSettings.getVocDataPath() + localRing.getFilePath();
    }
    
    public boolean isSipFlag() {
        return this.midwareSettings.isSipFlag();
    }
    
    public String getSipVoiceBoxPath() {
        return this.midwareSettings.getSipVoiceboxPath();
    }
    
    public VoiceBox startVoicebox(String callerNo, String customerNo, Dept dept) {
        Date now = new Date();
        VoiceBox vb = new VoiceBox();
        vb.setCallerNo(callerNo);
        vb.setCustomerNo(customerNo);
        vb.setDeptLsh(dept != null ? dept.getDeptLsh() : customerNo);
        vb.setStartTime(now);
        String fileName, filePath;
        if(this.isSipFlag()) {
            fileName = "boxes_" + callerNo + "_" + System.currentTimeMillis() + ".wav";
            filePath = this.getSipVoiceBoxPath() + "/storage/recordings/" + fileName;
        } else {
            fileName = callerNo + "_" + System.currentTimeMillis() + ".wav";
            filePath = this.midwareSettings.getBoxPath() + "/" + DateTimeUtil.formatShortDate(now) + "/" + customerNo;
            File file = new File(filePath);
            if (!file.exists() || !file.isDirectory()) {
                file.mkdirs();
            }
            filePath += "/" + fileName;
        }
        vb.setFileName(fileName);
        vb.setFilePath(filePath);
        return vb;
    }
    
    @Transactional
    public List<BindPhone> getIdleAgentsByDept(Dept dept, String callerNo) {
        if (this.commonSettings.isMainServer()) {
            return this.baseService.getIdleAgent(dept, callerNo);
        } else {
            return this.rmiService.getIdleAgent(dept.getCustomerNo(), dept.getDeptLsh(), callerNo);
        }
    }
    
    public void sortAgents(Dept dept, List<BindPhone> agents) {
        if (dept.getCallModle() == null) {
            log.warn("部门：{} 没有配置接听策略！", dept.getDeptLsh());
            return;
        }
        switch (dept.getCallModle()) {
            case 1:
                //随机
                Collections.shuffle(agents);
                break;
            case 2:
                //平均
                Collections.sort(agents, (BindPhone o1, BindPhone o2) -> o1.getCallnum() - o2.getCallnum());
                break;
            case 3:
                //轮训
                String last = getDeptLastBindPhone(dept);
                log.debug("轮询策略，上次接听的绑定号码流水号：{}", last);
                if (last != null) {
                    BindPhone lastBindPhone = this.getLocalBindPhone(dept.getCustomerNo(), last);
                    if (lastBindPhone != null) {
                        Long lastOrderBy = lastBindPhone.getOrderBy();
                        log.debug("轮询策略，上次接听的绑定号码 orderBy：{}", lastOrderBy);
                        List<BindPhone> sortList = new ArrayList(agents.size());
                        int index = 0;
                        for (int i = 0; i < agents.size(); i++) {
                            if (agents.get(i).getOrderBy() > lastOrderBy) {
                                index = i;
                                log.debug("找到大于上次接听排序号的坐席，此坐席将排首位！：{}", agents.get(i));
                                break;
                            }
                        }
                        for (int i = index; i < agents.size(); i++) {
                            sortList.add(agents.get(i));
                        }
                        for (int i = 0; i < index; i++) {
                            sortList.add(agents.get(i));
                        }
                        agents.clear();
                        agents.addAll(sortList);
                        sortList.clear();
                    }
                }
                break;
            case 4:
                //权重模式
                log.debug("权重策略，根据接通数/权重值排序");
                List<BindPhone> ratioList = agents.stream().filter(b -> b.getRatioIntValue() > 0).sorted((BindPhone o1, BindPhone o2) -> {
                    if (o1.getWeightValue().doubleValue() == o2.getWeightValue().doubleValue()) {
                        return o2.getRatioIntValue() - o1.getRatioIntValue();
                    }
                    return o1.getWeightValue().compareTo(o2.getWeightValue());
                }).collect(Collectors.toList());
                
                List<BindPhone> emptyRatioList = agents.stream().filter(b -> b.getRatioIntValue() == 0).sorted((BindPhone o1, BindPhone o2) -> {
                    return o1.getCallnum() - o2.getCallnum();
                }).collect(Collectors.toList());
                agents.clear();
                agents.addAll(ratioList);
                agents.addAll(emptyRatioList);
                ratioList.clear();
                emptyRatioList.clear();
                break;
            default:
                break;
        }
    }
    
    public List<BindPhone> judgeMemoryFlag(User user, Dept dept, List<BindPhone> agents, String mainCall){
    	if(agents.isEmpty()){
    		return agents;
    	}
    	if(!user.isMemoryFlag()){
    		log.debug("号码:{},部门流水号:{},未开通记忆功能",dept.getCustomerNo(),dept.getDeptLsh());
    		return agents;
    	}
    	List<TalkNoteDto> list=baseService.findByDeptAndMainCall(dept, mainCall);
    	final String[] backEndpoints = this.commonSettings.getBackupServers();
    	if(backEndpoints.length>0){
    		//获取备份机器的主叫来电数据
			for (String endpoint : backEndpoints) {
				if(!endpoint.startsWith("http")){
					continue;
				}
				String data =HttpHelper.SendPostRequest(endpoint + "getOffHookCallee/" + dept.getCustomerNo() + "/" + dept.getDeptLsh()+ "/" + mainCall, null);
				list.addAll(JsonHelper.json2List(data, TalkNoteDto.class));
			}
        } 
    	if(list.isEmpty()){
    		log.debug("号码:{},部门流水号:{},来电号码:{}:未找到时间段内历史呼入话单",
    				dept.getCustomerNo(),dept.getDeptLsh(),mainCall);
    		return agents;
    	}
    	log.debug("排序前的集合:{}",list);
    	//按呼入时间排序，取出最近的来电
    	Collections.sort(list);
    	log.debug("排序后的集合:{}",list);
    	String bindPhoneNo=list.get(0).getCalleeNo();
    	log.debug("记忆功能安排呼叫的坐席绑定号码:{}",bindPhoneNo);
//		Optional<BindPhone> find = agents.stream().filter(phone -> phone.getBindPhoneNo().equals(bindPhoneNo)).findFirst();
//    	if(find.isPresent()){
//    		agents.set(0, find.get());
//    	}
    	BindPhone filterBindPhone=null;
    	for(BindPhone phone:agents){
    		if(phone.getOrigBindPhoneNo().equals(bindPhoneNo)){
    			filterBindPhone=phone;
    			break;
    		}
    	}
    	if(filterBindPhone!=null){
    		List<BindPhone> convertAgents=new ArrayList<BindPhone>();
    		convertAgents.add(filterBindPhone);
    		for(BindPhone phone:agents){
    			if(!phone.getOrigBindPhoneNo().equals(bindPhoneNo)){
    				convertAgents.add(phone);
    			}
    		}
    		log.debug("agents:{},convertAgents:{}",agents,convertAgents);
    		return convertAgents;
//    		agents=convertAgents;
//    		agents.add(0, filterBindPhone);
    	}
    	return agents;
    }
    
    public String getReportHeadFile() {
        return this.midwareSettings.getDefaultRingFile(MidwareConstants.DEFAULT_RING_NUMHEAD);
    }

    public String getReportEndFile() {
        return this.midwareSettings.getDefaultRingFile(MidwareConstants.DEFAULT_RING_NUMEND);
    }
    
    public String getNumberWav(int num) {
        if (num < 10) {
            return this.midwareSettings.getDefaultRingFile(num + ".wav");
        } else {
            StringBuilder sb = new StringBuilder();
            String s = num + "";
            char[] data = s.toCharArray();
            for (int i = 0; i < data.length; i++) {
                sb.append(this.midwareSettings.getDefaultRingFile(data[i] + ".wav")).append(",");
            }
            return sb.deleteCharAt(sb.length() - 1).toString();
        }
    }
    
    @Transactional
    public void callConnected(BindPhone bindPhone, TalkNote talkNote, Dept currentDept, boolean pushFlag, boolean isTransCall) {
        //在改状态之前再判断下状态是否是calling中。如果不是，则抛出异步修改异常。
        //		String state = this.getAgentState(bindPhone);
        //		if(!state.contains(AGENT_STATE_CALLING)) {
        //			throw new ConcurrentModificationException("绑定号码状态已经不是呼叫中！连接失败！");
        //		}
		if (this.commonSettings.isMainServer()) {
		this.baseService.modifyAgentStatus(bindPhone.getId(), MidwareConstants.AGENT_STATE_CONNECT);
		} else {
			this.rmiService.modifyAgentStatus(bindPhone.getCustomerNo(), bindPhone.getLsh(),
				MidwareConstants.AGENT_STATE_CONNECT);
		}
		log.info("号码:{}, pushFlag:{}", bindPhone.getCustomerNo(), pushFlag);
		if (pushFlag) {
            try {
				User user = this.baseService.getUserByNumber(bindPhone.getCustomerNo());
				long calleeOffhookTime = isTransCall ? (new Date().getTime()) : talkNote.getCalleeOffhookTime().getTime();
				SocketMessage message = new AgentMessage(bindPhone.getCustomerNo(), bindPhone.getAgentId(),
					bindPhone.getLsh(), AgentMessage.STATE_CONNECT, bindPhone.getOrigBindPhoneNo(), talkNote.getId(),
					calleeOffhookTime, talkNote.getIncomingTime().getTime(), bindPhone.getReportNum(),
					currentDept.getIvrKey(), bindPhone.isNeedLogin());
				log.info("号码:{}, platformPlus:{}", bindPhone.getCustomerNo(), user.isPlatformPlus());
				if(user.isPlatformPlus()) {
				    HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendAgentMessage", message);
				}
				log.info("号码:{}, sendMsg success:{}", bindPhone.getCustomerNo(), message);
			} catch (Exception e) {
				log.error("send connect msg error 号码:{}", bindPhone.getCustomerNo(), e);
			}
		}
    }
    
    public void callConnectedAsync(BindPhone bindPhone, TalkNote talkNote, Dept currentDept, boolean pushFlag, boolean isTransCall) {
		this.es.submit(()->{
			this.callConnected(bindPhone, talkNote, currentDept, pushFlag, isTransCall);
		});
	}

    public void addAgentCallNum(BindPhone bindPhone) {
        if (this.commonSettings.isMainServer()) {
            this.baseService.modifyAgentCallNum(bindPhone.getId());
        } else {
            this.rmiService.modifyAgentCallNum(bindPhone.getCustomerNo(), bindPhone.getLsh());
        }
    }
    
    public void addAgentCallNumAsync(BindPhone bindPhone) {
        this.es.submit(()->{
			this.addAgentCallNum(bindPhone);
		});
    }
    
    @Transactional(readOnly = true)
    public String getAgentAlertRing(Dept dept) {
        dept = this.baseService.getDept(dept.getId());
        return dept.getAgentAlertRing() != null ? this.midwareSettings.getVocDataPath()
            + dept.getAgentAlertRing().getFilePath() : null;
    }
    
    public int getAgentAlertRingTime() {
        return this.midwareSettings.getAgentAlertRingTime();
    }
    
    public BindPhone getLocalBindPhone(String customNo, String lsh) {
        return this.baseService.getBindPhoneByCustomerNoAndLsh(customNo, lsh);
    }
    
    private String getDeptLastBindPhone(Dept dept) {
        if (this.commonSettings.isMainServer()) {
            return dept.getLastBindPhoneLsh();
        } else {
            return this.rmiService.getDeptLastBindPhone(dept.getCustomerNo(), dept.getDeptLsh());
        }
    }
    
    public String getAgentState(BindPhone bindPhone) {
        if (this.commonSettings.isMainServer()) {
            return this.baseService.getAgentStatus(bindPhone.getId());
        } else {
            return this.rmiService.getAgentStatus(bindPhone.getCustomerNo(), bindPhone.getLsh());
        }
    }
    
    public String getDeptBindphoneDetails(Dept dept) {
        if (this.commonSettings.isMainServer()) {
            return this.baseService.getDeptBindphoneDetails(dept.getId());
        } else {
            return this.rmiService.getDeptBindphoneDetails(dept.getCustomerNo(), dept.getDeptLsh());
        }
    }
    
    @Transactional(readOnly = true)
	public String getVoiceBoxAlertRing(Dept dept) {
        Dept localDept = this.baseService.getDept(dept.getId());
        return localDept.getVoiceRing() != null ? this.midwareSettings.getVocDataPath() + localDept.getVoiceRing().getFilePath() : null;
    }
    
    @Transactional(readOnly = true)
    public String getCompanyRingFile(Dept dept) {
        dept = this.baseService.getDept(dept.getId());
        ColorRing ring = dept.getCompanyRing();
        return ring != null ? this.midwareSettings.getVocDataPath() + ring.getFilePath() : null;
    }
    
    public boolean isRingExist(String ringFile) {
        if (!this.midwareSettings.isRingFileCheck()) {
            log.info("配置文件不需要检测彩铃文件是否存在！返回true");
            return true;
        } else {
            return Files.exists(Paths.get(ringFile));
        }
    }
    
    public String getVoiceBoxAlertRing(String customerNo) {
        ColorRing cr = this.baseService.getVoiceBoxAlertRing(customerNo);
        return cr != null ? this.midwareSettings.getVocDataPath() + cr.getFilePath() : null;
    }
    
    public int getVoiceBoxMaxTime() {
        return this.midwareSettings.getVoiceboxMaxSeconds();
    }
    
    @Transactional
    public String getSmartBroadRingFile(Dept dept) {
        dept = this.baseService.getDept(dept.getId());
        ColorRing ring = dept.getSmartBroadRing();
        return ring != null ? this.midwareSettings.getVocDataPath() + ring.getFilePath() : null;
    }
    
    public void saveOrUpdateRecord(final VoiceRecord vr) {
        this.es.submit(() -> {
            SungoinProcessService.this.businessService.saveOrUpdateVoiceRecord(vr);
        });
    }
    
    @Transactional
    public void midwareUpdateAgentState(BindPhone bindPhone, String state, boolean sendMessage) {
        if (this.commonSettings.isMainServer()) {
            this.baseService.modifyAgentStatus(bindPhone.getId(), state);
        } else {
            this.rmiService.modifyAgentStatus(bindPhone.getCustomerNo(), bindPhone.getLsh(), state);
        }
        if (sendMessage) {
            log.debug("运营点更新坐席状态：{}，发送socket消息通知中心端。", state);
            SocketMessage message = new AgentMessage(bindPhone.getCustomerNo(), bindPhone.getAgentId(),
                bindPhone.getLsh(), state, bindPhone.getOrigBindPhoneNo(), bindPhone.isNeedLogin());
            User user = this.baseService.getUserByNumber(bindPhone.getCustomerNo());
            if(user.isPlatformPlus()) {
                HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendAgentMessage", message);
            }
        }
    }
    
    public int getPbxCount(BindPhone bindPhone) {
        if (this.commonSettings.isMainServer()) {
            return this.businessService.getConnectCount(bindPhone.getId());
        } else {
            return this.rmiService.getConnectCount(bindPhone.getCustomerNo(), bindPhone.getLsh());
        }
    }
    
    public void saveOrUpdatePbxInfo(BindPhone phone,Integer useCount){
    	this.baseService.saveOrUpdatePbxInfo(phone, useCount);
    }
    
    public void increasePbxCount(BindPhone bindPhone) {
        if (this.commonSettings.isMainServer()) {
            this.businessService.addConnect(bindPhone.getId());
        } else {
            this.rmiService.addConnect(bindPhone.getCustomerNo(), bindPhone.getLsh());
        }
    }
    
    public boolean isMainServer() {
        return this.commonSettings.isMainServer();
    }
    
    public void updateDeptLastBindPhone(String customNo, String deptLsh, String bindPhoneLsh) {
        if (this.commonSettings.isMainServer()) {
            this.baseService.updateDeptLastBindPhone(customNo, deptLsh, bindPhoneLsh);
        } else {
            this.rmiService.updateDeptLastBindPhone(customNo, deptLsh, bindPhoneLsh);
        }
    }
    
    public String getFixedCalleeNo(String callNo) {
        return this.baseService.processCallNo(callNo);
    }
    
    @Transactional
    public void screenRequest(String talkNoteId, BindPhone phone, String callin, String callout, String districtDesc, String ivrKey,String callee,long incomingTime) {
        String agentId = phone.getAgentId();
        SocketMessage message = new CallincomeMessage(phone.getCustomerNo(), agentId, callin, talkNoteId,
                districtDesc, phone.getReportNum(), ivrKey, callee, incomingTime);
        User user = this.baseService.getUserByNumber(phone.getCustomerNo());
        if (user.isPlatformPlus()) {
            HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendCallinMessage", message);
        } 
    }
    
    public VoiceRecord startRecord(User user, String caller, String callee, boolean jmyFlag) {
        String prefix = "";
        if (this.baseService.isNotRecordConvertWhiteList(user.getNumber())) {
            log.debug("{} 开启用户监听！", user.getNumber());
            prefix = MONITOR_PREFIX;
        }
        Date now = new Date();
        VoiceRecord record = new VoiceRecord();
        record.setCalleeNo(callee);
        record.setCallerNo(caller);
        record.setStartTime(now);
        record.setCustomerNo(user.getNumber());
        String filePath = this.midwareSettings.getRecordPath() + "/" + DateTimeUtil.formatShortDate(now) + "/"
            + user.getNumber();
        File file = new File(filePath);
        if (!file.exists() || !file.isDirectory()) {
            file.mkdirs();
        }
        Long timestamp = System.currentTimeMillis();
        String fileName = null;
        String recordFilePath = null;
        if(jmyFlag && !this.isSipFlag()) {
            String fileNameLeft = prefix + caller + "_" + timestamp + "_L.wav";
            String fileNameRight = prefix + caller + "_" + timestamp + "_R.wav";
            fileName = fileNameLeft + "," + fileNameRight;
            String filePathLeft = filePath + "/" + fileNameLeft;
            String filePathRight = filePath + "/" + fileNameRight;
            recordFilePath = filePathLeft + "," + filePathRight;
        } else {
            fileName = prefix + caller + "_" + timestamp + ".wav";
            recordFilePath = filePath + "/" + fileName;
        }
        record.setFileName(fileName);
        record.setFilePath(recordFilePath);
        return record;
    }
    
    public CallNumDto getCallNumDto(String customerNo, String caller, String callee) {
        return this.ics.interconnectionProcess(customerNo, caller, callee);
    }
    
    public void setCallDtoDefaultValue(CallNumDto dto) {
        if (dto.getCallType() == CallNumDto.CALL_TYPE_DEFAULT) {
            dto.setCallType(this.midwareSettings.isCallTypeTrans() ? CallNumDto.CALL_TYPE_TRANS : CallNumDto.CALL_TYPE_DIRECT);
        }
        if (dto.getTransPrefix() == CallNumDto.TRANS_PREFIX_DEFAULT) {
            dto.setTransPrefix(this.commonSettings.isOrigCalleePrefix() ? CallNumDto.TRANS_PREFIX_ON
                : CallNumDto.TRANS_PREFIX_OFF);
        }
    }
    
    public CallNumDto getDefaultCallNumDto(String originNo, String caller, String callee) {
        CallNumDto dto = new CallNumDto();
        dto.setCallerNo(caller);
        dto.setCalleeNo(callee);
        dto.setOriginalNo(originNo);
        dto.setCallType(this.midwareSettings.isCallTypeTrans() ? CallNumDto.CALL_TYPE_TRANS : CallNumDto.CALL_TYPE_DIRECT);
        dto.setTransPrefix(this.commonSettings.isOrigCalleePrefix() ? CallNumDto.TRANS_PREFIX_ON
            : CallNumDto.TRANS_PREFIX_OFF);
        return dto;
    }
    
    public boolean isOriginalNoPrefix() {
        return this.commonSettings.isOriginalNoPrefix();
    }
    
    public String getlocalNo() {
        return this.commonSettings.getLocalNo();
    }
    
    public boolean isKeepLocalDistrict(String caller) {
		return caller.length() <= 12 && this.commonSettings.isKeepCallerLocalDistrict();
	}
    
    public boolean isAddCallerNationCode() {
		return this.commonSettings.isAddCallerNationCode();
	}
    
    public String getSpecialOrigNo(String customerNo) {
        return this.specialOrigNoMap.get(customerNo);
    }
    
    public boolean isOrigUseNetphone() {
        return this.commonSettings.isOrigUseNetphone();
    }
    
    public int getMakeCallRetryTime() {
        return this.midwareSettings.getMakecallRetrySeconds();
    }
    
    public void decreasePbxCount(BindPhone bindPhone) {
        if (this.commonSettings.isMainServer()) {
            this.businessService.subtractConnect(bindPhone.getId());
        } else {
            this.rmiService.subtractConnect(bindPhone.getCustomerNo(), bindPhone.getLsh());
        }
    }
    
    public String getAgentStateByAgentId(String agentId) {
        if (this.commonSettings.isMainServer()) {
            return this.baseService.getAgentStatusByAgentId(agentId);
        } else {
            return this.rmiService.getAgentStatusByAgentId(agentId);
        }
    }
    
    @Transactional
    public void midwareUpdateAgentStateByAgentId(BindPhone bindPhone, String state, boolean sendMessage) {
        if (this.commonSettings.isMainServer()) {
            this.baseService.modifyAgentStatusByAgentId(bindPhone.getCustomerNo(), bindPhone.getAgentId(), state);
        } else {
            this.rmiService.modifyAgentStatusByAgentId(bindPhone.getCustomerNo(), bindPhone.getAgentId(), state);
        }
        if (sendMessage) {
            log.debug("运营点更新坐席状态：{}，发送socket消息通知中心端。", state);
            SocketMessage message = new AgentMessage(bindPhone.getCustomerNo(), bindPhone.getAgentId(),
                bindPhone.getLsh(), state, bindPhone.getOrigBindPhoneNo(), bindPhone.isNeedLogin());
            User user = this.baseService.getUserByNumber(bindPhone.getCustomerNo());
            if(user.isPlatformPlus()) {
                HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendAgentMessage", message);
            } 
        }
    }
    
    public void endVoiceBox(final TalkNote talkNote, VoiceBox vb) {
        if (vb.isEndFlag()) {
            vb.setInterval(DateTimeUtil.getTimeDifference(vb.getStartTime(), vb.getEndTime()));
        }
        final VoiceBox box = vb;
        this.es.submit(() -> {
            if (talkNote.getId() == null) {
                try {
                    SungoinProcessService.this.businessService.saveOrUpdate(talkNote);
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }
            }
            if (box.getTalkNoteId() == null) {
                box.setTalkNoteId(talkNote.getId());
            }
            SungoinProcessService.this.businessService.saveOrUpdateVoiceBox(box);
            SungoinProcessService.this.businessService.saveTalkTask(new OutDataTask(talkNote.getId(), OutDataType.VOICE_BOX));
        });
    }
    
    public void endRecord(final TalkNote talkNote, VoiceRecord vr, final boolean transMp3) {
        endRecord(talkNote, vr, transMp3, false);
    }
    
    public void endRecord(final TalkNote talkNote, VoiceRecord vr, final boolean transMp3, final boolean jmyFlag) {
        if (vr.isEndFlag()) {
            vr.setInterval(DateTimeUtil.getTimeDifference(vr.getStartTime(), vr.getEndTime()));
        }
        final VoiceRecord record = vr;
        this.es.submit(() -> {
            if (talkNote.getId() == null) {
                try {
                    SungoinProcessService.this.businessService.saveOrUpdate(talkNote);
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }
            }
            if (record.getTalkNoteId() == null) {
                record.setTalkNoteId(talkNote.getId());
            }
            if(jmyFlag && !this.isSipFlag()) {
                try {
                    Thread.sleep(300);
                } catch (Exception e) {
                }
                log.info("基木鱼合成双声道录音：{}", record.getFilePath());
                String[] filePathArr=record.getFilePath().split(",");
                String filePath = filePathArr[0].substring(0,filePathArr[0].lastIndexOf("/"));
                String prefix = record.getFileName().startsWith(MONITOR_PREFIX) ? MONITOR_PREFIX : "";
                String complexFileName = prefix + vr.getCallerNo() + "_" + vr.getStartTime().getTime() + "_LR.mp3";
                String complexFilePath = filePath + "/" + complexFileName;
                
                VoiceUtil.wavToMp3(filePathArr[0], filePathArr[1], complexFilePath);
                record.setFileName(complexFileName);
                record.setFilePath(complexFilePath);
            } else if(transMp3) {
                try {
                    Thread.sleep(300);
                } catch (Exception e) {
                }
				log.info("执行录音转换:{}",record.getFilePath());
				String mp3Path = record.getFilePath().replace("wav", "mp3");
				VoiceUtil.wavToMp3(record.getFilePath(), mp3Path);
			} else {
				log.info("此号码配置了录音文件不转换MP3！");
			}
            SungoinProcessService.this.businessService.saveOrUpdateVoiceRecord(record);
            SungoinProcessService.this.businessService.saveTalkTask(new OutDataTask(talkNote.getId(), OutDataType.VOICE));
        });
    }
    
    @Transactional
    public void endTalkNote(TalkNote talkNote) {
        if (talkNote.isEndFlag() && talkNote.getCalleeOffhookTime() != null) {
            talkNote.setTalkInterval(DateTimeUtil.getTimeDifference(talkNote.getCalleeOffhookTime(),
                talkNote.getOnhookTime()));
        }
        final TalkNote note = talkNote;
        this.es.submit(() -> {
            try {
                SungoinProcessService.this.businessService.saveOrUpdate(note);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
            SungoinProcessService.this.businessService.saveTalkTask(new OutDataTask(note.getId(), OutDataType.TALK));
        });
    }
    
    public void sendSmsMessage(SmsSendDto dto) {
        Message msg = new Message(MqConstants.MESSAGE_TOPIC, MqConstants.SMS_SEND_TAG, JsonHelper.Object2Json(dto).getBytes());
        SendResult result = mqProducer.send(msg);
        log.info("platform sms-messageid:{},topic:{}", result.getMessageId(), result.getTopic());
    }
    
    public void sendMissMessage(User user, BindPhone bindPhone, TalkNote talkNote) {
        SocketMessage message = new CalleeOnhookMessage(bindPhone.getCustomerNo(), bindPhone.getAgentId(),
                bindPhone.getReportNum(), talkNote.getId(), "0");
        if (user.isPlatformPlus()) {
            HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendOnhookMessage", message);
        }
    }
    public BindPhone getLocalBindPhone(String bindPhoneId) {
        return this.baseService.getBindPhone(bindPhoneId);
    }
    
    @Transactional
    public void calleeOnhook(final BindPhone bindPhone, final boolean isOffhook, final boolean isTransCall,
        final TalkNote talkNote, NumberFuncConfig config) {
        User user = this.baseService.getUserByNumber(bindPhone.getCustomerNo());
        final boolean sendMessage = user.getPushStatusFlag();
        if(sendMessage || (config != null && config.isInteractFlag())) {
            //发送挂机消息
            log.debug("{} 发送挂机消息", user.getNumber());
            SocketMessage message = new CalleeOnhookMessage(bindPhone.getCustomerNo(), bindPhone.getAgentId(),
                bindPhone.getReportNum(), talkNote.getId(), (isTransCall ? "1" : "0"));
            if (user.isPlatformPlus()) {
                HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendOnhookMessage", message);
            }
        }
        this.midwareUpdateAgentState(bindPhone, MidwareConstants.AGENT_STATE_IDLE, sendMessage);
    }
    
    @Transactional
    public void calleeOnhook(final BindPhone bindPhone, final TalkNote talkNote, NumberFuncConfig config) {
        this.calleeOnhook(bindPhone, true, true, talkNote, config);
    }
    
    public void decreaseGloablePbxCount(String customerNo, String bindPhone) {
        if (this.commonSettings.isMainServer()) {
            this.businessService.subtractGloableConnect(customerNo, bindPhone);
        } else {
            this.rmiService.subtractConnect(customerNo, bindPhone);
        }
    }
    
    public void midwareUpdateAgentStateByAgentId(String customerNo, String agentId, String state) {
		 if (this.commonSettings.isMainServer()) {
            this.baseService.modifyAgentStatusByAgentId(customerNo, agentId, state);
        } else {
            this.rmiService.modifyAgentStatusByAgentId(customerNo, agentId, state);
        }
	}
    
    public VoiceScore startSatisfy(TalkNote talkNote) {
        VoiceScore vs = new VoiceScore();
        vs.setCalleeNo(talkNote.getCalleeNo());
        vs.setCallerNo(talkNote.getCallerNo());
        vs.setCustomerNo(talkNote.getCustomerNo());
        vs.setGh(talkNote.getGh() + "");
        vs.setStartTime(new Date());
        vs.setTalkNoteId(talkNote.getId());
        this.businessService.saveOrUpdateVoiceScore(vs);
        return vs;
    }
    
    @Transactional(readOnly = true)
    public String getSatiRing(Dept dept) {
        dept = this.baseService.getDept(dept.getId());
        return dept.getSatiRing() != null ? this.midwareSettings.getVocDataPath() + dept.getSatiRing().getFilePath() : this
            .getDefaultRing(MidwareConstants.DEFAULT_RING_SCORE);
    }
    
    public void endVoiceScore(final TalkNote talkNote, VoiceScore vs) {
        vs.setEndTime(new Date());
        final VoiceScore score = vs;
        this.es
            .submit(() -> {
                if (talkNote.getId() == null) {
                    try {
                        SungoinProcessService.this.businessService.saveOrUpdate(talkNote);
                    } catch (Exception ex) {
                        log.error(ex.getMessage(), ex);
                    }
                }
                if (score.getTalkNoteId() == null) {
                    score.setTalkNoteId(talkNote.getId());
                }
                SungoinProcessService.this.businessService.saveOrUpdateVoiceScore(score);
                SungoinProcessService.this.businessService.saveTalkTask(new OutDataTask(score.getTalkNoteId(),
                    OutDataType.VOICE_SCORE));
            });
    }
    
    public String getSupportIvrKey() {
        return this.midwareSettings.getSupportIvrKey();
    }
}
