/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.task;

import com.sungoin.netphoneLocal.business.po.BindPhone;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import com.sungoin.netphoneLocal.midware.service.ProcessService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class OrderFinishTask implements Runnable {

    private static final Logger log = LoggerFactory.getLogger(OrderFinishTask.class);
    
    private final String number;
	private final String bindPhoneId;
    private final String agentId;
    private final int orderTime;
	private final ProcessService service;

    public OrderFinishTask(String number, String bindPhoneId, int orderTime, ProcessService service, String agentId) {
        this.number = number;
        this.bindPhoneId = bindPhoneId;
        this.orderTime = orderTime;
        this.service = service;
        this.agentId = agentId;
    }
    
    @Override
    public void run() {
        log.debug("{}，坐席ID：{}，{}秒整理结束，更新坐席状态为空闲！", number, agentId, orderTime);
        BindPhone bp = null;
        try {
            if(Thread.currentThread().isInterrupted()) {
                log.warn("{}、坐席ID：{}：当前整理任务已被终止", number, agentId);
                return;
            }
            bp = service.getLocalBindPhone(bindPhoneId);
            User user = service.getUserByNumber(number);
            String state = service.getAgentState(bp);
            log.debug("坐席当前的状态：{}", state);
            if(StringUtils.isNotEmpty(user.getUniqueName())) {
                service.midwareBatchUpdateAgentState(bp, MidwareConstants.AGENT_STATE_IDLE, true, user.getUniqueName());
            } else {
                service.midwareUpdateAgentState(bp, MidwareConstants.AGENT_STATE_IDLE, true);
            }
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        } finally {
            service.removeOrder(this.number + this.agentId);
        }
    }
    
}
