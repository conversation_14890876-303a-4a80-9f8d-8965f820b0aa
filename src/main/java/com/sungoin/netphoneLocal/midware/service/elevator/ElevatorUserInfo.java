/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.elevator;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class ElevatorUserInfo {

    private String sos_position_code;
    private String sos_s_person_one;
    private String sos_s_phone_one;
    private String sos_s_person_two;
    private String sos_s_phone_two;
    private String sos_m_person_one;
    private String sos_m_phone_one;
    private String sos_m_person_two;
    private String sos_m_phone_two;
    private String number_info;
    private String project_name;

    public ElevatorUserInfo() {
    }

    public ElevatorUserInfo(Map<String, String> data) {
        this.sos_position_code = data.get("sos_position_code");
        this.sos_s_person_one = data.get("sos_s_person_one");
        this.sos_s_phone_one = data.get("sos_s_phone_one");
        this.sos_s_person_two = data.get("sos_s_person_two");
        this.sos_s_phone_two = data.get("sos_s_phone_two");
        this.sos_m_person_one = data.get("sos_m_person_one");
        this.sos_m_phone_one = data.get("sos_m_phone_one");
        this.sos_m_person_two = data.get("sos_m_person_two");
        this.sos_m_phone_two = data.get("sos_m_phone_two");
        this.number_info = data.get("number_info");
        this.project_name = data.get("project_name");
    }
    
    public List<String> getCalleeList() {
        List<String> list = new ArrayList<>();
        if(StringUtils.isNotEmpty(this.sos_s_phone_one)) {
            list.add(this.sos_s_phone_one);
        }
        if(StringUtils.isNotEmpty(this.sos_m_phone_one)) {
            list.add(this.sos_m_phone_one);
        }
        if(StringUtils.isNotEmpty(this.sos_m_phone_two)) {
            list.add(this.sos_m_phone_two);
        }
        if(StringUtils.isNotEmpty(this.sos_s_phone_two)) {
            list.add(this.sos_s_phone_two);
        }
        return list;
    }
    
    public String getSos_position_code() {
        return sos_position_code;
    }

    public void setSos_position_code(String sos_position_code) {
        this.sos_position_code = sos_position_code;
    }

    public String getSos_s_person_one() {
        return sos_s_person_one;
    }

    public void setSos_s_person_one(String sos_s_person_one) {
        this.sos_s_person_one = sos_s_person_one;
    }

    public String getSos_s_phone_one() {
        return sos_s_phone_one;
    }

    public void setSos_s_phone_one(String sos_s_phone_one) {
        this.sos_s_phone_one = sos_s_phone_one;
    }

    public String getSos_s_person_two() {
        return sos_s_person_two;
    }

    public void setSos_s_person_two(String sos_s_person_two) {
        this.sos_s_person_two = sos_s_person_two;
    }

    public String getSos_s_phone_two() {
        return sos_s_phone_two;
    }

    public void setSos_s_phone_two(String sos_s_phone_two) {
        this.sos_s_phone_two = sos_s_phone_two;
    }

    public String getSos_m_person_one() {
        return sos_m_person_one;
    }

    public void setSos_m_person_one(String sos_m_person_one) {
        this.sos_m_person_one = sos_m_person_one;
    }

    public String getSos_m_phone_one() {
        return sos_m_phone_one;
    }

    public void setSos_m_phone_one(String sos_m_phone_one) {
        this.sos_m_phone_one = sos_m_phone_one;
    }

    public String getSos_m_person_two() {
        return sos_m_person_two;
    }

    public void setSos_m_person_two(String sos_m_person_two) {
        this.sos_m_person_two = sos_m_person_two;
    }

    public String getSos_m_phone_two() {
        return sos_m_phone_two;
    }

    public void setSos_m_phone_two(String sos_m_phone_two) {
        this.sos_m_phone_two = sos_m_phone_two;
    }

    public String getNumber_info() {
        return number_info;
    }

    public void setNumber_info(String number_info) {
        this.number_info = number_info;
    }

    public String getProject_name() {
        return project_name;
    }

    public void setProject_name(String project_name) {
        this.project_name = project_name;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("ElevatorUserInfo{");
        sb.append("sos_position_code=").append(sos_position_code);
        sb.append(", sos_s_person_one=").append(sos_s_person_one);
        sb.append(", sos_s_phone_one=").append(sos_s_phone_one);
        sb.append(", sos_s_person_two=").append(sos_s_person_two);
        sb.append(", sos_s_phone_two=").append(sos_s_phone_two);
        sb.append(", sos_m_person_one=").append(sos_m_person_one);
        sb.append(", sos_m_phone_one=").append(sos_m_phone_one);
        sb.append(", sos_m_person_two=").append(sos_m_person_two);
        sb.append(", sos_m_phone_two=").append(sos_m_phone_two);
        sb.append(", project_name=").append(project_name);
        sb.append(", number_info=").append(number_info);
        sb.append('}');
        return sb.toString();
    }
}
