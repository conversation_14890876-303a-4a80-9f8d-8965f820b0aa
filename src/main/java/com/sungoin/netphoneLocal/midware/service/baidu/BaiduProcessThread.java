/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.midware.service.baidu;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.netphoneLocal.business.bean.CallNumDto;
import com.sungoin.netphoneLocal.business.bean.baidu.BaiduResult;
import com.sungoin.netphoneLocal.business.po.NumberFuncConfig;
import com.sungoin.netphoneLocal.business.po.TalkNote;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.business.po.VoiceRecord;
import com.sungoin.netphoneLocal.constants.CallOutErrorCode;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE_CALLER;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.MAIN_CALL_THREAD;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.TALK_TYPE_IVR_CON;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.TALK_TYPE_IVR_NOT_CON;
import com.sungoin.netphoneLocal.midware.exception.MidwareException;
import com.sungoin.netphoneLocal.util.CtiDes;
import com.sungoin.netphoneLocal.util.DateTimeUtil;
import com.sungoin.netphoneLocal.util.SpringHelper;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.util.Date;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.sungoin.netphoneLocal.midware.service.EventHandler;

/**
 *
 * <AUTHOR>
 */
public class BaiduProcessThread extends TeleThread implements EventHandler {
    private static final Logger log = LoggerFactory.getLogger(BaiduProcessThread.class);
    private transient BaiduProcessService service;//序列化时忽略此域，反序列化时手动赋值
    
    private final String logPrefix;
    private final NumberFuncConfig functionConfig;
    private final User user;
    private volatile Call mainCall;
    private volatile Call agent;
    private volatile TalkNote talkNote;
    private volatile VoiceRecord voiceRecord;
    private String fixedCallerNo;//处理过的主叫号码
    private String fixedCalleeNo;//处理过的被叫号码
    private volatile int talkType = TALK_TYPE_IVR_NOT_CON;
    
    //异步呼叫控制
    private final String makeCallSync = new String();
    private volatile boolean isMakingCall = false;
    private volatile boolean isCallOver = false;
    private boolean isCallSuccess = false;
    private boolean isRejectCall = false;
    private volatile boolean isCallConnected = false;//是否呼通的标识
    private int callErrorCode = -1;
    
    //IVR导航异步收码控制
    private final String ivrSync = new String();
    private boolean isIvrOver;
    private StringBuffer dtmf;
    
    //百度接口返回dto
    private BaiduResult ivrResult;
    private BaiduResult bindResult;
    private String phoneNums;
    private String currentNum;
    
    /**
     * 反序列化时给transient域service赋值
     *
     * @param in
     * @throws IOException
     * @throws ClassNotFoundException
     */
    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        in.defaultReadObject();
        this.service = SpringHelper.getBean(BaiduProcessService.class);
    }
    
    public BaiduProcessThread(Call mainCall, User user, TeleThreadEventListener listener, NumberFuncConfig config) {
        super(listener);
        Date now = new Date();
        this.user = user;
        this.mainCall = mainCall;
        this.logPrefix = mainCall.getCaller() + "-" + user.getNumber() + "-" + DateTimeUtil.formatTime(now);
        this.talkNote = new TalkNote(user.getNumber(), user.getOriginalNo(), null, now);
        this.talkNote.setUserField("");
        this.functionConfig = config;
        this.init();
    }
    
    private void init() {
        this.service = SpringHelper.getBean(BaiduProcessService.class);
        this.mainCall.attachTeleThread(this);
        this.mainCall.setParam(CALL_TYPE, CALL_TYPE_CALLER);
        this.mainCall.setParam(MAIN_CALL_THREAD, this);
    }
    
    private void debug(String string, Object... os) {
        log.debug(this.logPrefix + string, os);
    }

    private void info(String string, Object... os) {
        log.info(this.logPrefix + string, os);
    }

    private void warn(String string, Object... os) {
        log.warn(this.logPrefix + string, os);
    }

    private void error(String string, Object... os) {
        log.error(this.logPrefix + string, os);
    }

    private void errorStackTrace(String string, Throwable throwable) {
        log.error(this.logPrefix + string, throwable);
    }

    public String getLogPrefix() {
        return this.logPrefix;
    }
    
     @Override
    public void run() {
        try {
            this.debug("开始百度业务流程！");
            int ret = this.answerOrAlert();
            if (this.user.isPreOffhook()) {
                //如果在摘机前，主叫已经挂机了，防止摘机时间晚于挂机时间，直接取挂机时间
                Date offHookTime = this.talkNote.getOnhookTime() == null ? new Date() : this.talkNote.getOnhookTime();
                this.talkNote.setCallerOffhookTime(offHookTime);
            }
            //处理主叫号码，补上区号
            this.fixedCallerNo = this.service.getFixedCallerNo(this.mainCall.getCaller());
            this.debug("处理过后的主叫号码：{}", this.fixedCallerNo);
            this.talkNote.setCallerNo(this.getTalkNoteCaller());
            this.service.saveOrUpdateTalkNote(this.talkNote, false);
            this.debug("保存话单成功！");
            if (ret != Constants.RET_SUCCESS) {
                throw new MidwareException(this.logPrefix + "对主叫：" + this.mainCall.toString() + "摘机失败！");
            }
            //获取百度导航信息
            this.ivrResult = this.service.getIvrInfo(talkNote, null);
            this.debug("获取到百度导航信息：{}", ivrResult);
            if(this.ivrResult == null) {
                this.warn("未获取到导航信息，挂断主叫！");
                this.callErrorCode = CallOutErrorCode.API_ERROR;
                this.mainCall.onHook();
                return;
            }
            if(StringUtils.isNotEmpty(ivrResult.getUserField())) {
                this.talkNote.setUserField(ivrResult.getUserField());
                this.service.saveOrUpdateTalkNote(this.talkNote);
            }
            switch(ivrResult.getResult()) {
                case BaiduResult.IVR_RESULT_ERROR: 
                    this.debug("进入异常流程...");
                    this.ivrError();
                    break;
                case BaiduResult.IVR_RESULT_NONE:
                    this.debug("进入无分组流程流程...");
                    this.processNoIvr();
                    break;
                case BaiduResult.IVR_RESULT_HAS: 
                    this.debug("进入有分组流程...");
                    this.processIvr();
                    break;
                default:
                    this.debug("进入默认流程...");
                    this.ivrError();
                    break;
            }
        } catch (Exception ex) {
            this.errorStackTrace(ex.getMessage(), ex);
            this.warn("程序异常，挂断主被叫");
            hangupBothCall();
        } finally {
            this.finished();
            this.debug("主线程结束！");
        }
    }
    private String getCallStateDesc(int callState) {
        switch (callState) {
            case 0:
                return "空闲";
            case 1:
                return "呼叫中";
            case 2:
                return "振铃";
            case 3:
                return "通话中";
            default:
                return "其他";
        }
    }
    
    /**
     * 保存话单中的主叫号码 规则1.如果是手机，只保存11位真正手机号。 规则2.如果是固话，保存区号+固话
     *
     * @return
     */
    private String getTalkNoteCaller() {
        String caller = this.mainCall.getCaller();
        if (caller.startsWith("1") && caller.length() == 11) {
            //mobile,not change
        } else if (caller.startsWith("01") && caller.charAt(2) != '0') {
            //0+mobile, substring(1)
            caller = caller.substring(1);
        } else {
            //固话
            caller = this.fixedCallerNo;
        }
        try {
            caller = CtiDes.getInstance().encrypt(caller);
        } catch (Exception ex) {
            this.error(ex.getMessage(), ex);
        }
        return caller;
    }
    
    private int answerOrAlert() {
        this.debug("用户开通了预摘机功能！对来电摘机！");
        this.mainCall.answerSync(1000);
        this.debug("对来电摘机！来电状态为：" + this.getCallStateDesc(this.mainCall.getState()));
        return this.mainCall.getState() == Constants.CALL_CONTENT ? 1 : 0;
    }
    
    public void hangupBothCall() {
        if (this.mainCall.getState() != Constants.CALL_IDLE) {
            this.info("挂断主叫");
            this.mainCall.onHook();
        }
        handupAgent();
    }
    
    public void handupAgent() {
        if (this.isMakingCall) {
            this.debug("终止呼叫坐席！");
            synchronized (this.makeCallSync) {
                this.isCallOver = true;
                this.isCallSuccess = false;
                this.isRejectCall = true;
                this.makeCallSync.notifyAll();
            }
        } else {
            if (this.agent != null && this.agent.getState() == Constants.CALL_CONTENT) {
                this.debug("挂断坐席！");
                this.agent.onHook();
            }
        }
    }
    
    private void ivrError() {
        //异常, 播放welcomeContent并挂断
        String msg = ivrResult.getErrorMsg();
        if(msg.length() > 0) {
            String ttsPath = this.service.getTtsFilePath(this.user.getNumber(), msg);
            this.debug("根据内容：{}获取到的语音文件路径：{}", msg, ttsPath);
            this.mainCall.play(ttsPath, false, false, false, 30, 0);
        }
        this.mainCall.onHook();
    }
    
    private String getBgRing() {
        return this.service.getDefaultRing("200000000.wav");
    }
    
    private void processNoIvr() {
        //无分组配置 播放welcomeContent并转接坐席
        this.phoneNums = ivrResult.getDestNum();
        log.debug("无分组流程，绑定号码：{}", this.phoneNums);
        if(StringUtils.isEmpty(this.phoneNums)) {
            log.warn("绑定号码为空！播放欢迎语后挂断！");
            this.callErrorCode = CallOutErrorCode.NO_IDLE_BINDPHONE;
            this.ivrError();
        } else {
            String msg = ivrResult.getWelcomeContent();
            String ttsPath = this.service.getTtsFilePath(this.user.getNumber(), msg);
            String bgRing = this.getBgRing();
            String files = ttsPath + "," + bgRing;
            this.debug("要播放的文件路径：{}", files);
            this.mainCall.play(files, false, true, true, 0, 0);
            this.processMakeCall();
        }
    }
    
    private void processMakeCall() {
        if (this.mainCall.getState() == Constants.CALL_IDLE) {
            throw new MidwareException("主叫已挂机！");
        }
        String[] phoneArray = this.phoneNums.split(",");
        for(String s : phoneArray) {
            if(StringUtils.isEmpty(s)) {
                continue;
            }
            this.currentNum = s;
            
            Call call = this.makeCall();
            if(call != null) {
                this.agent = call;
                break;
            }
        }
        if (this.agent != null) {
            initCallParams(this.agent, MidwareConstants.CALL_TYPE_CALLEE);
            this.calleeOffHook();
            this.processRecord();
            boolean connected = this.connectCall(this.mainCall, this.agent);
            if (!connected) {
                this.warn("连接主被叫失败！当前主叫状态：{}， 当前被叫状态：{}", getCallStateDesc(this.mainCall.getState()), getCallStateDesc(this.agent.getState()));
                throw new MidwareException("连接主被叫失败！");
            }
            this.isCallConnected = true;
        } else {
            this.callFail();
        }
    }
    
    private void callFail() {
        this.debug("呼叫失败！挂断主叫");
        this.mainCall.onHook();
    }
    
    private Call makeCall() {
        if (this.mainCall.getState() == Constants.CALL_IDLE) {
            throw new MidwareException("主叫已挂机！");
        }
        if(this.service.isInTest()) {
            //测试环境外地手机不补0
            this.fixedCalleeNo = this.currentNum;
        } else {
            this.fixedCalleeNo = this.service.getFixedCalleeNo(this.currentNum);
        }
        log.debug("处理过后的被叫号码：{}", this.fixedCalleeNo);
        CallNumDto callNumDto = this.service.getDefaultCallNumDto(this.user.getOriginalNo(), this.getDefaultRuleCaller(),
                    this.fixedCalleeNo);
        long begin = System.currentTimeMillis();
        this.debug("开始呼叫坐席：主叫号码：{},被叫号码：{},开始时间：{},超时时间：{}", callNumDto.getCallerNo(), callNumDto.getCalleeNo(), begin, 30);
        Call callee = this.asyncMakeCall(callNumDto, 30);
        long end = System.currentTimeMillis();
        long callTime = (end - begin) / 1000;
        this.debug("呼叫结束，呼叫时间：{}，呼叫结果：{}", callTime, this.isCallSuccess);
        return callee;
    }
    
    private Call asyncMakeCall(CallNumDto dto, long timeoutSeconds) {
        if (this.mainCall.getState() == Constants.CALL_IDLE) {
            this.error("主叫已挂机！");
            return null;
        }
        String originalNo = null;
        //处理号码功能配置
        if(this.functionConfig != null && (StringUtils.contains(this.functionConfig.getAccessShortNum(), this.mainCall.getCaller()) || this.functionConfig.containsShortNum(this.mainCall.getCaller()))) {
            this.debug("主叫号码：{} 采用号码配置功能配置的短号码外显：{}", this.mainCall.getCaller(), this.functionConfig.getShortShowNum());
            dto.setCallerNo(this.functionConfig.getShortShowNum());
        } else {
            originalNo = dto.getCallType() == CallNumDto.CALL_TYPE_TRANS ? dto.getOriginalNo() : null;
            if (originalNo != null && dto.getTransPrefix() == CallNumDto.TRANS_PREFIX_ON) {
                originalNo = this.service.getlocalNo() + originalNo;
            }
            //判断是否用400号码作为原始被叫及改发号码
            if (originalNo != null && this.service.isOrigUseNetphone()) {
                this.info("原始小号使用400号码");
                originalNo = this.user.getNumber();
            }
        }
        Call call = null;
        try {
            synchronized (this.makeCallSync) {
                this.isMakingCall = true;
                this.isCallOver = false;
                this.isCallSuccess = false;
                this.isRejectCall = false;
                if (this.mainCall.getState() == Constants.CALL_IDLE) {
                    this.error("主叫已挂机！");
                    return null;
                }
                call = this.manager.makeCall(dto.getCallerNo(), dto.getCalleeNo(), originalNo, true, timeoutSeconds,
                        this.getName());
                if (call == null) {
                    return call;
                }
                call.setParam(MAIN_CALL_THREAD, this);
                long begin = System.currentTimeMillis();
                while (!this.isCallOver) {
                    this.makeCallSync.wait(timeoutSeconds * 1000);
                    this.info("主线程被唤醒...isCallOver={}, isCallSuccess={}", this.isCallOver, this.isCallSuccess);
                    if (this.isRejectCall
                            || (!this.isCallOver && System.currentTimeMillis() - begin >= timeoutSeconds * 1000)) {
                        this.warn((this.isRejectCall ? "用户拒接" : "超时") + "，终止呼叫！");
                        this.manager.stopMakeCall(call.getDeviceId(), call.getLsh());
                        this.isCallSuccess = false;
                        this.isCallOver = true;
                    }
                }
                this.isMakingCall = false;
            }
            return this.isCallSuccess ? call : null;
        } catch (Exception ex) {
            this.errorStackTrace(ex.getMessage(), ex);
            return null;
        }
    }
    
    private boolean connectCall(Call caller, Call callee) {
        if (caller.getState() == Constants.CALL_IDLE || callee.getState() == Constants.CALL_IDLE) {
            this.warn("主被叫有一方挂机！退出连接！");
            return false;
        }
        int ret = caller.connectCall(callee, false);
        if (ret != Constants.RET_SUCCESS) {
            this.warn("连接主被叫失败！主叫状态：{}，被叫状态：{}，等待一秒后重新尝试一次", this.getCallStateDesc(caller.getState()),
                    this.getCallStateDesc(callee.getState()));
            this.await(1000);
            ret = caller.connectCall(callee, false);
            if (ret != Constants.RET_SUCCESS) {
                if (caller.getState() == Constants.CALL_ALERT && callee.getState() == Constants.CALL_CONTENT) {
                    this.await(1000);
                    ret = caller.connectCall(callee, false);
                }
                if (ret != Constants.RET_SUCCESS) {
                    this.error("重新连接主被叫失败！主叫状态：{}，被叫状态：{}", this.getCallStateDesc(caller.getState()),
                            this.getCallStateDesc(callee.getState()));
                }
            }
        }
        return ret == Constants.RET_SUCCESS;
    }
    
    private void processRecord() {
        this.mainCall.stopPlay();
        this.await(200);
        this.voiceRecord = this.service.startRecord(this.user, this.talkNote.getCallerNo(), this.fixedCalleeNo);
        this.voiceRecord.setTalkNoteId(this.talkNote.getId());
        this.service.saveOrUpdateRecord(this.voiceRecord);
        String recordPath = this.voiceRecord.getFilePath();
        this.debug("用户的录音路径：{}", recordPath);
        String[] records = recordPath.split(",");
        this.mainCall.record(records[0], 0, false);
        this.agent.record(records[1], 0, false);
    }
    
    private void processIvr() {
        //有分组配置, 播放welcomeContent+ivrPlayContent
        String ivrMsg = ivrResult.getIvrPlayContent();
        log.debug("有分组流程，ivr内容：{}", ivrMsg);
        String welcomePath = this.service.getTtsFilePath(this.user.getNumber(), ivrResult.getWelcomeContent());
        String ivrPath = this.service.getTtsFilePath(this.user.getNumber(), ivrMsg);
        String welcomeFiles = welcomePath + "," + ivrPath;
        this.debug("要播放的文件路径：{}", welcomeFiles);
        this.mainCall.play(welcomeFiles, false, true, true, 0, 0);
        String key = this.receiveIvrDtmf();
        this.debug("收到的用户按键：{}", key);
        int retryCount = 0;
        while(StringUtils.isEmpty(key) && this.mainCall.getState() != Constants.CALL_IDLE && ++ retryCount < 4) {
            this.warn("用户未输入按键，第" + retryCount + "次重试！");
            this.mainCall.play(welcomeFiles, false, true, true, 0, 0);
            key = this.receiveIvrDtmf();
        }
        if(StringUtils.isEmpty(key)) {
            this.warn("用户未输入按键，挂断主叫！");
            this.mainCall.onHook();
            return;
        }
        this.bindResult = this.service.getBindInfo(talkNote, key);
        this.debug("获取到绑定号码信息：{}", this.bindResult);
        if(this.bindResult == null) {
            this.warn("未获取到被叫号码，挂断电话！");
            this.callErrorCode = CallOutErrorCode.NO_IDLE_BINDPHONE;
            this.mainCall.onHook();
            return;
        }
        if(bindResult.getResult().equals("-1") || StringUtils.isEmpty(bindResult.getDestNum())) {
            this.warn("接口返回异常，播放欢迎语并挂断！");
            String msg = bindResult.getErrorMsg();
            String msgPath = null;
            if(msg.length() > 0) {
                msgPath = this.service.getTtsFilePath(this.user.getNumber(), msg);
                this.debug("播放的欢迎语地址：{}", msgPath);
                this.mainCall.play(msgPath, false, false, false, 30, 0);
            }
            this.callErrorCode = CallOutErrorCode.API_ERROR;
            this.mainCall.onHook();
            return;
        }
        this.mainCall.stopPlay();
        this.phoneNums = bindResult.getDestNum();
        String transferMsg = this.ivrResult.getTransferringIVRContent();
        this.debug("获取到转接提示语：{}", transferMsg);
        if(StringUtils.isNotEmpty(transferMsg)) {
            String transferPath = this.service.getTtsFilePath(this.user.getNumber(), transferMsg);
            String bgRing = this.getBgRing();
            String transferFiles = transferPath + "," + bgRing;
            this.debug("要播放的文件路径：{}", transferFiles);
            this.mainCall.play(transferFiles, false, true, true, 0, 0);
        } else {
            String bgRing = this.getBgRing();
            this.debug("要播放的文件路径：{}", bgRing);
            this.mainCall.play(bgRing, false, false, true, 0, 0);
        }
        this.processMakeCall();
    }
    
    private String receiveIvrDtmf() {
        int timeoutSecond = 30;
        String returnKey = null;
        if (this.functionConfig != null) {
            if(functionConfig.getInputTimeOut() != null) {
                timeoutSecond = functionConfig.getInputTimeOut();
                this.debug("号码配置了按键收码等待时长：{}", timeoutSecond);
            }
            if(functionConfig.getInputTimeOutDtmf() != null) {
                returnKey = functionConfig.getInputTimeOutDtmf() + "";
                this.debug("号码配置了按键收码超时返回：{}", returnKey);
            }
        }
        this.debug("发送收码请求到cti-server，开始收码。。。");
        return this.receiveIvrDtmf(timeoutSecond, returnKey);
    }
    
    private String receiveIvrDtmf(int timeoutSecond, String timeoutReturnKey) {
        try {
            this.dtmf = new StringBuffer();
            this.mainCall.receiveDTMFAsync();
            synchronized (this.ivrSync) {
                this.isIvrOver = false;
                while (!this.isIvrOver) {
                    this.ivrSync.wait(timeoutSecond * 1000);
                    if (this.mainCall.getState() != Constants.CALL_CONTENT) {
                        this.warn("收码异常！主叫挂机！");
                        return null;
                    }
                    if (!this.isIvrOver) {
                        this.warn("ivr 收码超时！");
                        this.mainCall.stopReceiveDTMF();
                        return timeoutReturnKey;
                    }
                }
            }
            this.mainCall.stopReceiveDTMF();
            return this.dtmf.toString();
        } catch (Exception ex) {
            this.errorStackTrace(ex.getMessage(), ex);
            return null;
        }
    }

    private void calleeOffHook() {
        Date now = new Date();
        if (!this.user.isPreOffhook()) {
            this.mainCall.answerSync(500);
            this.talkNote.setCallerOffhookTime(now);
        }
        this.talkType = TALK_TYPE_IVR_CON;
        this.talkNote.setTalkType(this.talkType);
        this.talkNote.setCalleeNo(this.currentNum);
        this.talkNote.setCalleeOffhookTime(now);
        this.service.saveOrUpdateTalkNote(this.talkNote);
        this.debug("被叫摘机，被叫号码：{},摘机时间：{}", this.fixedCalleeNo, this.talkNote.getCalleeOffhookTime());
    }
    
    private void initCallParams(Call callee, String callType) {
        callee.attachTeleThread(this);
        callee.setParam(CALL_TYPE, callType);
    }
    
    private String getDefaultRuleCaller() {
        String localAddress = this.service.getlocalNo();
        String remoteAddress = null;//外地手机的区号
        String originalNo = this.user.getOriginalNo();
        //测试环境配置关闭透传,生产环境无次配置
        if(this.service.isInTest()) {
            String caller = this.service.isOriginalNoPrefix() ? localAddress + originalNo : originalNo;
            return caller;
        }
        //判断是否是国外号码
        if (this.mainCall.getCaller().startsWith("00") && this.service.isForeignCallerAccess(this.user.getNumber())) {
            String caller = this.service.isOriginalNoPrefix() ? localAddress + originalNo : originalNo;
            this.info("国外主叫号码直接改成小号{}", caller);
            return caller;
        }
        String caller = this.fixedCallerNo;
        if (caller.length() > 12) {
            remoteAddress = caller.substring(0, caller.length() - 11);
        }
        if (caller.startsWith(localAddress) && !this.service.isKeepLocalDistrict(caller)) {
            //透传号码的主叫去掉本地区号
            caller = caller.substring(localAddress.length());
        }
        //主叫号码如果是外地手机，改成“0” + 手机号码，默认fixedCallerNo是区号 + 手机号码
        if (remoteAddress != null && !remoteAddress.equals(localAddress)) {
            caller = caller.substring(remoteAddress.length());
            if (!this.service.isAddCallerNationCode()) {
                caller = "0" + caller;
            }
        }
        if (this.service.isAddCallerNationCode()) {
            caller = "0086" + caller;
        }
        return caller;
    }
    
    @Override
    public void callerOnhook() {
        this.debug("主叫挂机！");
        if (this.isMakingCall) {
            this.debug("正在呼叫被叫，终止呼叫。。。");
            this.handupAgent();
        }
        synchronized (this.ivrSync) {
            this.ivrSync.notifyAll();
		}
        Date now = new Date();
        if (this.voiceRecord != null) {
            this.voiceRecord.setEndTime(now);
            this.voiceRecord.setEndFlag(true);
            this.service.endRecord(this.talkNote, this.voiceRecord, true);
        }
        if (this.talkNote.getCallerOnHook() == null) {
            this.talkNote.setCallerOnHook(true);
        }
        this.talkNote.setEndFlag(true);
        this.talkNote.setOnhookTime(now);
        if(!isCallConnected) {
            //未接原因
            if(this.callErrorCode < 0) {
                this.callErrorCode = CallOutErrorCode.MAINCALL_ONHOOK;
            }
            this.talkNote.setErrorCode(callErrorCode);
        }
        if(StringUtils.isEmpty(this.talkNote.getCalleeNo()) && StringUtils.isNotEmpty(this.phoneNums)) {
            //如果未呼通，保存第一个坐席号码
            this.talkNote.setCalleeNo(this.phoneNums.split(",")[0]);
        }
        if(this.talkNote.getCallerNo() == null) {
            this.fixedCallerNo = this.service.getFixedCallerNo(this.mainCall.getCaller());
            this.talkNote.setCallerNo(this.getTalkNoteCaller());
        }
        this.service.endTalkNote(this.talkNote);
        this.mainCall.getAllParam().clear();
        this.handupAgent();
    }

    @Override
    public void calleeOnhook() {
        this.debug("坐席挂机");
        this.mainCall.stopRecord();
        if (this.talkNote.getCallerOnHook() == null) {
            this.talkNote.setCallerOnHook(false);
        }
        this.agent.getAllParam().clear();
        if (this.mainCall.getState() != Constants.CALL_IDLE) {
            this.info("挂断主叫");
            this.mainCall.onHook();
        }
    }

    @Override
    public void transOnhook(Call trans) {
        this.warn("转移坐席挂机！不支持的事件");
    }

    @Override
    public void callOver(int status, int errorCode) {
        this.debug("呼叫结束！呼叫结果：{}，失败原因：{}", status, errorCode);
        synchronized (this.makeCallSync) {
            if (this.isMakingCall) {
                this.isCallOver = true;
                this.isCallSuccess = (status == 1);
                this.callErrorCode = errorCode;
                this.makeCallSync.notifyAll();
                this.debug("唤醒等待线程！");
            }
        }
    }

    @Override
    public void ivrReceived(String key) {
        synchronized (this.ivrSync) {
			this.dtmf.append(key);
            this.isIvrOver = true;
            this.ivrSync.notifyAll();
		}
    }

    @Override
    public void dtmfTrans() {
        this.warn("按键转接！不支持的事件");
    }

    @Override
    public void cdr(String record) {
        this.debug("收到cdr事件，录音地址：{}", record);
        if(this.voiceRecord != null) {
            this.voiceRecord.setFilePath(record);
            this.voiceRecord.setFileName(record.substring(record.lastIndexOf("/") + 1));
            this.service.saveOrUpdateRecord(voiceRecord);
        }
    }

    @Override
    public void detectSpeech(String text) {
        this.warn("语音识别，不支持的事件");
    }

    @Override
    public void dtmfToIvr() {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }
}
