/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.sungoin;

import com.alibaba.fastjson.JSON;
import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.netphoneLocal.business.bean.CallNumDto;
import com.sungoin.netphoneLocal.business.mq.SmsSendDto;
import com.sungoin.netphoneLocal.business.po.BindPhone;
import com.sungoin.netphoneLocal.business.po.CrbtRing;
import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.business.po.NumberFuncConfig;
import com.sungoin.netphoneLocal.business.po.TalkNote;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.business.po.VoiceBox;
import com.sungoin.netphoneLocal.business.po.VoiceRecord;
import com.sungoin.netphoneLocal.business.po.VoiceScore;
import com.sungoin.netphoneLocal.config.SungoinSetting;
import com.sungoin.netphoneLocal.constants.CallOutErrorCode;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.AGENT_STATE_CALLING;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.AGENT_STATE_CONNECT;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.AGENT_STATE_IDLE;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_AGENT_ID;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_BINDPHONE_ID;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_BINDPHONE_LSH;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE_CALLER;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.DEFAULT_RING_DUDU;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.DEFAULT_RING_FAIL;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.DEFAULT_RING_VOICEBOX;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.MAIN_CALL_THREAD;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.RECEIVED_DTMF;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.RECEIVED_DTMF_TIME;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.TALK_TYPE_IVR_CON;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.TALK_TYPE_IVR_NOT_CON;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.TALK_TYPE_VOICE_BOX;
import com.sungoin.netphoneLocal.midware.exception.MidwareException;
import com.sungoin.netphoneLocal.midware.service.EventHandler;
import com.sungoin.netphoneLocal.socket.message.CalleeOnhookMessage;
import com.sungoin.netphoneLocal.util.CacheUtil;
import com.sungoin.netphoneLocal.util.CtiDes;
import com.sungoin.netphoneLocal.util.DateTimeUtil;
import com.sungoin.netphoneLocal.util.MyStringUtil;
import com.sungoin.netphoneLocal.util.SpringHelper;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

/**
 *
 * <AUTHOR>
 */
public class SungoinProcessThread extends TeleThread implements EventHandler {
    
    private static final Logger log = LoggerFactory.getLogger(SungoinProcessThread.class);
    private transient SungoinProcessService service;//序列化时忽略此域，反序列化时手动赋值
    private transient SungoinSetting setting;//序列化时忽略此域，反序列化时手动赋值
    
    private final String logPrefix;
    private final NumberFuncConfig functionConfig;
    private volatile List<Call> transList;
    private final User user;
    private volatile Dept currentDept;
    private volatile Call mainCall;
    private volatile Call agent;
    private volatile BindPhone currentBindPhone;
    private volatile TalkNote talkNote;
    private volatile VoiceBox voiceBox;
    private volatile VoiceRecord voiceRecord;
    private volatile VoiceScore voiceScore;
    private String fixedCallerNo;//处理过的主叫号码
    private String fixedCalleeNo;//处理过的被叫号码
    private volatile int talkType = TALK_TYPE_IVR_NOT_CON;
    private String currentRing;
    private volatile CrbtRing currentCrbtRing;
    private boolean ringToVoice = false;
    private boolean ringToOnHook = false;
    private List<BindPhone> agents;
    private BindPhone missedBindPhone;
    //呼叫轨迹相关字段
    private String districtDesc = null;//归属地
    
    private boolean dualChannel = false;//双声道录音标识

    private boolean interactFlag = false;//接口客户标识
    
    //异步呼叫控制
    private final String makeCallSync = new String();
    private volatile boolean isMakingCall = false;
    private volatile boolean isCallOver = false;
    private boolean isCallSuccess = false;
    private boolean isRejectCall = false;
    private volatile boolean isCallConnected = false;//是否呼通的标识
    private volatile int callErrorCode = -1;

    private boolean isIvrError = false;
    private String ivrErrorTip = null;
    //主叫挂机标识
    private AtomicBoolean callerOnhooked = new AtomicBoolean(false);
    private volatile boolean isPlayingNumber = false;
    
    private volatile String apiCallee;
    /**
     * 反序列化时给transient域service赋值
     *
     * @param in
     * @throws IOException
     * @throws ClassNotFoundException
     */
    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        in.defaultReadObject();
        this.service = SpringHelper.getBean(SungoinProcessService.class);
        this.setting = SpringHelper.getBean(SungoinSetting.class);
    }

    public SungoinProcessThread(Call mainCall, User user, TeleThreadEventListener listener, NumberFuncConfig config) {
        super(listener);
        Date now = new Date();
        this.user = user;
        this.mainCall = mainCall;
        this.logPrefix = mainCall.getCaller() + "-" + user.getNumber() + "-" + DateTimeUtil.formatTime(now);
        this.talkNote = new TalkNote(user.getNumber(), user.getOriginalNo(), null, now);
        this.transList = new ArrayList<>();
        this.functionConfig = config;
        this.dualChannel = user.isBaiduJimuyu() || (config != null && config.isRecordChannelFlag());
        this.interactFlag= (config != null && config.isInteractFlag());
        this.init();
    }
    
    private void init() {
        this.service = SpringHelper.getBean(SungoinProcessService.class);
        this.setting = SpringHelper.getBean(SungoinSetting.class);
        this.mainCall.attachTeleThread(this);
        this.mainCall.setParam(CALL_TYPE, CALL_TYPE_CALLER);
        this.mainCall.setParam(MAIN_CALL_THREAD, this);
    }
    
    private void debug(String string, Object... os) {
        log.debug(this.logPrefix + string, os);
    }

    private void info(String string, Object... os) {
        log.info(this.logPrefix + string, os);
    }

    private void warn(String string, Object... os) {
        log.warn(this.logPrefix + string, os);
    }

    private void error(String string, Object... os) {
        log.error(this.logPrefix + string, os);
    }

    private void errorStackTrace(String string, Throwable throwable) {
        log.error(this.logPrefix + string, throwable);
    }

    public String getLogPrefix() {
        return this.logPrefix;
    }

    @Override
    public void run() {
        try {
            MDC.put(Constants.TRACE_ID, mainCall.getTraceId());
            this.debug("开始尚景业务流程！");
            int ret = this.answerOrAlert();
            if (this.user.isPreOffhook()) {
                //如果在摘机前，主叫已经挂机了，防止摘机时间晚于挂机时间，直接取挂机时间
                Date offHookTime = this.talkNote.getOnhookTime() == null ? new Date() : this.talkNote.getOnhookTime();
                this.talkNote.setCallerOffhookTime(offHookTime);
            }
            //处理主叫号码，补上区号
            this.fixedCallerNo = this.service.getFixedCallerNo(this.mainCall.getCaller());
            this.debug("处理过后的主叫号码：{}", this.fixedCallerNo);
            this.districtDesc = this.service.getDistrictDesc(this.fixedCallerNo);
            this.debug("主叫号码来电归属地：{}", this.districtDesc);
            this.talkNote.setCallerNo(this.getTalkNoteCaller());
            this.service.saveOrUpdateTalkNote(this.talkNote);
            this.debug("保存话单成功！");
            if (ret != Constants.RET_SUCCESS) {
                throw new MidwareException(this.logPrefix + "对主叫：" + this.mainCall.toString() + "摘机失败！");
            }
            //播放根部门彩铃
            this.currentDept = this.service.getRootDept(this.user);
            this.debug("{}:根部门获取成功", this.user.getNumber());
            this.currentCrbtRing = this.service.getCrbtRingFile(this.currentDept);
            this.ringToVoice = currentCrbtRing == null ? false : currentCrbtRing.isToVoice();
            this.ringToOnHook = currentCrbtRing == null ? false : currentCrbtRing.isOnHook();
            if (this.ringToVoice) {
                //呼叫轨迹：转入炫铃
                ret = this.playCurrentDeptRing(false, false, false);
                this.debug("炫铃进留言，放音返回：{}", ret);
                this.leaveMessage();
                return;
            } else if (this.ringToOnHook) {
                //呼叫轨迹：转入炫铃
                ret = this.playCurrentDeptRing(false, false, false);
                this.debug("炫铃播放完主叫挂机，放音返回：{}", ret);
                this.mainCall.onHook();
                return;
            } else {
                this.info("开始播放根部门企业宣传彩铃。。。");
                this.playDeptCompanyRing(this.currentDept);
                if(isSmartBroadOpen(this.currentDept)) {
                    ret = this.playSmartBroadRing(this.currentDept);
                } else {
                    ret = this.playCurrentDeptRing(true, true, true);
                    this.debug("ivr根部门放音返回：{}", ret);
                }
            }
            //通过接口获取被叫 
            HotlineCallInDto dto = new HotlineCallInDto(this.user.getNumber(), this.mainCall.getCaller(), this.talkNote.getId(), this.talkNote.getIncomingTime().getTime());
            boolean apiSuccess;
            try {
                this.apiCallee = service.getHotlineCallee(dto);
                apiSuccess = true;
            } catch (Exception e) {
                this.warn("获取接口坐席异常，使用本地策略");
                apiSuccess = false;
            }
            this.debug("通过接口获取到的坐席号码：{}", this.apiCallee);
            if(apiSuccess) {
                if(StringUtils.isNotEmpty(this.apiCallee)) {
                    //开始呼叫坐席
                    this.processMakeCall(this.apiCallee);
                } else {
                    this.leaveMessage();
                }
            } else {
                //ivr导航
                Dept dept = this.getIvrDept();
                this.debug("ivr导航找到的最终部门：{}", dept);
                if(dept == null) {
                    log.warn("未找到最终部门！");
                    this.leaveMessage();
                    return;
                }
                this.agents = this.service.getIdleAgentsByDept(this.currentDept, this.fixedCallerNo);
                if (this.agents.isEmpty()) {
                    this.info("当前部门下无空闲坐席。。。显示当前部门下坐席状态明细：");
                    this.callErrorCode = CallOutErrorCode.NO_IDLE_BINDPHONE;
                    this.warn(this.service.getDeptBindphoneDetails(this.currentDept));
                    this.leaveMessage();
                } else {
                    //开始呼叫坐席
                    this.processMakeCall();
                }
            }
        } catch (Exception ex) {
            this.errorStackTrace(ex.getMessage(), ex);
            this.warn("程序异常，挂断主被叫");
            hangupBothCall();
        } finally {
            this.debug("主线程结束！");
            this.finished();
        }
    }
    
    private void processMakeCall(String callNo) {
        this.debug("开始呼叫api接口坐席：{}", callNo);
        this.agent = this.makeCall(callNo);
        if(this.agent != null) {
            initCallParams(this.agent, MidwareConstants.CALL_TYPE_CALLEE);
            this.calleeOffhook();
            if(this.mainCall.getState() != Constants.CALL_CONTENT || this.agent.getState() != Constants.CALL_CONTENT) {
                throw new MidwareException("主被叫有一方已挂机！");
            }
            boolean connected = this.connectCall(this.mainCall, this.agent);
            if (!connected) {
                throw new MidwareException("连接主被叫失败！");
            }
            this.await(200);
            if (this.mainCall.getState() == Constants.CALL_IDLE || this.agent.getState() == Constants.CALL_IDLE) {
                throw new MidwareException("主被叫有一方挂机！终止流程！");
            }
            this.isCallConnected = true;
            this.processRecord();
            //接收被叫输码，按键转移
            this.calleeReceiveDTMF();
        } else {
            this.leaveMessage();
        }
    }
    
    private Call makeCall(String callNo) {
        if (this.mainCall.getState() == Constants.CALL_IDLE) {
            throw new MidwareException("主叫已挂机！");
        }
        try {
            this.fixedCalleeNo = this.service.getFixedCalleeNo(callNo);
            this.debug("处理过后的被叫号码：{}", this.fixedCalleeNo);
            this.debug("开始处理互联互通。。。");
            //处理互联互通问题
            CallNumDto callNumDto = processInterConnection();
            long begin = System.currentTimeMillis();
            this.debug("开始呼叫坐席：主叫号码：{},被叫号码：{},开始时间：{},超时时间：{}", callNumDto.getCallerNo(), callNumDto.getCalleeNo(), begin, setting.getApiCallTimeOut());
            Call callee = this.asyncMakeCall(callNumDto, setting.getApiCallTimeOut());
            long end = System.currentTimeMillis();
            long callTime = (end - begin) / 1000;
            this.debug("呼叫结束，呼叫时间：{}，呼叫结果：{}", callTime, this.isCallSuccess);
            if (this.callErrorCode == 129) {
                this.warn("发生同抢，再次重新呼叫一次！", callTime);
                begin = System.currentTimeMillis();
                callee = this.asyncMakeCall(callNumDto, setting.getApiCallTimeOut());
                end = System.currentTimeMillis();
                callTime = (end - begin) / 1000;
            }
            if (callee != null && callTime < 1 && !callNumDto.getCalleeNo().startsWith(MidwareConstants.SIP_PHONE_PREFIX)) {
                //呼叫成功小于1秒，则认为超时终止失败，接通的是上一个坐席
                this.warn("发生并发接通问题，接通被叫：{}，外呼被叫：{}，忽略本次接通！", callee.getCallee(), callNumDto.getCalleeNo());
                callee.onHook();
                callee = null;
            }
            return callee;
        } catch (Exception e) {
            this.error(e.getMessage(), e);
            return null;
        }
    }
    
    private void processMakeCall() {
        this.debug("坐席的接听策略为：{},对坐席重排序！排序前顺序：{}", this.currentDept.getCallModleDescription(), this.agents);
        this.service.sortAgents(this.currentDept, this.agents);
        this.info("重新排序后的坐席顺序：{}", this.agents);
        this.agents = this.service.judgeMemoryFlag(this.user, this.currentDept, this.agents, this.talkNote.getCallerNo());
        this.info("记忆功能判断后再次重新排序后的坐席顺序：{}", this.agents);
        for (BindPhone phone : this.agents) {
            if (this.service.getAgentState(phone).equals(AGENT_STATE_IDLE)) {
                this.currentBindPhone = this.service.getLocalBindPhone(phone.getCustomerNo(), phone.getLsh());
                this.debug("根据400号码：{}和绑定号码流水号：{}，获得本地绑定号码：{}", phone.getCustomerNo(), phone.getLsh(),
                        this.currentBindPhone);
                if(this.currentBindPhone == null) {
                    this.warn("未找到本地绑定号码，取下一个空闲坐席！");
                    continue;
                }
                Call callee = this.makeCall();
                if (callee != null) {
                    this.agent = callee;
                    break;
                } else {
                    this.missedBindPhone = phone;
                }
            }
        }
        if (this.agent != null) {
            initCallParams(this.agent, MidwareConstants.CALL_TYPE_CALLEE);
            this.calleeOffhook();
            if(this.mainCall.getState() != Constants.CALL_CONTENT || this.agent.getState() != Constants.CALL_CONTENT) {
                throw new MidwareException("主被叫有一方已挂机！");
            }
            //改坐席状态，发送消息
            if (!this.currentBindPhone.isPbx()) {
                this.service.callConnectedAsync(this.currentBindPhone, this.talkNote, this.currentDept, user.getPushStatusFlag(), false);
                this.service.addAgentCallNumAsync(this.currentBindPhone);
            }
            //播放坐席提示音
            this.playAgentAlertRing(this.agent);
            this.playRecordTip(this.mainCall, this.agent);
            this.playNum(this.mainCall, this.agent);
            boolean connected = this.connectCall(this.mainCall, this.agent);
            if (!connected) {
                String currentState = this.service.getAgentState(this.currentBindPhone);
                this.warn("连接主被叫失败！当前电话状态：{}， 当前绑定号码状态：{}", this.agent.getState(), currentState);
                if (this.agent.getState() != Constants.CALL_IDLE || currentState.startsWith(AGENT_STATE_CALLING)) {
                    this.service.midwareUpdateAgentState(this.currentBindPhone, AGENT_STATE_IDLE, true);
                }
                if (this.agent.getState() == Constants.CALL_IDLE && currentState.equals(AGENT_STATE_CONNECT)) {
                    this.service.midwareUpdateAgentState(this.currentBindPhone, AGENT_STATE_IDLE, true);
                }
                throw new MidwareException("连接主被叫失败！");
            }
            this.await(200);
            if (this.mainCall.getState() == Constants.CALL_IDLE || this.agent.getState() == Constants.CALL_IDLE) {
                throw new MidwareException("主被叫有一方挂机！终止流程！");
            }
            this.isCallConnected = true;
            this.processRecord();
            //接收被叫输码，按键转移
            this.calleeReceiveDTMF();
        } else {
            this.leaveMessage();
        }
    }
    
    private void calleeReceiveDTMF() {
        if (this.user.isGhFlag()) {
            if (this.functionConfig != null && this.functionConfig.isInputTransferFlag()) {
                this.warn("用户关闭按键转接功能！");
            } else {
                this.getLastAgent().receiveDTMFAsync();
                this.debug("坐席开始异步收码");
            }
        } else {
            this.warn("用户未开通报工号功能！按键转接功能不能使用！");
        }
    }
    
    private void processRecord() {
        this.stopMainCallPlay();
        if (this.user.isRecordFlag()) {
            this.voiceRecord = this.service.startRecord(this.user, this.talkNote.getCallerNo(),
                    this.fixedCalleeNo, dualChannel);
            this.voiceRecord.setTalkNoteId(this.talkNote.getId());
            this.service.saveOrUpdateRecord(this.voiceRecord);
            String recordPath = this.voiceRecord.getFilePath();
            this.debug("用户的录音路径：{}", recordPath);
            if (dualChannel && !service.isSipFlag()) {
                String[] records = recordPath.split(",");
                this.mainCall.record(records[0], 0, false);
                this.agent.record(records[1], 0, false);
            } else {
                this.mainCall.record(recordPath, 0, false);
            }
        } else {
            this.info("当前用户未开通录音功能！");
        }
    }
    
    private boolean connectCall(Call caller, Call callee) {
        if (this.mainCall.isIsPlaying()) {
            this.mainCall.stopPlay();
            this.await(200);
        }
        if (caller.getState() == Constants.CALL_IDLE || callee.getState() == Constants.CALL_IDLE) {
            this.warn("主被叫有一方挂机！退出连接！");
            return false;
        }
        int ret = caller.connectCall(callee, !dualChannel);
        if (ret != Constants.RET_SUCCESS) {
            this.warn("连接主被叫失败！主叫状态：{}，被叫状态：{}，等待一秒后重新尝试一次", this.getCallStateDesc(caller.getState()),
                    this.getCallStateDesc(callee.getState()));
            this.await(1000);
            ret = caller.connectCall(callee, !dualChannel);
            if (ret != Constants.RET_SUCCESS) {
                if (caller.getState() == Constants.CALL_ALERT && callee.getState() == Constants.CALL_CONTENT) {
                    this.await(2000);
                    ret = caller.connectCall(callee, !dualChannel);
                }
                if (ret != Constants.RET_SUCCESS) {
                    this.error("重新连接主被叫失败！主叫状态：{}，被叫状态：{}", this.getCallStateDesc(caller.getState()),
                            this.getCallStateDesc(callee.getState()));
                }
            }
        }
        return ret == Constants.RET_SUCCESS;
    }
    
    private void playNum(Call caller, Call callee) {
        if (caller.getState() != Constants.CALL_CONTENT || callee.getState() != Constants.CALL_CONTENT) {
            this.warn("主被叫有一方还未摘机，等待500毫秒！");
            this.await(500);
        }
        if (this.user.isGhFlag() && this.currentDept.isGhFlag()) {
            this.stopMainCallPlay();
            String headFile = this.service.getReportHeadFile();
            String endFile = this.service.getReportEndFile();
            this.debug("报工号头文件：{},尾文件：{},工号：{}", headFile, endFile, this.currentBindPhone.getReportNum());
            if (StringUtils.isNotEmpty(this.currentBindPhone.getReportNum())
                    && this.getLastAgent().getState() == Constants.CALL_CONTENT
                    && this.mainCall.getState() != Constants.CALL_IDLE) {
                String files = headFile + ","
                        + this.service.getNumberWav(Integer.valueOf(this.currentBindPhone.getReportNum())) + "," + endFile;
                this.await(500);
                this.isPlayingNumber = true;
                caller.play(files, false, true, true, 0, 0);
                callee.play(files, false, true, false, 20, 0);
                this.isPlayingNumber = false;
            }
        } else {
//            this.await(200);
            this.info("用户：{}未开通报工号功能！", this.user.getNumber());
        }
    }
    
    private void playRecordTip(Call caller, Call callee) {
        if (caller.getState() == Constants.CALL_IDLE || callee.getState() == Constants.CALL_IDLE) {
            this.warn("主被叫有一方挂机！退出录音提示音播放！");
            return;
        }
        if (this.user.isRecordRingFlag()) {
            String ring = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_RECORD_TIP);
            this.info("播放录音提示彩铃：{}", ring);
            callee.play(ring, false, false, true, 0, 0);
            caller.play(ring, false, false, false, 0, 0);
        } else {
            this.info("用户未开通录音提示音功能！");
        }
    }
    
    private void initCallParams(Call callee, String callType) {
        callee.attachTeleThread(this);
        callee.setParam(RECEIVED_DTMF, new StringBuffer());
        callee.setParam(RECEIVED_DTMF_TIME, 0l);
        callee.setParam(CALL_TYPE, callType);
        if(this.currentBindPhone != null) {
            callee.setParam(CALL_BINDPHONE_ID, this.currentBindPhone.getId());
            callee.setParam(CALL_BINDPHONE_LSH, this.currentBindPhone.getCustomerNo() + "_" + this.currentBindPhone.getLsh());
            callee.setParam(CALL_AGENT_ID, this.currentBindPhone.getAgentId());
        }
    }
    
    private void playAgentAlertRing(Call call) {
        if (call.getState() != Constants.CALL_CONTENT) {
            this.warn("被叫挂机！退出坐席提示音！");
            return;
        }
        if (this.currentDept.getAgentAlertRing() != null) {
            String ring = this.service.getAgentAlertRing(this.currentDept);
            this.debug("坐席提示音地址：{}", ring);
            int time = this.service.getAgentAlertRingTime();
            call.play(ring, false, false, false, time, 0);
            if (call.isIsPlaying()) {
                call.stopPlay();
            }
        } else if (MidwareConstants.DEFAULT_RING_AGENT_TIP_CODE.equals(this.currentDept.getSalesAlertToneLsh())) {
            this.info("部门：{}进入默认坐席提示音", this.currentDept);
            String ring = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_AGENT_TIP);
            this.debug("坐席提示音地址：{}", ring);
            int time = this.service.getAgentAlertRingTime();
            call.play(ring, false, false, false, time, 0);
            if (call.isIsPlaying()) {
                call.stopPlay();
            }
        } else {
            this.info("部门：{}未配置坐席提示音", this.currentDept);
        }
    }
    
    /**
     * 被叫摘机
     */
    private void calleeOffhook() {
        Date now = new Date();
        if (!this.user.isPreOffhook()) {
            this.stopMainCallPlay();
            this.mainCall.answerSync(500);
            this.talkNote.setCallerOffhookTime(now);
        }
        this.talkType = TALK_TYPE_IVR_CON;
        this.talkNote.setTalkType(this.talkType);
        this.talkNote.setCalleeOffhookTime(now);
        this.talkNote.setDeptId(this.currentDept.getDeptLsh());
        if(this.apiCallee != null) {
            this.talkNote.setCalleeNo(this.apiCallee);
        } else {
            this.talkNote.setLsh(this.currentBindPhone.getId());
            if (StringUtils.isNotBlank(this.currentBindPhone.getReportNum())) {
                this.talkNote.setGh(Long.valueOf(this.currentBindPhone.getReportNum()));
            }
            this.talkNote.setCalleeNo(this.currentBindPhone.getOrigBindPhoneNo());
        }
        this.service.saveOrUpdateTalkNote(this.talkNote);
        this.debug("被叫摘机，被叫号码：{},工号：{},摘机时间：{}", this.fixedCalleeNo, this.talkNote.getGh(),
                this.talkNote.getCalleeOffhookTime());
    }
    
    private Call makeCall() {
        return this.makeCall(false);
    }
    
    private Call makeCall(boolean trans) {
        if (this.mainCall.getState() == Constants.CALL_IDLE) {
            throw new MidwareException("主叫已挂机！");
        }
        try {
            //更新坐席状态
            if (this.currentBindPhone.isPbx()) {
                int count = this.service.getPbxCount(this.currentBindPhone);
                this.debug("绑定号码开通了中继数量：{}，当前的通话数量：{}", this.currentBindPhone.getBindNum(), count);
//                this.service.saveOrUpdatePbxInfo(this.currentBindPhone, count);
//                this.debug("保存绑定号码当前的通话数量：{}", count);
                if (count < this.currentBindPhone.getBindNum()) {
                    //可以呼入
                    this.service.increasePbxCount(this.currentBindPhone);
                } else {
                    this.warn("用户的中继数量超过最大数量：{}", this.currentBindPhone.getBindNum());
                    return null;
                }
            } else {
                this.service.midwareUpdateAgentState(this.currentBindPhone,
                        AGENT_STATE_CALLING + "_" + this.service.isMainServer(), this.user.getPushStatusFlag());
            }
            if (!trans && this.currentDept.getCallModle() == 3) {
                //更新部门呼叫的最后绑定号码
                this.service.updateDeptLastBindPhone(this.user.getNumber(), this.currentDept.getDeptLsh(), this.currentBindPhone.getLsh());
            }
            CacheUtil.put(CacheUtil.CACHE_CALL_PROCESS_NAME, this.currentBindPhone.getCustomerNo() + "_"
                    + this.currentBindPhone.getId(), this);
            this.fixedCalleeNo = this.service.getFixedCalleeNo(this.currentBindPhone.getOrigBindPhoneNo());
            this.debug("处理过后的被叫号码：{}", this.fixedCalleeNo);
            if ((this.user.isScreenFlag() || interactFlag)   && !this.currentBindPhone.isPbx()) {
                this.debug("发送弹屏请求");
                String decryptStr = this.talkNote.getCallerNo();
                try {
                    decryptStr = CtiDes.getInstance().decrypt(this.talkNote.getCallerNo());
                } catch (Exception e) {
                    this.error("{}:解密失败", this.talkNote.getCallerNo(), e);
                }
                this.service.screenRequest(this.talkNote.getId(), this.currentBindPhone, decryptStr,
                        this.fixedCalleeNo, this.districtDesc, StringUtils.isEmpty(this.currentDept.getIvrKey()) ? (":" + this.currentDept.getName()) : (this.currentDept.getIvrKey() + ":" + this.currentDept.getName()),
                        this.currentBindPhone.getOrigBindPhoneNo(), talkNote.getIncomingTime().getTime());
            }

            if(this.user.isSxSmsFlag() && MyStringUtil.isMobile(this.currentBindPhone.getOrigBindPhoneNo())) {
                SmsSendDto dto = sendSmsMessage(6);
                this.debug("发送闪信短信：{}", JSON.toJSON(dto));
            }
            
            this.debug("开始处理互联互通。。。");
            //处理互联互通问题
            CallNumDto callNumDto = processInterConnection();
            //处理sip中继
            if(this.fixedCalleeNo.startsWith(MidwareConstants.SIP_PHONE_PREFIX)) {
                callNumDto.setCallerNo(this.mainCall.getCaller());
            }
            long begin = System.currentTimeMillis();
            this.debug("开始呼叫坐席：主叫号码：{},被叫号码：{},开始时间：{},超时时间：{}", callNumDto.getCallerNo(), callNumDto.getCalleeNo(), begin,
                    this.currentBindPhone.getWaitTime());
            Call callee = this.asyncMakeCall(callNumDto, this.currentBindPhone.getWaitTime());
            long end = System.currentTimeMillis();
            long callTime = (end - begin) / 1000;
            this.debug("呼叫结束，呼叫时间：{}，呼叫结果：{}", callTime, this.isCallSuccess);
            if (callee == null && callTime <= this.service.getMakeCallRetryTime() && this.mainCall.getState() != Constants.CALL_IDLE) {
                this.warn("呼叫失败时间:{}秒,小于重拨时间，重新呼叫一次！", callTime);
                begin = System.currentTimeMillis();
                callee = this.asyncMakeCall(callNumDto, this.currentBindPhone.getWaitTime());
                end = System.currentTimeMillis();
                callTime = (end - begin) / 1000;
            }
            if (callee == null && this.callErrorCode == 129) {
                this.warn("发生同抢，再次重新呼叫一次！", callTime);
                begin = System.currentTimeMillis();
                callee = this.asyncMakeCall(callNumDto, this.currentBindPhone.getWaitTime());
                end = System.currentTimeMillis();
                callTime = (end - begin) / 1000;
            }
            if (callee != null && callTime < 1 && !callNumDto.getCalleeNo().startsWith(MidwareConstants.SIP_PHONE_PREFIX)) {
                //呼叫成功小于1秒，则认为超时终止失败，接通的是上一个坐席
                this.warn("发生并发接通问题，接通被叫：{}，外呼被叫：{}，忽略本次接通！", callee.getCallee(), callNumDto.getCalleeNo());
                callee.onHook();
                callee = null;
            }
            if (callee == null) {
                CacheUtil.remove(CacheUtil.CACHE_CALL_PROCESS_NAME, this.currentBindPhone.getCustomerNo() + "_"
                        + this.currentBindPhone.getId());
                if (this.currentBindPhone.isPbx()) {
                    this.service.decreasePbxCount(this.currentBindPhone);
                } else {
                    //如果状态任然是呼叫中，则改回空闲
                    if (this.service.getAgentStateByAgentId(this.currentBindPhone.getAgentId()).equals(
                            AGENT_STATE_CALLING + "_" + this.service.isMainServer())) {
                        this.service.midwareUpdateAgentStateByAgentId(this.currentBindPhone, AGENT_STATE_IDLE,
                                this.user.getPushStatusFlag());
                    } else {
                        log.warn("绑定号码发生并发呼叫，忽略还原到空闲状态！");
                    }
                }
            }
            return callee;
        } catch (Exception e) {
            this.error(e.getMessage(), e);
            if (this.service.getAgentStateByAgentId(this.currentBindPhone.getAgentId()).equals(
                    AGENT_STATE_CALLING + "_" + this.service.isMainServer())) {
                this.service.midwareUpdateAgentStateByAgentId(this.currentBindPhone, AGENT_STATE_IDLE,
                        this.user.getPushStatusFlag());
            }
            return null;
        }
    }
    
    private Call asyncMakeCall(CallNumDto dto, long timeoutSeconds) {
        if (this.mainCall.getState() == Constants.CALL_IDLE) {
            this.error("主叫已挂机！");
            return null;
        }
        String originalNo = null;
        if(dto.getCalleeNo().startsWith(MidwareConstants.SIP_PHONE_PREFIX)) {
            //中继号码
            originalNo = this.service.getlocalNo() + dto.getOriginalNo();
        } else {
            //处理号码功能配置
            if (this.functionConfig != null && (StringUtils.contains(this.functionConfig.getAccessShortNum(), this.mainCall.getCaller()) || this.functionConfig.containsShortNum(this.mainCall.getCaller()))) {
                this.debug("主叫号码：{} 采用号码配置功能配置的短号码外显：{}", this.mainCall.getCaller(), this.functionConfig.getShortShowNum());
                dto.setCallerNo(this.functionConfig.getShortShowNum());
            } else {
                if (StringUtils.isNotEmpty(this.apiCallee) || (this.currentBindPhone != null && this.currentBindPhone.isPassthrough())) {
                    originalNo = dto.getCallType() == CallNumDto.CALL_TYPE_TRANS ? dto.getOriginalNo() : null;
                }
                if (originalNo != null && dto.getTransPrefix() == CallNumDto.TRANS_PREFIX_ON) {
                    originalNo = this.service.getlocalNo() + originalNo;
                }
                //判断有没有配置特殊的小号
                String specialOrigNo = this.service.getSpecialOrigNo(this.user.getNumber());
                if (originalNo != null && specialOrigNo != null) {
                    this.debug("使用配置的特殊小号：{}", specialOrigNo);
                    originalNo = specialOrigNo;
                }

                //判断是否用400号码作为原始被叫及改发号码
                if (originalNo != null && this.service.isOrigUseNetphone()) {
                    this.info("原始小号使用400号码");
                    originalNo = this.user.getNumber();
                }
            }
        }
        Call call = null;
        boolean terminate = false;
        try {
            synchronized (this.makeCallSync) {
                this.isMakingCall = true;
                this.isCallOver = false;
                this.isCallSuccess = false;
                this.isRejectCall = false;
                if (this.mainCall.getState() == Constants.CALL_IDLE) {
                    this.error("主叫已挂机！");
                    return null;
                }
                call = this.manager.makeCall(dto.getCallerNo(), dto.getCalleeNo(), originalNo, true, timeoutSeconds,
                        this.getName());
                if (call == null) {
                    return call;
                }
                call.setTraceId(this.mainCall.getTraceId());
                call.setParam(MAIN_CALL_THREAD, this);
                long begin = System.currentTimeMillis();
                while (!this.isCallOver) {
                    this.makeCallSync.wait(timeoutSeconds * 1000);
                    this.info("主线程被唤醒...isCallOver={}, isCallSuccess={}", this.isCallOver, this.isCallSuccess);
                    terminate = this.isRejectCall || (!this.isCallOver && System.currentTimeMillis() - begin >= timeoutSeconds * 1000);
                    if (terminate) {
                        this.warn((this.isRejectCall ? "用户拒接" : "超时") + "，终止呼叫！");
                        this.manager.stopMakeCall(call.getDeviceId(), call.getLsh());
                        this.isCallSuccess = false;
                        this.isCallOver = true;
                    }
                }
                this.isMakingCall = false;
            }
            if(terminate) {
                this.await(500);
            }
            return this.isCallSuccess ? call : null;
        } catch (Exception ex) {
            this.errorStackTrace(ex.getMessage(), ex);
            return null;
        }
    }
    
    private CallNumDto processInterConnection() {
        CallNumDto callNumDto = this.service.getCallNumDto(this.user.getNumber(), this.fixedCallerNo,
                this.apiCallee != null ? this.apiCallee : this.currentBindPhone.getOrigBindPhoneNo());
        if (callNumDto != null) {
            this.service.setCallDtoDefaultValue(callNumDto);
            //如果互联互通里没有设置被叫，使用默认被叫
            if (StringUtils.isEmpty(callNumDto.getCalleeNo())) {
                callNumDto.setCalleeNo(this.fixedCalleeNo);
            }
            this.info("查找到对应的互联互通配置:{}", callNumDto);
            if(StringUtils.isNotEmpty(this.apiCallee) && BooleanUtils.isTrue(setting.getCallerUseOriginalNo())) {
                this.info("api接口号码透传功能不可用！使用小号作为主叫号码");
                String origNo = this.user.getOriginalNo();
                callNumDto.setCallerNo(this.service.isOriginalNoPrefix() ? this.service.getlocalNo() + origNo : origNo);
            }
            if (this.currentBindPhone != null && !this.currentBindPhone.isPassthrough()) {
                this.info("绑定号码透传功能不可用！使用小号作为主叫号码");
                String origNo = this.user.getOriginalNo();
                callNumDto.setCallerNo(this.service.isOriginalNoPrefix() ? this.service.getlocalNo() + origNo : origNo);
            }
        } else {
            this.info("未找到互联互通配置，使用默认规则！");
            callNumDto = this.service.getDefaultCallNumDto(this.user.getOriginalNo(), this.getDefaultRuleCaller(),
                    this.fixedCalleeNo);
        }
        return callNumDto;
    }
    
    private String getDefaultRuleCaller() {
        String localAddress = this.service.getlocalNo();
        String remoteAddress = null;//外地手机的区号
        boolean passThrough = StringUtils.isNotEmpty(this.apiCallee) || this.currentBindPhone.isPassthrough();
        if(StringUtils.isNotEmpty(this.apiCallee) && BooleanUtils.isTrue(setting.getCallerUseOriginalNo())) {
        	passThrough = false;
        }
        String originalNo = this.user.getOriginalNo();
        //判断是否是国外号码
        if (this.mainCall.getCaller().startsWith("00")) {
            String caller = this.service.isOriginalNoPrefix() ? localAddress + originalNo : originalNo;
            this.info("国外主叫号码直接改成小号{}", caller);
            return caller;
        }
        String caller = passThrough ? this.fixedCallerNo : this.service.isOriginalNoPrefix() ? localAddress
                + originalNo : originalNo;

        if (caller.length() > 12) {
            remoteAddress = caller.substring(0, caller.length() - 11);
        }
        if (passThrough && caller.startsWith(localAddress) && !this.service.isKeepLocalDistrict(caller)) {
            //透传号码的主叫去掉本地区号
            caller = caller.substring(localAddress.length());
        }
        //主叫号码如果是外地手机，改成“0” + 手机号码，默认fixedCallerNo是区号 + 手机号码
        if (remoteAddress != null && !remoteAddress.equals(localAddress)) {
            caller = caller.substring(remoteAddress.length());
            if (!this.service.isAddCallerNationCode()) {
                caller = "0" + caller;
            }
        }
        if (this.service.isAddCallerNationCode()) {
            caller = "0086" + caller;
        }
        return caller;
    }
    
    private Dept getIvrDept() {
        Dept childDept = null;
        while (this.currentDept != null && this.service.hasChildDeptsBySql(this.currentDept)) {
            if (this.mainCall.getState() == Constants.CALL_IDLE) {
                throw new MidwareException("主叫已挂机！");
            }
            //分流进入
            childDept = this.service.getChildDeptByDivide(this.currentDept, this.fixedCallerNo);
            this.debug("当前部门：{}，分流进入下级部门，找到的下级部门：{}", this.currentDept, childDept);
            this.currentDept = childDept;
        }
        return this.currentDept;
    }
    
    //语音播报功能是否开启
    private boolean isSmartBroadOpen(Dept dept) {
        return this.user.isSmartBroadFlag() && dept.isSmartBroadFlag();
    }
    
    private int playSmartBroadRing(Dept dept) {
        if (this.mainCall.isIsPlaying()) {
            this.mainCall.stopPlay();
            this.await(500);
        }
        String ring = service.getSmartBroadRingFile(dept);
        this.currentRing = ring;
        if (ring != null) {
            this.debug("播放部门语音播报彩铃：{}", ring);
            return this.mainCall.play(ring, !this.user.isSmartBroadOnceFlag(), false, true, 0, 0);
        } else {
            this.warn("未配置语音播报彩铃！");
            return 0;
        }
    }
    
    private int playCurrentDeptRing(boolean playDefault, boolean async, boolean loop) {
        String ring;
        if (this.currentCrbtRing == null && this.currentDept.isBgRing()) {
            ring = this.service.getDefaultRing(this.currentDept.getColorRingLsh() + ".wav");
            this.debug("此部门配置了背景音乐：{}", ring);
        } else if (this.currentCrbtRing == null && MidwareConstants.DEFAULT_RING_TRANSFER_DEPT_CODE.equals(this.currentDept.getColorRingLsh())) {
            this.debug("此部门配置了默认彩铃：{}.wav" , MidwareConstants.DEFAULT_RING_TRANSFER_DEPT_CODE);
            ring = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_TRANSFER_DEPT);
            if (ring == null) {
                ring = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_TRANSFER_DEPT);
            }
        } else {
            ring = this.service.getRingFile(this.currentDept);
            if ((ring == null || !this.service.isRingExist(ring)) && playDefault) {
                ring = this.service.getDefaultRing(DEFAULT_RING_DUDU);
                this.debug("未找到部门：{}的彩铃！使用默认彩铃{}", this.currentDept.getName(), ring);
            }
        }
        this.debug("获得部门：{}的彩铃文件：{}", this.currentDept.getName(), ring);
        this.currentRing = ring;
        return this.currentRing != null ? this.mainCall.play(this.currentRing, loop, false, async, 0, 0) : 1;
    }
    
    private void playDeptCompanyRing(Dept dept) {
        String ring = this.service.getCompanyRingFile(dept);
        if ((ring == null || !this.service.isRingExist(ring))) {
            this.warn("未找到部门：{}的宣传彩铃！终止播放", dept.getName());
            return;
        }
        this.debug("获得部门：{}的宣传彩铃文件：{}", dept.getName(), ring);
        if (this.mainCall.isIsPlaying()) {
            this.mainCall.stopPlay();
            this.await(200);
        }
        this.mainCall.play(ring, false, false, false, 0, 0);
    }
    
    private void leaveMessage() {
        this.debug("用户进入留言！");
        if (this.mainCall.getState() == Constants.CALL_IDLE) {
            throw new MidwareException("主叫已挂机！");
        }
        this.stopMainCallPlay();
        if (!this.user.isVoiceBoxFlag()) {
            this.info("用户未开通留言功能！");
            this.callFail();
        } else {
            if (!this.user.isPreOffhook()) {
                this.debug("用户没有开通预摘机，留言前先对用户摘机！");
                int ret = this.mainCall.answerSync(500);
                if (ret == Constants.RET_SUCCESS) {
                    this.talkNote.setCallerOffhookTime(new Date());
                } else {
                    this.error("对用户摘机失败！");
                    return;
                }
            }
            //播放提示音前就算留言记录。2017/9/1 运维要求。
            this.voiceBox = this.service.startVoicebox(this.talkNote.getCallerNo(), this.user.getNumber(),
                    this.currentDept);
            this.voiceBox.setTalkNoteId(this.talkNote.getId());
            this.service.saveOrUpdateVoiceBox(this.voiceBox);
            this.talkNote.setOutgoingTime(this.voiceBox.getStartTime());
            this.service.saveOrUpdateTalkNote(this.talkNote);

            String voiceRing = currentDept == null ? null : this.service.getVoiceBoxAlertRing(currentDept);
            this.debug("部门的留言彩铃{}", voiceRing);
            if (voiceRing == null || !this.service.isRingExist(voiceRing)) {
                voiceRing = this.service.getVoiceBoxAlertRing(this.user.getNumber());
                this.debug("未找到部门的留言彩铃！使用号码的留言彩铃{}", voiceRing);
            }
            if ((voiceRing == null || !this.service.isRingExist(voiceRing))) {
                voiceRing = this.service.getDefaultRing(DEFAULT_RING_VOICEBOX);
                this.debug("未找到用户的留言彩铃！使用默认彩铃{}", voiceRing);
            }
            this.mainCall.play(voiceRing, false, false, false, 60, 0);
            if (this.mainCall.getState() == Constants.CALL_CONTENT) {
                this.debug("用户的留言路径：{}", this.voiceBox.getFilePath());
                String recordPath = this.voiceBox.getFilePath();
                if(this.service.isSipFlag()) {
                    //外部路径替换成xcc内部路径
                    recordPath = recordPath.replace(service.getSipVoiceBoxPath(), "/usr/local/freeswitch");
                }
                this.mainCall.recordSync(recordPath, this.service.getVoiceBoxMaxTime(), false);
                if (this.mainCall.getState() == Constants.CALL_CONTENT) {
                    this.mainCall.onHook();
                }
            }
        }
    }
    
    private void callFail() {
        String failRing = null;
        if(this.isIvrError && StringUtils.isNotEmpty(this.ivrErrorTip)) {
            failRing = this.ivrErrorTip;
        } else {
            failRing = this.service.getDefaultRing(DEFAULT_RING_FAIL);
        }
        this.info("播放坐席全忙提示音:{}", failRing);
        this.mainCall.play(failRing, false, false, false, 30, 0);
        if (this.mainCall.getState() != Constants.CALL_IDLE) {
            this.mainCall.onHook();
        }
    }
    
    private void stopMainCallPlay() {
        if (this.mainCall.isIsPlaying()) {
            this.mainCall.stopPlay();
            this.await(400);
        }
    }
    
    /**
     * 保存话单中的主叫号码 规则1.如果是手机，只保存11位真正手机号。 规则2.如果是固话，保存区号+固话
     *
     * @return
     */
    private String getTalkNoteCaller() {
        String caller = this.mainCall.getCaller();
        if (caller.startsWith("1") && caller.length() == 11) {
            //mobile,not change
        } else if (caller.startsWith("01") && caller.charAt(2) != '0') {
            //0+mobile, substring(1)
            caller = caller.substring(1);
        } else {
            //固话
            caller = this.fixedCallerNo;
        }
        try {
            caller = CtiDes.getInstance().encrypt(caller);
        } catch (Exception ex) {
            this.error(ex.getMessage(), ex);
        }
        return caller;
    }
    
    private int answerOrAlert() {
        if (this.user.isPreOffhook()) {
            this.debug("用户开通了预摘机功能！对来电摘机！");
            this.mainCall.answerSync(1000);
            if (this.mainCall.getState() != Constants.CALL_CONTENT) {
                this.await(500);
            }
            this.debug("对来电摘机！来电状态为：" + this.getCallStateDesc(this.mainCall.getState()));
            return this.mainCall.getState() == Constants.CALL_CONTENT ? 1 : 0;
        } else {
            this.debug("用户未开通预摘机功能！对来电振铃！");
            this.mainCall.alert();
            this.await(500);
            if (this.mainCall.getState() != Constants.CALL_ALERT) {
                this.await(500);
            }
            this.debug("对来电振铃！来电状态为：" + this.getCallStateDesc(this.mainCall.getState()));
            return this.mainCall.getState() == Constants.CALL_ALERT ? 1 : 0;
        }
    }
    
    private String getCallStateDesc(int callState) {
        switch (callState) {
            case 0:
                return "空闲";
            case 1:
                return "呼叫中";
            case 2:
                return "振铃";
            case 3:
                return "通话中";
            default:
                return "其他";
        }
    }
    
    public void hangupBothCall() {
        if (this.mainCall.getState() != Constants.CALL_IDLE) {
            this.info("挂断主叫");
            this.mainCall.onHook();
        }
        handupAgent();
    }
    
    public Call getLastAgent() {
        if (this.transList.isEmpty()) {
            return this.agent;
        } else {
            return this.transList.get(this.transList.size() - 1);
        }
    }
    
    public void handupAgent() {
        if (this.isMakingCall) {
            this.debug("终止呼叫坐席！");
            synchronized (this.makeCallSync) {
                this.isCallOver = true;
                this.isCallSuccess = false;
                this.isRejectCall = true;
                this.makeCallSync.notifyAll();
            }
        } else {
            Call callee = this.getLastAgent();
            if (callee != null && callee.getState() == Constants.CALL_CONTENT) {
                this.debug("挂断坐席！");
                callee.onHook();
            }
        }
    }
    
    private boolean isTalkNoteNotConnected() {
        return this.talkNote.getTalkType() == 1 || this.talkNote.getTalkType() == 2 || this.talkNote.getTalkType() == 5;
    }
    
    @Override
    public void callerOnhook() {
        if (this.callerOnhooked.compareAndSet(false, true)) {
            this.debug("主叫挂机！");
            if(this.isPlayingNumber) {
                this.debug("主叫在报工号中挂机");
                this.agent.stopPlay();
            }
            if (this.isMakingCall) {
                this.debug("正在呼叫被叫，终止呼叫。。。");
                this.handupAgent();
            }
            Date now = new Date();
            if (this.voiceBox != null) {
                this.talkType = TALK_TYPE_VOICE_BOX;
                this.voiceBox.setEndTime(now);
                this.voiceBox.setEndFlag(true);
                this.service.endVoiceBox(this.talkNote, this.voiceBox);
            }
            if (this.voiceRecord != null) {
                this.voiceRecord.setEndTime(now);
                this.voiceRecord.setEndFlag(true);
                this.service.endRecord(this.talkNote, this.voiceRecord, this.user.transMp3(), dualChannel);
            }
            if (this.talkNote.getCallerOnHook() == null) {
                this.talkNote.setCallerOnHook(true);
            }
            if (this.talkNote.getDeptId() == null) {
                this.talkNote.setDeptId(this.currentDept == null ? this.user.getNumber() : this.currentDept.getDeptLsh());
            }
            if (!this.isCallConnected && isTalkNoteNotConnected()) {
                //未接电话补上坐席信息
                BindPhone lastBindPhone = this.currentBindPhone != null ? this.currentBindPhone
                        : this.missedBindPhone != null ? this.missedBindPhone : null;
                if (lastBindPhone != null) {
                    this.talkNote.setCalleeNo(lastBindPhone.getOrigBindPhoneNo());
                    this.talkNote.setGh(StringUtils.isNotBlank(lastBindPhone.getReportNum()) ? Long.valueOf(lastBindPhone
                            .getReportNum()) : null);
                    this.talkNote.setLsh(lastBindPhone.getId());
                }
                //未接原因
                if (this.callErrorCode < 0) {
                    this.callErrorCode = CallOutErrorCode.MAINCALL_ONHOOK;
                }
                this.talkNote.setErrorCode(callErrorCode);
            }
            if (this.talkNote.getCallerNo() == null) {
                this.fixedCallerNo = this.service.getFixedCallerNo(this.mainCall.getCaller());
                this.talkNote.setCallerNo(this.getTalkNoteCaller());
            }
            this.talkNote.setEndFlag(true);
            this.talkNote.setOnhookTime(now);
            this.talkNote.setTalkType(this.talkType);
            this.service.endTalkNote(this.talkNote);
            this.mainCall.getAllParam().clear();
            this.handupAgent();
            this.transList.clear();
            //发送短信
            if (this.user.isPlatformPlus()) {
                if (this.isTalkNoteNotConnected() && this.user.isMissSmsFlag()) {
                    SmsSendDto dto = sendSmsMessage(2);
                    this.debug("发送漏接短信：{}", JSON.toJSON(dto));
                }
                if (this.user.isSmsFlag() && MyStringUtil.isMobile(this.mainCall.getCaller())) {
                    SmsSendDto dto = sendSmsMessage(1);
                    this.debug("发送挂机短信：{}", JSON.toJSON(dto));
                }
            }
            //发送漏接消息
            if (this.functionConfig != null && this.functionConfig.isInteractFlag() && this.currentBindPhone != null && isTalkNoteNotConnected()) {
                this.debug("接口客户发送漏接消息！");
                this.service.sendMissMessage(user, this.currentBindPhone, this.talkNote, this.mainCall.getState() == Constants.CALL_IDLE ? CalleeOnhookMessage.FINISH_TYPE_MAINCALL_ONHOOK : CalleeOnhookMessage.FINISH_TYPE_CALL_FAIL);
            }
        }
    }

    private SmsSendDto sendSmsMessage(int type) {
        SmsSendDto dto = new SmsSendDto();
        dto.setType(type);
        dto.setRecordId(this.talkNote.getId());
        dto.setNumberCode(this.user.getNumber());
        dto.setCallerNo(this.mainCall.getCaller());
        dto.setCalleeNo(this.apiCallee != null ? this.apiCallee : this.currentBindPhone != null ? this.currentBindPhone.getOrigBindPhoneNo() : "");
        dto.setDeptId(this.currentDept != null ? this.currentDept.getDeptLsh() : "");
        dto.setCallTime(DateTimeUtil.format(this.talkNote.getIncomingTime(), DateTimeUtil.Pattern.DATETIME));
        this.service.sendSmsMessage(dto);
        return dto;
    }
    
    @Override
    public void calleeOnhook() {
        this.debug("坐席挂机");
        if(this.isPlayingNumber) {
            this.debug("坐席在报工号中挂机");
            this.mainCall.stopPlay();
        }
        Object hasNext = null;
        if(this.apiCallee == null) {
            String bindPhoneId = (String) this.agent.getParam(CALL_BINDPHONE_ID);
            String agentId = (String) this.agent.getParam(CALL_AGENT_ID);
            hasNext = this.agent.getParam(MidwareConstants.CALL_HAS_NEXT);
            this.debug("坐席挂机，后续坐席：{}， 主叫状态：{}", hasNext, this.mainCall.getState());
            if (hasNext == null) {
                this.debug("不存在转移坐席，停止录音...");
                this.mainCall.stopRecord();
            }
            String customerNo;
            BindPhone bp = this.service.getLocalBindPhone(bindPhoneId);
            if (bp == null) {
                this.warn("根据绑定号码ID:{},找不到绑定号码，用户可能重新同步过绑定号码，改用流水号查找", bindPhoneId);
                String[] lsh = ((String) this.agent.getParam(CALL_BINDPHONE_LSH)).split("_");
                customerNo = lsh[0];
                bp = this.service.getLocalBindPhone(lsh[0], lsh[1]);
            } else {
                customerNo = bp.getCustomerNo();
            }
            CacheUtil.remove(CacheUtil.CACHE_CALL_PROCESS_NAME, customerNo + "_" + bindPhoneId);
            if (this.talkNote.getCallerOnHook() == null) {
                this.talkNote.setCallerOnHook(false);
            }
            if (bp != null) {
                this.debug("查找到坐席的绑定号码：{}", bp);
                if (!bp.isPbx()) {
                    this.debug("坐席挂机通知中心端");
                    this.service.calleeOnhook(bp, this.isCallConnected, false, this.talkNote, this.functionConfig);
                } else {
                    if (!this.service.getAgentState(bp).equals(MidwareConstants.AGENT_STATE_IDLE)) {
                        this.warn("坐席开通了交换机，但是坐席状态是不空闲，修改坐席状态为空闲");
                        this.service.midwareUpdateAgentState(bp, MidwareConstants.AGENT_STATE_IDLE, true);
                    }
                    this.debug("坐席挂机通知减少总机接通数量");
                    this.service.decreasePbxCount(bp);
                }
            } else {
                this.warn("找不到绑定号码，绑定号码可能被删除！");
                if (this.currentBindPhone != null) {
                    if (this.currentBindPhone.isPbx()) {
                        this.info("减少全局中继通话数量！");
                        this.service.decreaseGloablePbxCount(customerNo, this.currentBindPhone.getOrigBindPhoneNo());
                    } else {
                        this.info("更新坐席状态为空闲！");
                        this.service.midwareUpdateAgentStateByAgentId(customerNo, agentId, MidwareConstants.AGENT_STATE_IDLE);
                    }
                }
            }
        }
        this.agent.getAllParam().clear();
        if (hasNext == null && this.mainCall.getState() == Constants.CALL_CONTENT) {
            if (this.talkNote.getCalleeOffhookTime() == null) {
                //坐席尚未摘机，进入留言
                this.leaveMessage();
            } else {
                this.processSatisfy();
            }
        }
    }

    private void processSatisfy() {
        if (this.mainCall.getState() == Constants.CALL_CONTENT) {
            //先停止录音，原来的坐席通道可能被其他通话使用，防止录入其他通话的内容
            this.mainCall.stopRecord();
            if (!this.user.isSatiFlag()) {
                this.debug("用户未开通满意度评价");
            } else {
                this.voiceScore = this.service.startSatisfy(this.talkNote);
                String ring = this.service.getSatiRing(this.currentDept);
                this.debug("满意度彩铃：{}", ring);
                this.mainCall.play(ring, false, false, true, 0, 0);
                String key = this.mainCall.receiveDTMFSync(1, null, 30);
                this.debug("用户输入的满意度为：{}", key);
                if (StringUtils.isNumeric(key)) {
                    this.voiceScore.setResult(Integer.valueOf(key));
                }
                if(this.currentBindPhone != null) {
                    this.voiceScore.setAgentId(this.currentBindPhone.getAgentId() == null ? this.currentBindPhone.getLsh()
                        : this.currentBindPhone.getAgentId());
                }
                this.voiceScore.setEndFlag(true);
                this.service.endVoiceScore(this.talkNote, this.voiceScore);
                if ("4009960826".equals(this.user.getNumber())) {
                    ring = this.service.getDefaultRing("4009960826_score_end.wav");
                } else {
                    ring = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_SCORE_END);
                }
                this.debug("满意度结束彩铃：{}", ring);
                this.mainCall.play(ring, false, false, false, 30, 0);
            }
            this.mainCall.onHook();
        }
    }
    
    @Override
    public void transOnhook(Call trans) {
        this.debug("转移的坐席挂机！");
        String bindPhoneId = (String) trans.getParam(CALL_BINDPHONE_ID);
        String customerNo;
        BindPhone bp = this.service.getLocalBindPhone(bindPhoneId);
        if (bp == null) {
            this.warn("根据绑定号码ID:{},找不到绑定号码，用户可能重新同步过绑定号码，改用流水号查找", bindPhoneId);
            String[] lsh = ((String) trans.getParam(CALL_BINDPHONE_LSH)).split("_");
            customerNo = lsh[0];
            bp = this.service.getLocalBindPhone(lsh[0], lsh[1]);
        } else {
            customerNo = bp.getCustomerNo();
        }
        CacheUtil.remove(CacheUtil.CACHE_CALL_PROCESS_NAME, customerNo + "_" + bindPhoneId);
        if (bp != null) {
            if (!bp.isPbx()) {
                this.service.calleeOnhook(bp, this.talkNote, this.functionConfig);
            } else {
                this.service.decreasePbxCount(bp);
            }
        } else {
            this.warn("找不到绑定号码，绑定号码可能被删除！");
            if (this.currentBindPhone != null) {
                if (this.currentBindPhone.isPbx()) {
                    this.info("减少全局中继通话数量！");
                    this.service.decreaseGloablePbxCount(customerNo, this.currentBindPhone.getOrigBindPhoneNo());
                } else {
                    this.info("更新坐席状态为空闲！");
                    this.service.midwareUpdateAgentStateByAgentId(customerNo, this.currentBindPhone.getAgentId(), MidwareConstants.AGENT_STATE_IDLE);
                }
            }
        }
        if (trans.getParam(MidwareConstants.CALL_HAS_NEXT) == null
                && this.mainCall.getState() == Constants.CALL_CONTENT) {
            this.processSatisfy();
        }
        trans.getAllParam().clear();
    }

    @Override
    public void callOver(int status, int errorCode) {
        this.debug("呼叫结束！呼叫结果：{}，失败原因：{}", status, errorCode);
        synchronized (this.makeCallSync) {
            if (this.isMakingCall) {
                this.isCallOver = true;
                this.isCallSuccess = (status == 1);
                this.callErrorCode = errorCode;
                this.makeCallSync.notifyAll();
                this.debug("唤醒等待线程！");
            }
        }
    }

    @Override
    public void cdr(String record) {
        this.debug("收到cdr事件，录音地址：{}", record);
        if(this.voiceRecord != null) {
            this.voiceRecord.setFilePath(record);
            this.voiceRecord.setFileName(record.substring(record.lastIndexOf("/") + 1));
            this.service.saveOrUpdateRecord(voiceRecord);
        }
        if(this.voiceBox != null) {
            this.voiceBox.setFilePath(record);
            this.voiceBox.setFileName(record.substring(record.lastIndexOf("/") + 1));
            this.service.saveOrUpdateVoiceBox(this.voiceBox);
        }
    }
}
