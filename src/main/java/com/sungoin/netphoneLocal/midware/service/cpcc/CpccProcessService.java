/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.cpcc;

import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.business.service.BaseService;
import com.sungoin.netphoneLocal.config.CpccSetting;
import com.sungoin.netphoneLocal.midware.service.sungoin.SungoinResponse;
import com.sungoin.netphoneLocal.util.HttpHelper;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class CpccProcessService {
    private static final Logger log = LoggerFactory.getLogger(CpccProcessService.class);

    @Resource
    private CpccSetting setting;
    
    @Resource
    private BaseService baseService;
    
        
    public Dept getDeptByCustomerNoAndIvrKeyAndDepth(String customerNo, String ivrKey, Integer depth) {
        return this.baseService.getDeptByCustomerNoAndIvrKeyAndDepth(customerNo, ivrKey, depth);
    }
    
    public Dept findDeptByCustomerAndAreaCode(String customerNo, String areaCode) {
        return this.baseService.findByCustomerNoAndAreaCode(customerNo, areaCode);
    }
    
    public String getDistrict(String caller) {
        return this.baseService.getDistrict(caller);
    }
    
    public String getIvrKeyByCaller(String numberCode, String caller) {
        CpccCustomerDataDto dto = new CpccCustomerDataDto();
        dto.setCaller(caller);
        dto.setNumberCode(numberCode);
        SungoinResponse resp = HttpHelper.postSungoinHttps(setting.getCpccApiEndpoint() + CpccSetting.PATH_GET_IVR, dto);
        return resp == null ? null : (String) resp.getData();
    }
    
    public void addCallno(CpccCustomerDataDto dto) {
        SungoinResponse resp = HttpHelper.postSungoinHttps(setting.getCpccApiEndpoint() + CpccSetting.PATH_ADD, dto);
    }
}
