/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.great;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.business.po.NumberFuncConfig;
import com.sungoin.netphoneLocal.business.po.TalkNote;
import com.sungoin.netphoneLocal.business.po.User;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE_CALLER;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.MAIN_CALL_THREAD;
import com.sungoin.netphoneLocal.midware.exception.MidwareException;
import com.sungoin.netphoneLocal.midware.service.EventHandler;
import com.sungoin.netphoneLocal.util.CtiDes;
import com.sungoin.netphoneLocal.util.DateTimeUtil;
import com.sungoin.netphoneLocal.util.SpringHelper;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.util.Date;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class GreatProcessThread extends TeleThread implements EventHandler {
    
    private static final Logger log = LoggerFactory.getLogger(GreatProcessThread.class);
    private transient GreatProcessService service;//序列化时忽略此域，反序列化时手动赋值

    private final String logPrefix;
    private final NumberFuncConfig functionConfig;
    private final User user;
    private volatile Dept currentDept;
    private volatile Call mainCall;
    private volatile TalkNote talkNote;
    private String fixedCallerNo;//处理过的主叫号码
    
    //IVR导航异步收码控制
    private final String ivrSync = new String();
    private boolean isIvrOver;
    private StringBuffer dtmf;
    private int ivrLength;
    
    //业务对象
    private String type;//类型
    private String code;//厕所编号
    
    /**
     * 反序列化时给transient域service赋值
     *
     * @param in
     * @throws IOException
     * @throws ClassNotFoundException
     */
    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        in.defaultReadObject();
        this.service = SpringHelper.getBean(GreatProcessService.class);
    }

    public GreatProcessThread(Call mainCall, User user, TeleThreadEventListener listener, NumberFuncConfig config) {
        super(listener);
        Date now = new Date();
        this.user = user;
        this.mainCall = mainCall;
        this.logPrefix = mainCall.getCaller() + "-" + user.getNumber() + "-" + DateTimeUtil.formatTime(now);
        this.talkNote = new TalkNote(user.getNumber(), user.getOriginalNo(), null, now);
        this.functionConfig = config;
        this.init();
    }
    
    private void init() {
        this.service = SpringHelper.getBean(GreatProcessService.class);
        this.mainCall.attachTeleThread(this);
        this.mainCall.setParam(CALL_TYPE, CALL_TYPE_CALLER);
        this.mainCall.setParam(MAIN_CALL_THREAD, this);
    }

    private void debug(String string, Object... os) {
        log.debug(this.logPrefix + string, os);
    }

    private void info(String string, Object... os) {
        log.info(this.logPrefix + string, os);
    }

    private void warn(String string, Object... os) {
        log.warn(this.logPrefix + string, os);
    }

    private void error(String string, Object... os) {
        log.error(this.logPrefix + string, os);
    }

    private void errorStackTrace(String string, Throwable throwable) {
        log.error(this.logPrefix + string, throwable);
    }

    public String getLogPrefix() {
        return this.logPrefix;
    }

    @Override
    public void run() {
        try {
            this.debug("开始格瑞业务流程！");
            int ret = this.answerOrAlert();
            if (this.user.isPreOffhook()) {
                //如果在摘机前，主叫已经挂机了，防止摘机时间晚于挂机时间，直接取挂机时间
                Date offHookTime = this.talkNote.getOnhookTime() == null ? new Date() : this.talkNote.getOnhookTime();
                this.talkNote.setCallerOffhookTime(offHookTime);
            }
            //处理主叫号码，补上区号
            this.fixedCallerNo = this.service.getFixedCallerNo(this.mainCall.getCaller());
            this.debug("处理过后的主叫号码：{}", this.fixedCallerNo);
            this.talkNote.setCallerNo(this.getTalkNoteCaller());
            this.service.saveOrUpdateTalkNote(this.talkNote, false);
            this.debug("保存话单成功！");
            if (ret != Constants.RET_SUCCESS) {
                throw new MidwareException(this.logPrefix + "对主叫：" + this.mainCall.toString() + "摘机失败！");
            }
            this.currentDept = this.service.getRootDept(this.user);
            this.processIvr();
        } catch (Exception ex) {
            this.errorStackTrace(ex.getMessage(), ex);
            this.warn("程序异常，挂断主叫");
            hangupMainCall();
        } finally {
            this.finished();
            this.debug("主线程结束！");
        }
    }
    
    private int answerOrAlert() {
        this.mainCall.answerSync(1000);
        this.debug("对来电摘机！来电状态为：" + this.getCallStateDesc(this.mainCall.getState()));
        return this.mainCall.getState() == Constants.CALL_CONTENT ? 1 : 0;
    }
    
    private String getCallStateDesc(int callState) {
        switch (callState) {
            case 0:
                return "空闲";
            case 1:
                return "呼叫中";
            case 2:
                return "振铃";
            case 3:
                return "通话中";
            default:
                return "其他";
        }
    }
    
    /**
     * 保存话单中的主叫号码 规则1.如果是手机，只保存11位真正手机号。 规则2.如果是固话，保存区号+固话
     *
     * @return
     */
    private String getTalkNoteCaller() {
        String caller = this.mainCall.getCaller();
        if (caller.startsWith("1") && caller.length() == 11) {
            //mobile,not change
        } else if (caller.startsWith("01") && caller.charAt(2) != '0') {
            //0+mobile, substring(1)
            caller = caller.substring(1);
        } else {
            //固话
            caller = this.fixedCallerNo;
        }
        try {
            caller = CtiDes.getInstance().encrypt(caller);
        } catch (Exception ex) {
            this.error(ex.getMessage(), ex);
        }
        return caller;
    }
    
    private void hangupMainCall() {
        if (this.mainCall.getState() != Constants.CALL_IDLE) {
            this.info("挂断主叫");
            this.mainCall.onHook();
        }
    }
    
    @Override
    public void callerOnhook() {
        this.debug("主叫挂机！");
        synchronized (this.ivrSync) {
            this.ivrSync.notifyAll();
        }
        Date now = new Date();
        if (this.talkNote.getCallerOnHook() == null) {
            this.talkNote.setCallerOnHook(true);
        }
        if (this.talkNote.getDeptId() == null) {
            this.talkNote.setDeptId(this.currentDept == null ? this.user.getNumber() : this.currentDept.getDeptLsh());
        }
        this.talkNote.setEndFlag(true);
        this.talkNote.setOnhookTime(now);
        if (this.talkNote.getCallerNo() == null) {
            this.fixedCallerNo = this.service.getFixedCallerNo(this.mainCall.getCaller());
            this.talkNote.setCallerNo(this.getTalkNoteCaller());
        }
        this.service.endTalkNote(this.talkNote);
        this.mainCall.getAllParam().clear();
    }
    
    private void processIvr() {
        int typeErrorCount = 0, codeErrorCount = 0;
        String inputRing = this.service.getCustomRing("great_select_type.wav");
        this.debug("开始播放选择类型提示音：{}", inputRing);
        this.mainCall.play(inputRing, false, false, true, 0, 0);
        int timeoutSecond = currentDept.getIvrStayTime() != null ? currentDept.getIvrStayTime() + 1 : this.service.getIvrTimeoutSeconds();
        this.debug("按键收码超时时长：{}", timeoutSecond);
        if (this.functionConfig != null && this.functionConfig.getInputTimeOut() != null) {
            timeoutSecond = functionConfig.getInputTimeOut();
            this.debug("号码配置了按键收码等待时长：{}", timeoutSecond);
        }
        type = this.receiveIvrDtmf(1, timeoutSecond);
        this.debug("收到用户的按键：{}", type);
        while(isMainCallConnected() && (type.equals("*") || (!type.equals("0") && !type.equals("1"))) && typeErrorCount++ < 3) {
            this.mainCall.play(inputRing, false, false, true, 0, 0);
            type = this.receiveIvrDtmf(1, timeoutSecond);
        }
        if(!type.equals("0") && !type.equals("1")) {
            this.mainCall.onHook();
            return;
        }
        String codeRing = this.service.getCustomRing("great_input_code.wav");
        this.debug("开始播放输入编号提示音：{}", codeRing);
        this.mainCall.play(codeRing, false, false, true, 0, 0);
        boolean codeSuccess = false;
        while(!codeSuccess && codeErrorCount++ < 3 && isMainCallConnected()) {
            code = this.receiveIvrDtmf(30, timeoutSecond);
            this.debug("用户输入的编号：{}", code);
            ResponseDto responseDto = null;
            if(StringUtils.isNotEmpty(code)) {
                code = code.substring(0, code.length() -1);
                RequestDto requestDto = new RequestDto(type, code);
                responseDto = this.service.getResult(requestDto);
                this.debug("查询编号，请求参数：{}，返回：{}", requestDto, responseDto);
            }
            codeSuccess = responseDto != null && responseDto.getCode() == 200;
            if(!codeSuccess) {
                String errorRing = this.service.getCustomRing("great_input_error.wav");
                this.debug("开始播放按键错误提示音：{}", errorRing);
                this.mainCall.play(errorRing, false, false, true, 0, 0);
            }
        }
        if(codeSuccess) {
            String successRing = this.service.getCustomRing("great_success.wav");
            this.debug("开始播放成功提示音：{}", successRing);
            this.mainCall.play(successRing, false, false, false, 20, 0);
        } else {
            String failRing = this.service.getCustomRing("great_fail.wav");
            this.debug("开始播放失败提示音：{}", failRing);
            this.mainCall.play(failRing, false, false, false, 15, 0);
        }
        if(this.mainCall.getState() == Constants.CALL_CONTENT) {
            this.mainCall.onHook();
        }
    }
    
    private boolean isMainCallConnected() {
        return this.mainCall.getState() == Constants.CALL_CONTENT;
    }
    
    private String receiveIvrDtmf(int length, int timeoutSecond) {
        try {
            this.dtmf = new StringBuffer();
            this.mainCall.receiveDTMFAsync();
            synchronized (this.ivrSync) {
                this.ivrLength = length;
                this.isIvrOver = false;
                while (!this.isIvrOver) {
                    this.ivrSync.wait(timeoutSecond * 1000);
                    if (!this.isIvrOver) {
                        if (this.mainCall.getState() != Constants.CALL_CONTENT) {
                            this.warn("收码异常！主叫挂机！");
                            return "";
                        }
                        this.warn("ivr 收码超时！");
                        this.mainCall.stopReceiveDTMF();
                        return "";
                    }
                }
            }
            this.mainCall.stopReceiveDTMF();
            return this.dtmf.toString();
        } catch (Exception ex) {
            this.errorStackTrace(ex.getMessage(), ex);
            return null;
        }
    }

    @Override
    public void calleeOnhook() {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public void transOnhook(Call trans) {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public void callOver(int status, int errorCode) {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public void ivrReceived(String key) {
        this.debug("收到用户按键：{}", key);
        this.dtmf.append(key);
        synchronized (this.ivrSync) {
            if (!this.isIvrOver && (key.equals("#") || (this.ivrLength == 1 && key.equals("*")) || dtmf.length() == this.ivrLength)) {
                this.info("用户按#号键*号键或满足按键位数！退出同步块！");
                this.isIvrOver = true;
                this.ivrSync.notifyAll();
            }
        }
    }

    @Override
    public void dtmfTrans() {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public void cdr(String record) {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public void detectSpeech(String text) {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }
    
    @Override
    public void dtmfToIvr() {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }
}
