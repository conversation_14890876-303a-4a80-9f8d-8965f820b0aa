/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.midware.service.task;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.Call;
import com.sungoin.netphoneLocal.midware.service.MainProcessThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 *
 * <AUTHOR>
 */
public class CallTimeoutTask implements Runnable {
	private static final Logger log = LoggerFactory.getLogger(CallTimeoutTask.class);
	
	private final MainProcessThread mpt;

	public CallTimeoutTask(MainProcessThread mpt) {
		this.mpt = mpt;
	}

	@Override
	public void run() {
		try {
			log.info(mpt.getLogPrefix() + "通话超时时间到，停止主被叫");
			Call mainCall = mpt.getMainCall();
			Call agent = mpt.getAgent();
			if(mainCall != null && agent != null) {
				log.info(mpt.getLogPrefix() + "主被叫状态：主叫：{}，被叫：{}", mainCall.getState(), agent.getState());
				if(mainCall.getState() == Constants.CALL_CONTENT && agent.getState() == Constants.CALL_CONTENT) {
					mpt.hangupBothCall();
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
}
