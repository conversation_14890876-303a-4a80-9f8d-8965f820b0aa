/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.log;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class PullLogThread implements Runnable {
    private static final Logger log = LoggerFactory.getLogger(PullLogThread.class);
    
    private final PullLogDto dto;

    public PullLogThread(PullLogDto dto) {
        this.dto = dto;
    }
    
    @Override
    public void run() {
        try {
            //拉取的日志存放目录,基于项目当前目录，netphoneLocal下则是（/usr/local/netphoneLocal)
            File folder = new File("logs/pullLog");
            log.debug("拉取日志的存放目录：{}", folder.getAbsolutePath());
            if(!folder.exists()) {
                folder.mkdir();
            }
            ProcessBuilder builder = new ProcessBuilder();
            List<String> commands = new ArrayList<String>();
            commands.add("/bin/sh");
            commands.add("-c");
            commands.add(dto.getCommand() + " > " + folder.getAbsolutePath() + "/" + dto.getId() + ".log");
            builder.command(commands);
            Process process = builder.start();
            process.waitFor(1, TimeUnit.MINUTES);
            File logFile = new File(folder, dto.getId() + ".log");
            if(logFile.exists() && logFile.length() > 0) {
                //上传云存储，回调中心端（判断主备）
                PullLogResultDto result = new PullLogResultDto();
                result.setId(dto.getId());
                result.setUrl("");
                result.setIsMain(true);
                
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
