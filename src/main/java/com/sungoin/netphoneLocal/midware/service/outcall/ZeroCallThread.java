/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.outcall;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.netphoneLocal.business.bean.ZeroCallDto;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE_OUTCALL;
import com.sungoin.netphoneLocal.midware.service.ProcessService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class ZeroCallThread implements Runnable {
    private static final Logger log = LoggerFactory.getLogger(ZeroCallThread.class);
    private static final String SIP_PREFIX = "Z";
    
    private final ZeroCallDto dto;
    private final ProcessService service;

    public ZeroCallThread(ZeroCallDto dto, ProcessService service) {
        this.dto = dto;
        this.service = service;
    }

    @Override
    public void run() {
        Call call = null;
        try {
            String caller = dto.getAutoCaller();
            String callee = dto.getNumberCode();
            long delayTime = service.getZeroDelayTime();
            log.debug("零次呼开始呼叫，主叫：{}， 被叫：{}，放音延迟：{}", caller, callee, delayTime);
            if(service.isSipFlag()) {
                //sip平台添加前缀
                callee = SIP_PREFIX + callee;
            }
            call = CallManager.getInstance().makeCall(caller, callee, null, false, 30, null);
            if(call != null) {
                call.setParam(CALL_TYPE, CALL_TYPE_OUTCALL);
                log.debug("呼叫成功！{}毫秒后开始广播", delayTime);
                dto.setStatus(1);
                Thread.sleep(delayTime);
                //目前只有sip平台实现了广播功能
                String ring = service.getDefaultRing("outbound_default_01.wav");
                log.debug("开始广播：{}", ring);
                call.broadCast(ring);
                Thread.sleep(15000);
                log.debug("放音结束，挂断主叫");
                if(call.getState() == Constants.CALL_CONTENT) {
                    call.onHook();
                }
            }
            service.sendZeroCallMessage(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
    
}
