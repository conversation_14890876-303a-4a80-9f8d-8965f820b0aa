/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.outcall;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.netphoneLocal.business.bean.ZeroCallDto;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import com.sungoin.netphoneLocal.midware.service.ProcessService;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE_OUTCALL;

/**
 *
 * <AUTHOR>
 */
public class OutCallRequest {
    private static final Logger log = LoggerFactory.getLogger(OutCallRequest.class);
    private static final String SIP_PREFIX = "A";
    private final ZeroCallDto dto;
    private final ProcessService service;

    public OutCallRequest(ZeroCallDto dto, ProcessService service) {
        this.dto = dto;
        this.service = service;
    }
    
    public boolean outcall() {
        Call call = null;
        try {
            Date now = new Date();
            String caller = dto.getAutoCaller();
            String callee = this.service.getFixedCalleeNo(dto.getNumberCode());
            log.debug("呼死你开始呼叫，主叫：{}， 被叫：{}，", caller, callee);
            if(service.isSipFlag()) {
                //sip平台添加网关前缀
                callee = SIP_PREFIX + callee;
            }
            call = CallManager.getInstance().makeCall(caller, callee, null, false, 30, null);
            if(call != null) {
                call.setParam(CALL_TYPE, CALL_TYPE_OUTCALL);
//                String filePath = this.service.getRecordPath() + "/" + DateTimeUtil.formatShortDate(now) + "/" + caller + "_" + now.getTime() + ".wav";;
//                log.debug("录音地址：{}", filePath);
//                call.record(filePath, 25, false);
                String ring = service.getDefaultRing(MidwareConstants.MISS_CALL_TIP);
                log.debug("{}-呼通后播放音频：{}", dto.getNumberCode(), ring);
                call.play(ring, false, false, false, 15, 0);
                if(call.getState() == Constants.CALL_CONTENT) {
                    call.onHook();
                }
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }
}
