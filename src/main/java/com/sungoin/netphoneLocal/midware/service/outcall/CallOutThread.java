/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.midware.service.outcall;

import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.netphoneLocal.business.bean.CallNumDto;
import com.sungoin.netphoneLocal.business.po.InterConnection;
import com.sungoin.netphoneLocal.business.service.InterConnectionService;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE_OUTCALL;
import com.sungoin.netphoneLocal.midware.service.ProcessService;
import com.sungoin.netphoneLocal.util.SpringHelper;
import java.util.concurrent.Callable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2016-2-15
 */
public class CallOutThread implements Callable<String> {

	private static final Logger log = LoggerFactory.getLogger(CallOutThread.class);
	public static final int RESULT_SUCCESS = 1;
	public static final int RESULT_FAIL = 0;

	private final String caller;
	private final String callee;
	private final InterConnection ic;

	public CallOutThread(String caller, String callee, InterConnection ic) {
		this.caller = caller;
		this.callee = callee;
		this.ic = ic;
	}

	@Override
	public String call() {
		Call call = null;
		try {
			InterConnectionService ics = SpringHelper.getBean(InterConnectionService.class);
			ProcessService ps = SpringHelper.getBean(ProcessService.class);
			CallNumDto dto = ics.httpProcess(ic, caller, callee);
			if (dto == null) {
				throw new IllegalArgumentException("主被叫与互联互通配置不匹配！");
			}
			ps.setCallDtoDefaultValue(dto);
			String originNo = dto.getCallType() == CallNumDto.CALL_TYPE_TRANS ? dto.getOriginalNo() : null;
			if(originNo != null && dto.getTransPrefix() == CallNumDto.TRANS_PREFIX_ON) {
				originNo = ps.getlocalNo() + originNo;
			}
			String result = "主叫：" + dto.getCallerNo() + "被叫：" + dto.getCalleeNo() + "原始被叫：" + originNo;
			log.info("开始外呼：{}", result);
			call = CallManager.getInstance().makeCall(dto.getCallerNo(), dto.getCalleeNo(), originNo, false, 30, null);
			if(call == null) {
				return result + "，呼叫失败";
			} else {
                call.setParam(CALL_TYPE, CALL_TYPE_OUTCALL);
                Thread.sleep(500);
				call.onHook();
				return result + "，呼叫成功";
			}
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
			return ex.getMessage();
		}
	}
}
