/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.elevator;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.SendResult;
import com.sungoin.netphoneLocal.business.bean.CallNumDto;
import com.sungoin.netphoneLocal.business.mq.PlatformProducer;
import com.sungoin.netphoneLocal.business.mq.SmsSendDto;
import com.sungoin.netphoneLocal.business.po.BindPhone;
import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.business.po.OutDataTask;
import com.sungoin.netphoneLocal.business.po.OutDataType;
import com.sungoin.netphoneLocal.business.po.TalkNote;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.business.po.VoiceRecord;
import com.sungoin.netphoneLocal.business.rmi.RmiService;
import com.sungoin.netphoneLocal.business.service.BaseService;
import com.sungoin.netphoneLocal.business.service.BusinessService;
import com.sungoin.netphoneLocal.config.CommonSettings;
import com.sungoin.netphoneLocal.config.ElevatorSetting;
import com.sungoin.netphoneLocal.constants.MqConstants;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import com.sungoin.netphoneLocal.midware.config.MidwareSettings;
import com.sungoin.netphoneLocal.socket.message.AgentMessage;
import com.sungoin.netphoneLocal.socket.message.SocketMessage;
import com.sungoin.netphoneLocal.util.DateTimeUtil;
import com.sungoin.netphoneLocal.util.HttpHelper;
import com.sungoin.netphoneLocal.util.JsonHelper;
import com.sungoin.netphoneLocal.util.VoiceUtil;
import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.sungoin.netphoneLocal.midware.service.ProcessService.MONITOR_PREFIX;

/**
 *
 * <AUTHOR>
 */
@Service
public class ElevatorProcessService {

    private static final Logger log = LoggerFactory.getLogger(ElevatorProcessService.class);

    @Resource
    private MidwareSettings midwareSetting;

    @Resource
    private ElevatorSetting setting;

    @Resource
    private CommonSettings commonSettings;

    @Resource
    private BaseService baseService;

    @Resource
    private RmiService rmiService;

    @Resource
    private BusinessService businessService;

    @Resource
    @Qualifier("PlatformMessageProducer")
    private Producer mqProducer;

    ExecutorService es = Executors.newCachedThreadPool();

    public ElevatorUserInfo getUserInfo(String code) {
        String url = setting.getElevatorApiEndpoint() + ElevatorSetting.API_USER_INFO;
        log.debug("获取电梯救援号码地址：{}", url);
        Map<String, String> params = new HashMap<>();
        params.put("sos_position_code", code);
        String returnValue = HttpHelper.SendPostRequest(url, params);
        ElevatorUserInfo userInfo = null;
        Map map = JsonHelper.json2Object(returnValue, Map.class);
        Object data = map.get("data");
        if (data != null && data instanceof Map) {
            userInfo = new ElevatorUserInfo((Map) data);
        } else {
            log.warn("未获取到用户信息，接口返回：{}", returnValue);
        }
        return userInfo;
    }

    public OnhookCreateInfo pushOnhookInfo(String code, String phone, String time) {
        OnhookCreateInfo info = null;
        try {
            String url = setting.getElevatorApiEndpoint() + ElevatorSetting.API_CREATE_CODE;
            log.debug("获取挂机创单地址：{}", url);
            Map<String, String> params = new HashMap<>();
            params.put("sos_position_code", code);
            params.put("sos_phone", phone);
            params.put("sosAt", time);
            String returnValue = HttpHelper.SendPostRequest(url, params);
            Map map = JsonHelper.json2Object(returnValue, Map.class);
            Object data = map.get("data");
            if (data != null && data instanceof Map) {
                info = new OnhookCreateInfo((Map) data);
            } else {
                log.warn("未获取到创单信息，接口返回：{}", returnValue);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return info;
    }

    public void saveOrUpdateTalkNote(final TalkNote talkNote, boolean async) {
        if (async) {
            this.es.submit(() -> {
                try {
                    ElevatorProcessService.this.businessService.saveOrUpdate(talkNote);
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }
            });
        } else {
            this.businessService.saveOrUpdate(talkNote);
        }
    }

    public void saveOrUpdateTalkNote(final TalkNote talkNote) {
        saveOrUpdateTalkNote(talkNote, true);
    }

    @Transactional
    public void endTalkNote(TalkNote talkNote) {
        if (talkNote.isEndFlag() && talkNote.getCalleeOffhookTime() != null) {
            talkNote.setTalkInterval(DateTimeUtil.getTimeDifference(talkNote.getCalleeOffhookTime(),
                    talkNote.getOnhookTime()));
        }
        final TalkNote note = talkNote;
        this.es.submit(() -> {
            try {
                ElevatorProcessService.this.businessService.saveOrUpdate(note);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
            ElevatorProcessService.this.businessService.saveTalkTask(new OutDataTask(note.getId(), OutDataType.TALK));
        });
    }

    public void endRecord(final TalkNote talkNote, VoiceRecord vr, final boolean transMp3) {
        if (vr.isEndFlag()) {
            vr.setInterval(DateTimeUtil.getTimeDifference(vr.getStartTime(), vr.getEndTime()));
        }
        final VoiceRecord record = vr;
        this.es.submit(() -> {
            if (talkNote.getId() == null) {
                try {
                    ElevatorProcessService.this.businessService.saveOrUpdate(talkNote);
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }
            }
            if (record.getTalkNoteId() == null) {
                record.setTalkNoteId(talkNote.getId());
            }
            if (!this.isSipFlag()) {
                String[] filePathArr = record.getFilePath().split(",");
                String filePath = filePathArr[0].substring(0, filePathArr[0].lastIndexOf("/"));
                String complexFileName = vr.getCallerNo() + "_" + vr.getStartTime().getTime() + "_LR.mp3";
                String complexFilePath = filePath + "/" + complexFileName;

                VoiceUtil.wavToMp3(filePathArr[0], filePathArr[1], complexFilePath);
                record.setFileName(complexFileName);
                record.setFilePath(complexFilePath);
            }
            ElevatorProcessService.this.businessService.saveOrUpdateVoiceRecord(record);
            ElevatorProcessService.this.businessService.saveTalkTask(new OutDataTask(talkNote.getId(), OutDataType.VOICE));
        });
    }

    public void saveOrUpdateRecord(final VoiceRecord vr) {
        this.es.submit(() -> {
            ElevatorProcessService.this.businessService.saveOrUpdateVoiceRecord(vr);
        });
    }

    @Transactional(readOnly = true)
    public Dept getRootDept(User user) {
        user = this.baseService.getUser(user.getId());
        return user.getRootDept();
    }

    public BindPhone getLocalBindPhone(String customNo, String lsh) {
        return this.baseService.getBindPhoneByCustomerNoAndLsh(customNo, lsh);
    }

    public boolean isMainServer() {
        return this.commonSettings.isMainServer();
    }

    @Transactional
    public void midwareUpdateAgentState(BindPhone bindPhone, String state) {
        if (this.commonSettings.isMainServer()) {
            this.baseService.modifyAgentStatus(bindPhone.getId(), state);
        } else {
            this.rmiService.modifyAgentStatus(bindPhone.getCustomerNo(), bindPhone.getLsh(), state);
        }
    }

    @Transactional
    public List<BindPhone> getIdleAgentsByDept(Dept dept, String callerNo) {
        if (this.commonSettings.isMainServer()) {
            return this.baseService.getIdleAgent(dept, callerNo);
        } else {
            return this.rmiService.getIdleAgent(dept.getCustomerNo(), dept.getDeptLsh(), callerNo);
        }
    }

    public String getAgentStateByAgentId(String agentId) {
        if (this.commonSettings.isMainServer()) {
            return this.baseService.getAgentStatusByAgentId(agentId);
        } else {
            return this.rmiService.getAgentStatusByAgentId(agentId);
        }
    }

    public String getFixedCallerNo(String callNo) {
        return this.baseService.processCallerNo(callNo);
    }

    public int getIvrRetryCount() {
        return this.midwareSetting.getIvrRetryCount();
    }

    public String getCustomRing(String ringName) {
        return this.midwareSetting.getCustomRingFile(ringName);
    }

    public int getIvrTimeoutSeconds() {
        return this.midwareSetting.getIvrTimeoutSeconds();
    }

    public String getFixedCalleeNo(String callNo) {
        return this.baseService.processCallNo(callNo);
    }

    public String getlocalNo() {
        return this.commonSettings.getLocalNo();
    }

    public boolean isInTest() {
        return this.commonSettings.isInTest();
    }

    public boolean isForeignCallerAccess(String num) {
        String foreignNum = this.commonSettings.getForeignPrefixNum();
        return foreignNum != null && foreignNum.contains(num);
    }

    public boolean isOriginalNoPrefix() {
        return this.commonSettings.isOriginalNoPrefix();
    }

    public boolean isKeepLocalDistrict(String caller) {
        return caller.length() <= 12 && this.commonSettings.isKeepCallerLocalDistrict();
    }

    public boolean isAddCallerNationCode() {
        return this.commonSettings.isAddCallerNationCode();
    }

    public CallNumDto getDefaultCallNumDto(String originNo, String caller, String callee) {
        CallNumDto dto = new CallNumDto();
        dto.setCallerNo(caller);
        dto.setCalleeNo(callee);
        dto.setOriginalNo(originNo);
        dto.setCallType(this.midwareSetting.isCallTypeTrans() ? CallNumDto.CALL_TYPE_TRANS : CallNumDto.CALL_TYPE_DIRECT);
        dto.setTransPrefix(this.commonSettings.isOrigCalleePrefix() ? CallNumDto.TRANS_PREFIX_ON
                : CallNumDto.TRANS_PREFIX_OFF);
        return dto;
    }

    public boolean isOrigCalleePrefix() {
        return this.commonSettings.isOrigCalleePrefix();
    }
    
    public boolean isOrigUseNetphone() {
        return this.commonSettings.isOrigUseNetphone();
    }

    public VoiceRecord startRecord(User user, String caller, String callee) {
        String prefix = "";
        if (this.baseService.isNotRecordConvertWhiteList(user.getNumber())) {
            log.debug("{} 开启用户监听！", user.getNumber());
            prefix = MONITOR_PREFIX;
        }
        Date now = new Date();
        VoiceRecord record = new VoiceRecord();
        record.setCalleeNo(callee);
        record.setCallerNo(caller);
        record.setStartTime(now);
        record.setCustomerNo(user.getNumber());
        Long timestamp = System.currentTimeMillis();
        String fileNameLeft = prefix + caller + "_" + timestamp + "_L.wav";
        String fileNameRight = prefix + caller + "_" + timestamp + "_R.wav";
        String fileName = fileNameLeft + "," + fileNameRight;
        String filePath = this.midwareSetting.getRecordPath() + "/" + DateTimeUtil.formatShortDate(now) + "/"
                + user.getNumber();
        File file = new File(filePath);
        if (!file.exists() || !file.isDirectory()) {
            file.mkdirs();
        }
        String filePathLeft = filePath + "/" + fileNameLeft;
        String filePathRight = filePath + "/" + fileNameRight;
        record.setFileName(fileName);
        record.setFilePath(filePathLeft + "," + filePathRight);
        return record;
    }

    public boolean isSipFlag() {
        return this.midwareSetting.isSipFlag();
    }

    public String getSipVoiceBoxPath() {
        return this.midwareSetting.getSipVoiceboxPath();
    }

    public void sendSmsMessage(SmsSendDto dto) {
        Message msg = new Message(MqConstants.MESSAGE_TOPIC, MqConstants.SMS_SEND_TAG, JsonHelper.Object2Json(dto).getBytes());
        SendResult result = mqProducer.send(msg);
        log.info("platform sms-messageid:{},topic:{}", result.getMessageId(), result.getTopic());
    }
}
