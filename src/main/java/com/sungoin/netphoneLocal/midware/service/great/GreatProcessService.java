/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.great;

import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.business.po.OutDataTask;
import com.sungoin.netphoneLocal.business.po.OutDataType;
import com.sungoin.netphoneLocal.business.po.TalkNote;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.business.service.BaseService;
import com.sungoin.netphoneLocal.business.service.BusinessService;
import com.sungoin.netphoneLocal.config.GreatSetting;
import com.sungoin.netphoneLocal.midware.config.MidwareSettings;
import com.sungoin.netphoneLocal.util.DateTimeUtil;
import com.sungoin.netphoneLocal.util.HttpHelper;
import com.sungoin.netphoneLocal.util.JsonHelper;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 *
 * <AUTHOR>
 */
@Service
public class GreatProcessService {
    
    private static final Logger log = LoggerFactory.getLogger(GreatProcessService.class);
    
    @Resource
    private MidwareSettings midwareSetting;
    
    @Resource
    private GreatSetting setting;
    
    @Resource
    private BaseService baseService;
    
    @Resource
    private BusinessService businessService;
    
    ExecutorService es = Executors.newCachedThreadPool();
    
    public ResponseDto getResult(RequestDto dto) {
        try {
            StringBuilder sb = new StringBuilder(setting.getGreatApiEndpoint());
            sb.append("?type=").append(dto.getType()).append("&code=").append(dto.getCode());
            String resp = HttpHelper.postHttpsParams(sb.toString(), null);
            return JsonHelper.json2Object(resp, ResponseDto.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }
    
    public String getFixedCallerNo(String callNo) {
        return this.baseService.processCallerNo(callNo);
    }
    
    public void saveOrUpdateTalkNote(final TalkNote talkNote, boolean async) {
        if (async) {
            this.es.submit(() -> {
                try {
                    GreatProcessService.this.businessService.saveOrUpdate(talkNote);
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }
            });
        } else {
            this.businessService.saveOrUpdate(talkNote);
        }
    }
    
    @Transactional
    public void endTalkNote(TalkNote talkNote) {
        if (talkNote.isEndFlag() && talkNote.getCalleeOffhookTime() != null) {
            talkNote.setTalkInterval(DateTimeUtil.getTimeDifference(talkNote.getCalleeOffhookTime(),
                    talkNote.getOnhookTime()));
        }
        final TalkNote note = talkNote;
        this.es.submit(() -> {
            try {
                GreatProcessService.this.businessService.saveOrUpdate(note);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
            GreatProcessService.this.businessService.saveTalkTask(new OutDataTask(note.getId(), OutDataType.TALK));
        });
    }

    public void saveOrUpdateTalkNote(final TalkNote talkNote) {
        saveOrUpdateTalkNote(talkNote, true);
    }
    
    @Transactional(readOnly = true)
    public Dept getRootDept(User user) {
        user = this.baseService.getUser(user.getId());
        return user.getRootDept();
    }
    
    public String getCustomRing(String ringName) {
        return this.midwareSetting.getCustomRingFile(ringName);
    }
    
    public int getIvrTimeoutSeconds() {
        return this.midwareSetting.getIvrTimeoutSeconds();
    }
}
