/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.midware.service;

import static com.sungoin.netphoneLocal.midware.MidwareConstants.*;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.sungoin.netphoneLocal.business.bean.BusinessConstants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.TeleThread;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.netphoneLocal.business.bean.CallNumDto;
import com.sungoin.netphoneLocal.business.mq.CallTraceDto;
import com.sungoin.netphoneLocal.business.mq.SmsSendDto;
import com.sungoin.netphoneLocal.business.po.BindPhone;
import com.sungoin.netphoneLocal.business.po.ColorRing;
import com.sungoin.netphoneLocal.business.po.CrbtRing;
import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.business.po.NumberFuncConfig;
import com.sungoin.netphoneLocal.business.po.TalkNote;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.business.po.VoiceBox;
import com.sungoin.netphoneLocal.business.po.VoiceRecord;
import com.sungoin.netphoneLocal.business.po.VoiceScore;
import com.sungoin.netphoneLocal.constants.CallOutErrorCode;
import com.sungoin.netphoneLocal.constants.FunctionSwitchConstants;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import com.sungoin.netphoneLocal.midware.exception.MidwareException;
import com.sungoin.netphoneLocal.util.CacheUtil;
import com.sungoin.netphoneLocal.util.CtiDes;
import com.sungoin.netphoneLocal.util.DateTimeUtil;
import com.sungoin.netphoneLocal.util.MyStringUtil;
import com.sungoin.netphoneLocal.util.SpringHelper;
import java.time.LocalDateTime;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR> 2015-7-28
 */
public class MainProcessThread extends TeleThread implements EventHandler {

    private static final long serialVersionUID = -5949673064795193501L;
    private static final Logger log = LoggerFactory.getLogger(MainProcessThread.class);
    private static final String SIP_LOCAL_PATH = "/usr/local/freeswitch";

    private transient ProcessService service;//序列化时忽略此域，反序列化时手动赋值
    private final String logPrefix;
    private final User user;
    private volatile Call mainCall;
    private volatile Call agent;
    private volatile List<Call> transList;
    private volatile Dept currentDept;
    private String currentRing;
    private volatile CrbtRing currentCrbtRing;
    private String fixedCallerNo;//处理过的主叫号码
    private String fixedCalleeNo;//处理过的被叫号码
    private volatile TalkNote talkNote;
    private volatile VoiceBox voiceBox;
    private volatile VoiceRecord voiceRecord;
    private volatile VoiceScore voiceScore;
    private List<BindPhone> agents;
    private volatile BindPhone currentBindPhone;
    private volatile boolean inQueue;
    private volatile int talkType = TALK_TYPE_IVR_NOT_CON;
    private volatile boolean inHold = false;
    private BindPhone missedBindPhone;
    private boolean leaveFromQueue;//是否从队列出来
    private volatile boolean isCallConnected = false;//是否呼通的标识

    //异步呼叫控制
    private final String makeCallSync = new String();
    private volatile boolean isMakingCall = false;
    private volatile boolean isCallOver = false;
    private boolean isCallSuccess = false;
    private boolean isRejectCall = false;
    private volatile int callErrorCode = -1;

    //IVR导航异步收码控制
    private final String ivrSync = new String();
    private boolean isIvrOver;
    private int ivrLength;
    private StringBuffer dtmf;
    private boolean detectSpeech = false;//语音识别标识
    private String speechText;//语音识别内容

    //当前导航进入时间
    private long ivrInTime;

    private boolean gotoSupport = false;
    private final NumberFuncConfig functionConfig;

    private boolean dualChannel;//双声道录音标识

    private boolean interactFlag;//接口客户标识
    
    //呼叫轨迹相关字段
    private String districtDesc = null;//归属地
    private volatile List<CallTraceDto> traceList = null;//呼叫轨迹
    
    //导航中进黑名单标识
    private boolean blackInIvr = false;
    private long whiteListXh;//白名单序号
    //主叫挂机标识
    private AtomicBoolean callerOnhooked = new AtomicBoolean(false);
    private volatile boolean isPlayingNumber = false;
    private boolean isIvrError = false;
    
    private volatile boolean securityDtmfFlag = false;//防伪码收码标识
    
    private volatile boolean talkNoteSaved = false;//话单是否保存标识，防止挂机在保存话单前执行
    
    private boolean queueUseable = false;
    private volatile boolean ivrTrans = false;//标识是转接进入的ivr
    
    private boolean multipleNumber = false;//多号同步标识
    
    /**
     * 反序列化时给transient域service赋值
     *
     * @param in
     * @throws IOException
     * @throws ClassNotFoundException
     */
    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        in.defaultReadObject();
        this.service = SpringHelper.getBean(ProcessService.class);
    }

    public MainProcessThread(Call mainCall, User user, TeleThreadEventListener listener, NumberFuncConfig config) {
        super(listener);
        Date now = new Date();
        this.user = user;
        this.mainCall = mainCall;
        this.logPrefix = mainCall.getCaller() + "-" + user.getNumber() + "-" + DateTimeUtil.formatTime(now);
        this.talkNote = new TalkNote(user.getNumber(), user.getOriginalNo(), null, now);
        this.transList = new ArrayList<>();
        this.functionConfig = config;
        this.dualChannel = user.isBaiduJimuyu() || (config != null && config.isRecordChannelFlag());
        this.interactFlag= (config != null && config.isInteractFlag());
        this.multipleNumber = StringUtils.isNotEmpty(user.getUniqueName());
        this.init();
    }
    
    public MainProcessThread(Call mainCall, User user, TeleThreadEventListener listener, NumberFuncConfig config, long whiteListXh) {
        this(mainCall, user, listener, config);
        this.whiteListXh = whiteListXh;
    }

    private void debug(String string, Object... os) {
        log.debug(this.logPrefix + string, os);
    }

    private void info(String string, Object... os) {
        log.info(this.logPrefix + string, os);
    }

    private void warn(String string, Object... os) {
        log.warn(this.logPrefix + string, os);
    }

    private void error(String string, Object... os) {
        log.error(this.logPrefix + string, os);
    }

    private void errorStackTrace(String string, Throwable throwable) {
        log.error(this.logPrefix + string, throwable);
    }

    public String getLogPrefix() {
        return this.logPrefix;
    }

    public Call getMainCall() {
        return this.mainCall;
    }

    public User getUser() {
        return this.user;
    }

    public Call getAgent() {
        return this.agent;
    }

    private void init() {
        if(this.user.isTraceFlag()) {
            this.traceList = new ArrayList<>();
        }
        this.service = SpringHelper.getBean(ProcessService.class);
        this.mainCall.attachTeleThread(this);
        //		this.mainCall.setParam(RECEIVED_DTMF, new StringBuffer());
        this.mainCall.setParam(CALL_TYPE, CALL_TYPE_CALLER);
        this.mainCall.setParam(MAIN_CALL_THREAD, this);
//        this.functionConfig = service.getNumberConfig(this.user.getNumber());
    }

    @Override
    public void run() {
        try {
            this.debug("开始业务流程！");
            int ret = this.answerOrAlert();
            if (this.user.isPreOffhook() && !this.service.isAutoTestCaller(this.mainCall.getCaller())) {
                //如果在摘机前，主叫已经挂机了，防止摘机时间晚于挂机时间，直接取挂机时间
                Date offHookTime = this.talkNote.getOnhookTime() == null ? new Date() : this.talkNote.getOnhookTime();
                this.talkNote.setCallerOffhookTime(offHookTime);
            }
            //处理主叫号码，补上区号
            this.fixedCallerNo = this.service.getFixedCallerNo(this.mainCall.getCaller());
            this.debug("处理过后的主叫号码：{}", this.fixedCallerNo);
            this.districtDesc = this.service.getDistrictDesc(this.fixedCallerNo);
            this.debug("主叫号码来电归属地：{}", this.districtDesc);
            this.talkNote.setCallerNo(this.getTalkNoteCaller());
            this.service.saveOrUpdateTalkNote(this.talkNote);
            this.debug("保存话单成功！");
            this.talkNoteSaved = true;
            //呼叫轨迹：用户来电
            if(this.user.isTraceFlag()) {
                this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), null, this.districtDesc, user.getNumber(), "用户来电", null));
            }
            if (ret != Constants.RET_SUCCESS) {
                throw new MidwareException(this.logPrefix + "对主叫：" + this.mainCall.toString() + "摘机失败！");
            }
            //播放根部门彩铃
            this.currentDept = this.service.getRootDept(this.user);
            this.debug("{}:根部门获取成功", this.user.getNumber());
            this.ivrInTime = System.currentTimeMillis();
            this.currentCrbtRing = this.service.getCrbtRingFile(this.currentDept);
            boolean ringToVoice = currentCrbtRing != null && currentCrbtRing.isToVoice();
            boolean ringToOnHook = currentCrbtRing != null && currentCrbtRing.isOnHook();
            if (ringToVoice) {
                //呼叫轨迹：转入炫铃
                if(this.user.isTraceFlag()) {
                    this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), null, this.districtDesc, "炫铃", "转入炫铃", null));
                }
//                int maxSeconds = this.user.isPreOffhook() ? 0 : 40;
                ret = this.playCrbtRing(false, false, 0);
                this.debug("炫铃进留言，放音返回：{}", ret);
                this.leaveMessage();
                return;
            } else if (ringToOnHook) {
                //呼叫轨迹：转入炫铃
                if(this.user.isTraceFlag()) {
                    this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), null, this.districtDesc, "炫铃", "转入炫铃", null));
                }
                ret = this.playCrbtRing(false, false, 0);
                this.debug("炫铃播放完主叫挂机，放音返回：{}", ret);
                this.mainCall.onHook();
                return;
            } else {
                this.info("开始播放根部门企业宣传彩铃。。。");
                this.playDeptCompanyRing(this.currentDept);
                if(isSmartBroadOpen(this.currentDept)) {
                    ret = this.playSmartBroadRing(this.currentDept);
                } else {
                    if(this.user.isAudioIvrFlag()) {
                        //语音识别
                        ret = detecteSpeech();
                        this.debug("ivr根部门语音识别返回：{}", ret);
                    } else {
                        ret = this.playCurrentDeptRing(true, true, true);
                        this.debug("ivr根部门放音返回：{}", ret);
                    }
                    //呼叫轨迹：转入炫铃
                    if(this.currentCrbtRing != null && this.user.isTraceFlag()) {
                        this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), null, this.districtDesc, "炫铃", "转入炫铃", null));
                    }
                }
            }
            //ivr导航
            Dept dept = this.getIvrDept();
            
            if(dept != null && user.isAudioIvrFlag() && !detectSpeech) {
                //强制中断语音识别
                this.mainCall.stopPlay();
            }
            this.debug("ivr导航找到的最终部门：{}", dept);
            if(dept != null && dept.isIvrSmsFlag() && MyStringUtil.isMobile(this.mainCall.getCaller())) {
                SmsSendDto dto = this.sendSmsMessage(3);
                this.debug("发送按键短信：{}", dto);
            }
            if (this.gotoSupport) {
                this.gotoSupport();
            } if (this.blackInIvr) {
                this.warn("主叫号码在导航黑名单中！");
                this.blackInIvr();
            } else if (dept == null) {
                this.isIvrError = true;
                this.callFail();
            } else if(dept.isVoiceCodeFlag()) {
                this.info("开始防伪码流程。。。");
                this.processSecurityCheck();
            } else if (!dept.isLoopPlayRing() && this.service.isBindPhoneEmpty(dept)) {
                //service.sendErrorMessage(this.user.getNumber(), new RuntimeException("导航:" + dept.getDeptLsh()+ "下无绑定号码"));
                this.leaveMessage();
            } else if (dept.isLoopPlayRing()) {
                this.info("开始循环播放彩铃。。。");
                this.playDeptRing(dept, true, true);
            } else {
                if (!this.currentDept.isRootDept()) {
                    this.currentCrbtRing = null;
                    this.info("开始播放子部门企业宣传彩铃。。。");
                    this.playDeptCompanyRing(this.currentDept);
                }
                this.queueUseable = this.service.isQueueUseable(this.currentDept);
                processCallAgent();
            }
        } catch (Exception ex) {
            this.errorStackTrace(ex.getMessage(), ex);
//            if (this.currentBindPhone != null
//                    && !this.service.getAgentState(this.currentBindPhone).equals(AGENT_STATE_IDLE)) {
//                this.warn("程序异常，将坐席状态置为空闲");
//                this.service.midwareUpdateAgentState(this.currentBindPhone, AGENT_STATE_IDLE, true);
//            }
            this.warn("程序异常，挂断主被叫");
            hangupBothCall();
            if(ex instanceof MidwareException || ex instanceof NullPointerException) {
            } else {
                this.service.sendErrorMessage(this.user.getNumber(), ex);
            }
        } finally {
            this.finished();
            this.debug("主线程结束！");
        }
    }
    
    private String getIvrErrorTip()  {
        String tip = this.service.getFunctionData(this.user.getNumber(), FunctionSwitchConstants.FUNCTION_CUSTOMER_IVR_QUIT_TIP);
        if(StringUtils.isEmpty(tip)) {
            tip = this.service.getIvrErrorQuitFile();
        }
        return tip;
    }

    private void processCallAgent() {
        int ret;
        boolean reenterQueue;
        int reenterQueueCount = 3;
        String callerId = this.fixedCallerNo + "_" + this.getName();
        do{
            reenterQueue = false;
            ret = this.processQueue();
            switch (ret) {
                case QUEUE_STATE_OFF:
                case QUEUE_STATE_INCOME:
                    //待接入
                    this.debug("可接入坐席！查询是否有空闲坐席");
                    this.agents = this.service.getIdleAgentsByDept(this.currentDept, this.fixedCallerNo);
                    if (this.agents.isEmpty()) {
                        this.info("当前部门下无空闲坐席。。。显示当前部门下坐席状态明细：");
                        this.callErrorCode = CallOutErrorCode.NO_IDLE_BINDPHONE;
                        this.warn(this.service.getDeptBindphoneDetails(this.currentDept));
                        if(ret == QUEUE_STATE_OFF) {
                            this.leaveMessage();
                        } else if(this.mainCall.getState() == Constants.CALL_CONTENT){
                            if(reenterQueueCount -- > 0) {
                                this.info("可接入时未找到空闲坐席，重新返回队列");
                                reenterQueue = true;
                            } else {
                                this.warn("超过3次重回队列，进入留言！");
                                this.service.removeQueue(this.currentDept, callerId, true);
                                this.leaveMessage();
                            }
                        }
                    } else {
                        //如果从队列中出来，或者当前未播放彩铃，播放彩铃
                        if (this.leaveFromQueue || !this.currentDept.isRootDept()) {
                            this.playCurrentDeptRing(true, true, true);
                        }
                        //开始呼叫坐席
                        ret = this.processMakeCall();
                        if(ret == MidwareConstants.CALL_RESULT_TO_QUEUE && this.mainCall.getState() == Constants.CALL_CONTENT) {
                            if(reenterQueueCount -- > 0) {
                                this.warn("呼叫时未找到空闲坐席，重新返回队列");
                                reenterQueue = true;
                            } else {
                                this.warn("超过3次重回队列，进入留言！");
                                this.service.removeQueue(this.currentDept, callerId, true);
                                this.leaveMessage();
                            }
                        }
                    }
                    break;
                case QUEUE_STATE_TIMOUT:
                    //超时
                    this.debug("排队超时，进入留言！。。。显示当前部门下坐席状态明细：");
                    this.warn(this.service.getDeptBindphoneDetails(this.currentDept));
                    this.leaveMessage();
                    break;
                case QUEUE_STATE_OVER:
                    this.debug("排队超时，按键错误，进入留言");
                    this.leaveMessage();
                    break;
                case QUEUE_STATE_OFFLINE:
                    this.debug("进入离线等待");
                    this.mainCall.play(this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_OFFLINE_TIP), false, false, false, 5, 5);
                    if(this.mainCall.getState() != Constants.CALL_IDLE) {
                        this.mainCall.onHook();
                    }
                    break;
                default:
                    //队列满
                    this.debug("队列满，进入留言！。。。显示当前部门下坐席状态明细：");
                    this.warn(this.service.getDeptBindphoneDetails(this.currentDept));
                    this.leaveMessage();
                    break;
            }
        } while(reenterQueue);
        if (this.service.queueExist(currentDept, callerId)) {
            this.service.removeQueue(this.currentDept, callerId, true);
        }
    }

    public void hangupBothCall() {
        if (this.mainCall.getState() != Constants.CALL_IDLE) {
            this.info("挂断主叫");
            this.mainCall.onHook();
        }
        handupAgent();
    }

    /**
     * 保存话单中的主叫号码 规则1.如果是手机，只保存11位真正手机号。 规则2.如果是固话，保存区号+固话
     *
     * @return
     */
    private String getTalkNoteCaller() {
        String caller = this.mainCall.getCaller();
        if (caller.startsWith("1") && caller.length() == 11) {
            //mobile,not change
        } else if (caller.startsWith("01") && caller.charAt(2) != '0') {
            //0+mobile, substring(1)
            caller = caller.substring(1);
        } else {
            //固话
            caller = this.fixedCallerNo;
        }
        try {
            caller = CtiDes.getInstance().encrypt(caller);
        } catch (Exception ex) {
            this.error(ex.getMessage(), ex);
        }
        return caller;
    }

    private int answerOrAlert() {
        if (this.user.isPreOffhook() && !this.service.isAutoTestCaller(this.mainCall.getCaller())) {
            this.debug("用户开通了预摘机功能！对来电摘机！");
            this.mainCall.answerSync(3000);
//            if (this.mainCall.getState() != Constants.CALL_CONTENT) {
//                this.await(500);
//            }
            this.debug("对来电摘机！来电状态为：" + this.getCallStateDesc(this.mainCall.getState()));
            return this.mainCall.getState() == Constants.CALL_CONTENT ? 1 : 0;
        } else {
            this.debug("用户未开通预摘机功能！对来电振铃！");
            this.mainCall.alert();
            this.await(500);
            if (this.mainCall.getState() != Constants.CALL_ALERT) {
                this.await(1000);
            }
            if (this.mainCall.getState() != Constants.CALL_ALERT) {
                this.await(1000);
            }
            this.debug("对来电振铃！来电状态为：" + this.getCallStateDesc(this.mainCall.getState()));
            return this.mainCall.getState() == Constants.CALL_ALERT ? 1 : 0;
        }
    }
    
    private boolean isTalkNoteNotConnected() {
        return this.talkNote.getTalkType() == 1 || this.talkNote.getTalkType() == 2 || this.talkNote.getTalkType() == 5;
    }
    
    @Override
    public void callerOnhook() {
        if (this.callerOnhooked.compareAndSet(false, true)) {
            this.debug("主叫挂机！");
//            if(this.isPlayingNumber) {
//                this.debug("主叫在报工号中挂机");
//                this.agent.stopPlay();
//            }
            //中断被叫放音
            if(this.agent != null && this.agent.getState() == Constants.CALL_CONTENT) {
                this.agent.stopPlay();
            }
            if (this.isMakingCall) {
                this.debug("正在呼叫被叫，终止呼叫。。。");
                this.handupAgent();
            }
            String callerId = this.fixedCallerNo + "_" + this.getName();
            if (this.service.queueExist(currentDept, callerId)) {
                this.debug("主叫在队列中挂机，移除队列:{}", this.currentDept.getId());
                this.service.removeQueue(this.currentDept, callerId, true);
                this.inQueue = false;
            }
            synchronized (this.ivrSync) {
                this.ivrSync.notifyAll();
            }
            if(!this.talkNoteSaved) {
                this.warn("挂机时话单还未保存！");
                this.await(1000);
            }
            Date now = new Date();
            if (this.voiceBox != null) {
                this.talkType = TALK_TYPE_VOICE_BOX;
                this.voiceBox.setEndTime(now);
                this.voiceBox.setEndFlag(true);
                this.service.endVoiceBox(this.talkNote, this.voiceBox);
            }
            if (this.voiceRecord != null) {
                this.voiceRecord.setEndTime(now);
                this.voiceRecord.setEndFlag(true);
                this.service.endRecord(this.talkNote, this.voiceRecord, this.user.transMp3(), dualChannel);
            }
            if (this.talkNote.getCallerOnHook() == null) {
                this.talkNote.setCallerOnHook(true);
            }
            if (this.talkNote.getDeptId() == null) {
                this.talkNote.setDeptId(this.currentDept == null ? this.user.getNumber() : this.currentDept.getDeptLsh());
            }
            if (!this.isCallConnected && isTalkNoteNotConnected()) {
                //未接电话补上坐席信息
                BindPhone lastBindPhone = this.currentBindPhone != null ? this.currentBindPhone
                        : this.missedBindPhone != null ? this.missedBindPhone : null;
                if (lastBindPhone != null) {
                    this.talkNote.setCalleeNo(lastBindPhone.getOrigBindPhoneNo());
                    this.talkNote.setGh(StringUtils.isNumeric(lastBindPhone.getReportNum()) ? Long.valueOf(lastBindPhone
                            .getReportNum()) : null);
                    this.talkNote.setLsh(lastBindPhone.getId());
                }
                //未接原因
                if (this.callErrorCode < 0) {
                    this.callErrorCode = CallOutErrorCode.MAINCALL_ONHOOK;
                }
                this.talkNote.setErrorCode(callErrorCode);
            }
            if (this.talkNote.getCallerNo() == null) {
                this.fixedCallerNo = this.service.getFixedCallerNo(this.mainCall.getCaller());
                this.talkNote.setCallerNo(this.getTalkNoteCaller());
            }
            this.talkNote.setEndFlag(true);
            this.talkNote.setOnhookTime(now);
            this.talkNote.setTalkType(this.talkType);
            this.service.endTalkNote(this.talkNote);
            this.mainCall.getAllParam().clear();
            this.handupAgent();
            this.transList.clear();
            //发送短信
            if (this.user.isPlatformPlus()) {
                if (this.isTalkNoteNotConnected() && this.user.isMissSmsFlag()) {
                    SmsSendDto dto = sendSmsMessage(2);
                    this.debug("发送漏接短信：{}", JSON.toJSON(dto));
                }
                if (this.user.isSmsFlag() && MyStringUtil.isMobile(this.mainCall.getCaller())) {
                    SmsSendDto dto = sendSmsMessage(1);
                    this.debug("发送挂机短信：{}", JSON.toJSON(dto));
                }
            }
            //扣除白名单次数
            if (this.whiteListXh != 0 && !this.isTalkNoteNotConnected() && talkNote.getTalkInterval() > 5 * 60) {
                this.debug("扣除白名单次数，白名单序号：{}", this.whiteListXh);
                this.service.subtractWhiteListCount(user.getNumber(), this.whiteListXh);
            }
            this.debug("推送轨迹标识：{}", this.user.isTraceFlag());
            //呼叫轨迹：用户挂机
            if (user.isTraceFlag()) {
                String route = this.user.getNumber();
                if (this.voiceBox != null) {
                    route = "语音信箱";
                } else if (this.voiceScore != null) {
                    route = "满意度";
                } else if (this.currentCrbtRing != null) {
                    route = "炫铃";
                } else if (this.currentDept != null) {
                    route = this.currentDept.getName();
                }
                if (this.user.isTraceFlag()) {
                    this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), this.currentBindPhone == null ? null : this.currentBindPhone.getOrigBindPhoneNo(), this.districtDesc, route, "用户挂机", this.currentDept == null ? null : this.currentDept.getDeptLsh()));
                }
            }
            if (this.user.isTraceFlag()) {
                this.await(2000);
                this.traceList.forEach(dto -> {
                    dto.setUqid(this.talkNote.getId());
                });
                this.debug("推送轨迹消息");
                this.sendTraceMessage();
            }
        }
    }
    
    private SmsSendDto sendSmsMessage(int type) {
        SmsSendDto dto = new SmsSendDto();
        dto.setType(type);
        dto.setRecordId(this.talkNote.getId());
        dto.setNumberCode(this.user.getNumber());
        dto.setCallerNo(this.mainCall.getCaller());
        dto.setCalleeNo(this.currentBindPhone != null ? this.currentBindPhone.getOrigBindPhoneNo() : "");
        dto.setDeptId(this.currentDept != null ? this.currentDept.getDeptLsh() : "");
        dto.setCallTime(DateTimeUtil.format(this.talkNote.getIncomingTime(), DateTimeUtil.Pattern.DATETIME));
        this.service.sendSmsMessage(dto);
        return dto;
    }

    @Override
    public void calleeOnhook() {
        this.debug("坐席挂机");
//        if(this.isPlayingNumber) {
//            this.debug("坐席在报工号中挂机");
//            this.mainCall.stopPlay();
//        }
        //中断主叫放音
        this.mainCall.stopPlay();
        String bindPhoneId = (String) this.agent.getParam(CALL_BINDPHONE_ID);
        String agentId = (String) this.agent.getParam(CALL_AGENT_ID);
        Object hasNext = this.agent.getParam(MidwareConstants.CALL_HAS_NEXT);
        this.debug("坐席挂机，后续坐席：{}， 主叫状态：{}", hasNext, this.mainCall.getState());
        if (hasNext == null) {
            this.debug("不存在转移坐席，停止录音...");
            this.mainCall.stopRecord();
        }
        String customerNo;
        BindPhone bp = this.service.getLocalBindPhone(bindPhoneId);
        if (bp == null) {
            this.warn("根据绑定号码ID:{},找不到绑定号码，用户可能重新同步过绑定号码，改用流水号查找", bindPhoneId);
            String[] lsh = ((String) this.agent.getParam(CALL_BINDPHONE_LSH)).split("_");
            customerNo = lsh[0];
            bp = this.service.getLocalBindPhone(lsh[0], lsh[1]);
        } else {
            customerNo = bp.getCustomerNo();
        }
        CacheUtil.remove(CacheUtil.CACHE_CALL_PROCESS_NAME, customerNo + "_" + bindPhoneId);
        if (this.talkNote.getCallerOnHook() == null) {
            this.talkNote.setCallerOnHook(false);
        }
        if (bp != null) {
            this.debug("查找到坐席的绑定号码：{}", bp);
            if (!bp.isPbx()) {
                this.debug("坐席挂机通知中心端");
                this.service.calleeOnhook(bp, this.isCallConnected, false, this.talkNote, this.functionConfig);
            } else {
                if (!this.service.getAgentState(bp).equals(MidwareConstants.AGENT_STATE_IDLE)) {
                    this.warn("坐席开通了交换机，但是坐席状态是不空闲，修改坐席状态为空闲");
                    this.service.midwareUpdateAgentState(bp, MidwareConstants.AGENT_STATE_IDLE, true);
                }
                this.debug("坐席挂机通知减少总机接通数量");
                this.service.decreasePbxCount(bp);
            }
        } else {
            this.warn("找不到绑定号码，绑定号码可能被删除！");
            if (this.currentBindPhone != null) {
                if (this.currentBindPhone.isPbx()) {
                    this.info("减少全局中继通话数量！");
                    this.service.decreaseGloablePbxCount(customerNo, this.currentBindPhone.getOrigBindPhoneNo());
                } else {
                    String state = user.isSetAcsType() ? MidwareConstants.AGENT_STATE_BUSY : MidwareConstants.AGENT_STATE_IDLE;
                    if(this.multipleNumber) {
                        this.info("根据绑定号码：{}，批量更新坐席状态为{}！", this.currentBindPhone.getBindPhoneNo(), state);
                        this.service.midwareBatchUpdateAgentStateByBindPhone(this.currentBindPhone, state, this.user.getPushStatusFlag(), user.getUniqueName());
                    } else {
                        this.info("根据坐席ID:{} 更新坐席状态为{}！", this.currentBindPhone.getAgentId(), state);
                        this.service.midwareUpdateAgentStateByAgentId(this.currentBindPhone,  state, this.user.getPushStatusFlag());
                    }
                }
            }
        }
        //呼叫轨迹：坐席挂机
        if(this.user.isTraceFlag()) {
            this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), bp == null ? null : bp.getOrigBindPhoneNo(), this.districtDesc, this.currentDept.getName(), "坐席挂机", this.currentDept.getDeptLsh()));
        }
        this.agent.getAllParam().clear();
        if (hasNext == null && this.mainCall.getState() == Constants.CALL_CONTENT) {
            if (this.talkNote.getCalleeOffhookTime() == null) {
                //坐席尚未摘机，进入留言
                this.leaveMessage();
            } else {
                this.processSatisfy();
            }
        }
    }

    @Override
    public void transOnhook(Call trans) {
        this.debug("转移的坐席挂机！");
        String bindPhoneId = (String) trans.getParam(CALL_BINDPHONE_ID);
        String customerNo;
        BindPhone bp = this.service.getLocalBindPhone(bindPhoneId);
        if (bp == null) {
            this.warn("根据绑定号码ID:{},找不到绑定号码，用户可能重新同步过绑定号码，改用流水号查找", bindPhoneId);
            String[] lsh = ((String) trans.getParam(CALL_BINDPHONE_LSH)).split("_");
            customerNo = lsh[0];
            bp = this.service.getLocalBindPhone(lsh[0], lsh[1]);
        } else {
            customerNo = bp.getCustomerNo();
        }
        CacheUtil.remove(CacheUtil.CACHE_CALL_PROCESS_NAME, customerNo + "_" + bindPhoneId);
        if (bp != null) {
            if (!bp.isPbx()) {
                this.service.calleeOnhook(bp, this.talkNote, this.functionConfig);
            } else {
                this.service.decreasePbxCount(bp);
            }
        } else {
            this.warn("找不到绑定号码，绑定号码可能被删除！");
        }
        //呼叫轨迹：坐席挂机
        if(this.user.isTraceFlag()) {
            this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), bp == null ? null : bp.getOrigBindPhoneNo(), this.districtDesc, this.currentDept.getName(), "坐席挂机", this.currentDept.getDeptLsh()));
        }
        if (trans.getParam(MidwareConstants.CALL_HAS_NEXT) == null
                && this.mainCall.getState() == Constants.CALL_CONTENT) {
            this.processSatisfy();
        }
        trans.getAllParam().clear();
    }

    private void processSatisfy() {
        this.debug("进入满意度判断，当前主叫状态：{}，主被叫是否连接成功：{}", this.mainCall.getState(), this.isCallConnected);
        if (this.mainCall.getState() == Constants.CALL_CONTENT && this.isCallConnected) {
            //先停止录音，原来的坐席通道可能被其他通话使用，防止录入其他通话的内容
            this.mainCall.stopRecord();
            if (!this.user.isSatiFlag()) {
                this.debug("用户未开通满意度评价");
            } else if(this.voiceScore != null) {
                this.warn("已经存在满意度记录！");
            } else {
                this.voiceScore = this.service.startSatisfy(this.talkNote);
                String ring = this.service.getSatiRing(this.currentDept);
                this.debug("满意度彩铃：{}", ring);
                this.mainCall.play(ring, false, false, true, 0, 0);
                String key = this.mainCall.receiveDTMFSync(1, null, 30);
                this.debug("用户输入的满意度为：{}", key);
                //呼叫轨迹：满意度结果
                if(this.user.isTraceFlag()) {
                    this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), null, this.districtDesc, "满意度", "满意度按键：" + key, null));
                }
                if (StringUtils.isNumeric(key)) {
                    this.voiceScore.setResult(Integer.parseInt(key));
                }
                this.voiceScore.setAgentId(this.currentBindPhone.getAgentId() == null ? this.currentBindPhone.getLsh()
                        : this.currentBindPhone.getAgentId());
                this.voiceScore.setEndFlag(true);
                this.service.endVoiceScore(this.talkNote, this.voiceScore);
                if ("4009960826".equals(this.user.getNumber())) {
                    ring = this.service.getDefaultRing("4009960826_score_end.wav");
                } else {
                    ring = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_SCORE_END);
                }
                this.debug("满意度结束彩铃：{}", ring);
                this.mainCall.play(ring, false, false, false, 30, 0);
            }
            this.mainCall.onHook();
        }
    }
    
    private int detecteSpeech() {
        String ring;
        if (this.currentCrbtRing == null && this.currentDept.isBgRing()) {
            ring = this.service.getDefaultRing(this.currentDept.getColorRingLsh() + ".wav");
            this.debug("此部门配置了背景音乐：{}", ring);
        } else if (this.currentCrbtRing == null && MidwareConstants.DEFAULT_RING_TRANSFER_DEPT_CODE.equals(this.currentDept.getColorRingLsh())) {
            this.debug("此部门配置了默认彩铃：{}.wav" , MidwareConstants.DEFAULT_RING_TRANSFER_DEPT_CODE);
            ring = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_TRANSFER_DEPT_CODE);
            if (ring == null) {
                ring = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_TRANSFER_DEPT);
            }
        } else {
            ring = this.service.getRingFile(this.currentDept);
        }
        this.debug("语音识别获得部门：{}的彩铃文件：{}", this.currentDept.getName(), ring);
        if(ring == null) {
            log.warn("未找到彩铃，忽略语音识别！");
            return 0;
        }
        this.currentRing = ring;
        int timeoutSecond = this.currentDept.getIvrStayTime() != null ? this.currentDept.getIvrStayTime() + 1 : this.service.getIvrTimeoutSeconds();
        if (this.functionConfig != null && this.functionConfig.getInputTimeOut() != null) {
            timeoutSecond = functionConfig.getInputTimeOut();
        }
        return this.mainCall.detectSpeech(ring, timeoutSecond);
    }

    /**
     * 播放炫铃
     * @param async
     * @param loop
     * @return 
     */
    private int playCrbtRing(boolean async, boolean loop, int maxSeconds) {
        return this.currentCrbtRing != null ? this.mainCall.play(this.service.getRingFile(this.currentCrbtRing), loop, false, async, 0, maxSeconds) : 0;
    }
    
    /**
     *
     * @param playDefault 是否播放默认彩铃
     * @param async 是否异步播放
     * @param loop 是否循环播放
     * @return                                              
     */
    private int playCurrentDeptRing(boolean playDefault, boolean async, boolean loop) {
        String ring;
        if (this.currentDept.isBgRing() || this.currentDept.isDefaultRing()) {
            ring = this.service.getDefaultRing(this.currentDept.getColorRingLsh() + ".wav");
            this.debug("此部门配置了默认彩铃：{}", ring);
        } else {
            ring = this.service.getRingFile(this.currentDept);
            if ((ring == null || !this.service.isRingExist(ring)) && playDefault) {
                ring = this.service.getDefaultRing(DEFAULT_RING_DUDU);
                this.debug("未找到部门：{}的彩铃！使用默认彩铃{}", this.currentDept.getName(), ring);
            }
        }
        this.debug("获得部门：{}的彩铃文件：{}", this.currentDept.getName(), ring);
        this.currentRing = ring;
        return this.currentRing != null ? this.mainCall.play(this.currentRing, loop, false, async, 0, 0) : 1;
    }

    private void playInputErrorRing(boolean async) {
        String ring = this.service.getInputErrorFile();
        this.mainCall.play(ring, false, false, async, 0, 0);
    }

    private void playTimeOutInputErrorRing(boolean async) {
        String ring = this.service.getQueueKeyErrorFile();
        this.debug("播放按键错误提示音：{}", ring);
        this.mainCall.play(ring, false, false, async, 0, 0);
    }

    private String receiveIvrDtmf(Dept dept) {
        int keyLength = dept.getChildKeyLen();
        int timeoutSecond = dept.getIvrStayTime() != null ? dept.getIvrStayTime() + 1 : this.service.getIvrTimeoutSeconds();
        String returnKey = null;
        if (this.functionConfig != null) {
            if (functionConfig.getInputTimeOut() != null) {
                timeoutSecond = functionConfig.getInputTimeOut();
                this.debug("号码配置了按键收码等待时长：{}", timeoutSecond);
            }
            if (functionConfig.getInputTimeOutDtmf() != null) {
                returnKey = functionConfig.getInputTimeOutDtmf() + "";
                this.debug("号码配置了按键收码超时返回：{}", returnKey);
            }
        }
        this.debug("发送收码请求到cti-server，开始收码。。。");
        return this.receiveIvrDtmf(keyLength, timeoutSecond, returnKey);
    }

    private String receiveIvrDtmf(int length, int timeoutSecond, String timeoutReturnKey) {
        try {
            this.dtmf = new StringBuffer();
            this.mainCall.receiveDTMFAsync();
            synchronized (this.ivrSync) {
                this.ivrLength = length;
                this.isIvrOver = false;
                this.detectSpeech = false;
                this.speechText = null;
                while (!this.isIvrOver) {
                    this.ivrSync.wait(timeoutSecond * 1000);
                    if (!this.isIvrOver) {
                        if (this.mainCall.getState() != Constants.CALL_CONTENT) {
                            this.warn("收码异常！主叫挂机！");
                            return null;
                        }
                        this.warn("ivr 收码超时！");
                        this.mainCall.stopReceiveDTMF();
                        if(this.user.isAudioIvrFlag()) {
                            this.mainCall.stopDetectSpeech();
                        }
                        return timeoutReturnKey;
                    }
                }
            }
            this.mainCall.stopReceiveDTMF();
            if(this.user.isAudioIvrFlag()) {
                this.mainCall.stopDetectSpeech();
                if(!this.detectSpeech) {
                    this.mainCall.stopPlay();
                }
            }
            return this.dtmf.toString();
        } catch (Exception ex) {
            this.errorStackTrace(ex.getMessage(), ex);
            return null;
        }
    }

//    public void ivrReceived(String key) {
//		synchronized (this.ivrSync) {
//			this.dtmf.append(key);
//			boolean isOver = false;
//			if (this.user.isSuperIvrFlag() && this.dtmf.length() == 1 && key.equals("0")) {
//				this.info("用户按键复合超级导航！退出同步块！");
//				isOver = true;
//			} else if (this.dtmf.length() == this.ivrLength) {
//				this.info("用户按键长度复合部门设置！退出同步块！");
//				isOver = true;
//			} else if (key.equals("#")) {
//				this.info("用户按#号键！退出同步块！");
//				if (this.dtmf.length() > 1) {
//					this.dtmf.deleteCharAt(this.dtmf.length() - 1);
//				}
//				isOver = true;
//			} else if (key.equals("*")) {
//				this.info("用户按*号键！退出同步块！");
//				if (this.dtmf.length() > 1) {
//					this.dtmf.deleteCharAt(this.dtmf.length() - 1);
//				}
//				isOver = true;
//			} else if(this.user.isDirectNumber() && this.dtmf.toString().equals(this.service.getSupportIvrKey())) {
//                this.info("用户按键长度复合提示语音按键！退出同步块！");
//                isOver = true;
//            }
//			if (isOver) {
//				this.isIvrOver = true;
//				this.ivrSync.notifyAll();
//			}
//		}
//	}
    
    @Override
    public void detectSpeech(String text) {
        //语音识别
        this.debug("收到语音输入：{}", text);
        synchronized (this.ivrSync) {
            this.isIvrOver = true;
            this.detectSpeech = true;
            this.speechText = text;
            this.ivrSync.notifyAll();
        }
    }
    
    @Override
    public void ivrReceived(String key) {
        this.debug("收到用户按键：{}", key);
        this.dtmf.append(key);
        synchronized (this.ivrSync) {
            if (!this.isIvrOver) {
                boolean isOver = false;
                char firstChar = dtmf.charAt(0);
                if (!this.securityDtmfFlag && firstChar == '0' && dtmf.length() == 1) {
                    //首字母为0，若开通了超级导航，直接返回，否则等待2秒，判断是否是提示彩铃
                    if (this.user.isSuperIvrFlag()) {
                        this.info("用户按键符合超级导航！退出同步块！");
                        isOver = true;
                    } else {
                        if (this.user.isDirectNumber()) {
                            this.await(2000);
                            String ivr = this.dtmf.toString();
                            this.debug("等待2秒后收到的按键：{}", ivr);
                            if (ivr.equals(this.service.getSupportIvrKey())) {
                                this.info("用户按键长度复合提示语音按键！退出同步块！");
                                isOver = true;
                            }
                        }
                        if (this.dtmf.length() >= this.ivrLength) {
                            this.info("用户按键长度复合部门设置！退出同步块！");
                            isOver = true;
                        }
                    }
                } else {
                    if ("#".equals(key)) {
                        this.info("用户按#号键！退出同步块！");
                        if (!this.securityDtmfFlag && this.dtmf.length() > 1) {
                            this.dtmf.setLength(0);
                            this.dtmf.append("#");
                        }
                        isOver = true;
                    } else if ("*".equals(key)) {
                        this.info("用户按*号键！退出同步块！");
                        if (!this.securityDtmfFlag && this.dtmf.length() > 1) {
                            this.dtmf.setLength(0);
                            this.dtmf.append("*");
                        }
                        isOver = true;
                    } else if (this.dtmf.length() == this.ivrLength) {
                        this.info("用户按键长度复合部门设置！退出同步块！");
                        isOver = true;
                    }
                }
                if (isOver) {
                    this.isIvrOver = true;
                    this.ivrSync.notifyAll();
                }
            }
        }
    }

    //语音播报功能是否开启
    private boolean isSmartBroadOpen(Dept dept) {
        return this.user.isSmartBroadFlag() && dept.isSmartBroadFlag();
    }
    
    private Dept getIvrDept() {
        Dept childDept = null;
        int keyErrorCount = 0;
        int ivrRetryCount = this.service.getIvrRetryCount();
        String ivr = null;
        boolean firstInLoop = true;
        String errorTipRing = this.service.getFunctionData(this.user.getNumber(), FunctionSwitchConstants.FUNCTION_CUSTOMER_IVR_ERROR_TIP);
        while (this.currentDept != null && keyErrorCount < ivrRetryCount && (isSmartBroadOpen(this.currentDept) || this.service.hasChildDeptsBySql(this.currentDept))) {
            if (this.mainCall.getState() == Constants.CALL_IDLE) {
                throw new MidwareException("主叫已挂机！");
            }
            if (this.currentDept.isLoopPlayRing()) {
                this.info("当前部门配置了循环播放彩铃", this.currentDept);
                return this.currentDept;
            }
            int childType = this.currentDept.getChildinType();
            if (childType == CHILDTYPE_DIVIDE) {
                //分流进入
                childDept = this.service.getChildDeptByDivide(this.currentDept, this.fixedCallerNo);
                this.debug("当前部门：{}，分流进入下级部门，找到的下级部门：{}", this.currentDept, childDept);
                this.currentDept = childDept;
            } else if (this.functionConfig != null && this.functionConfig.isIvrTransferFlag()) {
                //自动按键进入
                String key = this.functionConfig.getInputKey();
                childDept = this.service.getChildDeptByIvrKey(this.currentDept, key);
                this.currentDept = childDept;
                this.debug("当前部门：{}，自动按键：{}，找到的下级部门：{}", this.currentDept, key, childDept);
            } else {
                if ("*".equals(ivr) && this.currentDept.getResetRing() != null) {
                    this.debug("用户输入*号键,播放按键重播彩铃");
                    playDeptResetRing(currentDept);
                } else if (!firstInLoop) {
                    if(isSmartBroadOpen(this.currentDept)) {
                        //开通智能播报，判断是否发送按键短信
                        if(this.currentDept.isIvrSmsFlag() && MyStringUtil.isMobile(this.mainCall.getCaller())) {
                            SmsSendDto dto = this.sendSmsMessage(3);
                            this.debug("发送按键短信：{}", dto);
                        }
                        this.playSmartBroadRing(this.currentDept);
                    } else {
                        if(this.user.isAudioIvrFlag()) {
                            log.debug("用户开启了语音识别功能！");
                            this.detecteSpeech();
                        } else {
                            this.playCurrentDeptRing(false, true, true);
                        }
                    }
                }
                if (this.currentRing == null || this.currentRing.equals(this.service.getDefaultRing(DEFAULT_RING_DUDU))) {
                    this.warn("按键进入的部门，父部门没有配置彩铃！终止呼叫！");
                    service.sendErrorMessage(this.user.getNumber(), new RuntimeException("按键进入的部门：" + this.currentDept.getDeptLsh() + "，父部门没有配置彩铃"));
                    return null;
                }
                //按键进入
                ivr = this.receiveIvrDtmf(this.currentDept);
                if (this.mainCall.getState() == Constants.CALL_IDLE) {
                    throw new MidwareException("主叫已挂机！");
                }
                if(this.detectSpeech) {
                    ivr = this.speechText;
                }
                this.debug(this.detectSpeech ? "用户输入的语音为：{}" : "用户输入的按键为：{}", ivr);
                //判断是否超过导航停留时长
                if (this.currentDept.getIvrStayTime() != null && (System.currentTimeMillis() - this.ivrInTime) / 1000 > this.currentDept.getIvrStayTime()) {
                    this.warn("超过ivr停留时长" + this.currentDept.getIvrStayTime() + "！");
                    return null;
                }
                if (StringUtils.isNotEmpty(ivr)) {
                    if (this.user.isDirectNumber() && ivr.equals(this.service.getSupportIvrKey())) {
                        this.gotoSupport = true;
                        return null;
                    }
                    if(this.isBlackInIvr(ivr)) {
                        this.blackInIvr = true;
                        return null;
                    }
                    childDept = findDeptByIvr(ivr);
                } else {
                    if(isSmartBroadOpen(this.currentDept)) {
                        this.warn("语音播报导航中未收到按键，终止导航！");
                        return null;
                    }
                    childDept = null;
                }
                if (childDept != null) {
                    this.currentDept = childDept;
                    keyErrorCount = 0;
                } else if (keyErrorCount == ivrRetryCount - 1) {
                    return null;
                } else {
                    keyErrorCount++;
                    if(this.detectSpeech) {
                        //识别到语音，未找到导航
                        String speechTip = service.getDefaultRing(SPEECH_ERROR_TIP);
                        this.debug("播放语音导航错误提示彩铃：{}", speechTip);
                        this.mainCall.play(speechTip, false, false, false, 5, 0);
                    } else {
                        //按键类型的播放错误提示音
                        if(StringUtils.isNotEmpty(errorTipRing)) {
                            //自定义错误提示音
                            this.mainCall.play(errorTipRing, false, false, false, 10, 0);
                        } else {
                            this.playInputErrorRing(true);
                            ivr = this.mainCall.receiveDTMFSync(1, null, 15);
                            this.debug("用户按键错误后,再次输入的按键为：{}", ivr);
                            //判断是否超过导航停留时长
                            if (this.currentDept.getIvrStayTime() != null && (System.currentTimeMillis() - this.ivrInTime) / 1000 > this.currentDept.getIvrStayTime()) {
                                this.warn("超过ivr停留时长" + this.currentDept.getIvrStayTime() + "！");
                                return null;
                            }
                            if (StringUtils.isNotEmpty(ivr)) {
                                if("0".equals(ivr) && this.currentDept.getChildKeyLen() > 1 && !this.user.isSuperIvrFlag()) {
                                    ivr = "*";
                                }
                                childDept = findDeptByIvr(ivr);
                            }
                            if(childDept != null) {
                                this.currentDept = childDept;
                            }
                        }
                    }
                }
            }
            if (this.currentDept != null) {
                this.ivrInTime = System.currentTimeMillis();
            }
            firstInLoop = false;
        }
        return this.currentDept;
    }
    
    private Dept findDeptByIvr(String ivr) {
        Dept childDept;
        if(this.detectSpeech) {
            childDept = this.service.findDeptByKeyWords(this.user.getNumber(), ivr);
            this.debug("根据语音输入：{}，找到的部门：{}", ivr, childDept);
        } else {
            switch (ivr) {
                case "#":
                    childDept = this.service.getParentInputDept(this.currentDept);
                    this.debug("用户输入#号键，返回上级部门：{}", childDept);
                    break;
                case "*":
                    childDept = this.currentDept;
                    this.debug("用户输入*号键，重听当前部门", this.currentDept, childDept);
                    break;
                default:
                    childDept = this.service.getChildDeptByIvrKey(this.currentDept, ivr);
                    this.debug("当前部门：{}，按键进入下级部门，找到的下级部门：{}", this.currentDept, childDept);
                    break;
            }
        }
        return childDept;
    }

    private void callFail() {
        String failRing = null;
        if(this.isIvrError) {
            failRing = this.getIvrErrorTip();
        } else {
            failRing = this.service.getDefaultRing(DEFAULT_RING_FAIL);
        }
        this.info("播放挂机前提示音:{}", failRing);
        this.mainCall.play(failRing, false, false, false, 30, 0);
        if (this.mainCall.getState() != Constants.CALL_IDLE) {
            this.mainCall.onHook();
        }
    }

    private void leaveMessage() {
        this.debug("用户进入留言！");
        //20180525去掉漏接通知
        //		if (this.missedBindPhone != null) {
        //			this.service.missedCall(this.fixedCallerNo, this.missedBindPhone);
        //		}   
        if (this.mainCall.getState() == Constants.CALL_IDLE) {
            throw new MidwareException("主叫已挂机！");
        }
        this.stopMainCallPlay();
        if (!this.user.isVoiceBoxFlag() || this.service.isAutoTestCaller(this.mainCall.getCaller())) {
            this.info("用户未开通留言功能！");
            this.callFail();
        } else {
            if (!this.user.isPreOffhook()) {
                this.debug("用户没有开通预摘机，留言前先对用户摘机！");
                int ret = this.mainCall.answerSync(3000);
                if (ret == Constants.RET_SUCCESS ) {
                    //如果在摘机前，主叫已经挂机了，防止摘机时间晚于挂机时间，直接取挂机时间
                    Date offHookTime = this.talkNote.getOnhookTime() == null ? new Date() : this.talkNote.getOnhookTime();
                    this.talkNote.setCallerOffhookTime(offHookTime);
                } else {
                    this.error("对用户摘机失败！");
                    return;
                }
            }
            //播放提示音前就算留言记录。2017/9/1 运维要求。
            this.voiceBox = this.service.startVoicebox(this.talkNote.getCallerNo(), this.user.getNumber(),
                    this.currentDept);
            this.voiceBox.setTalkNoteId(this.talkNote.getId());
            this.service.saveOrUpdateVoiceBox(this.voiceBox);
            this.talkNote.setOutgoingTime(this.voiceBox.getStartTime());
//            this.talkType = TALK_TYPE_VOICE_BOX;
//            this.talkNote.setTalkType(this.talkType);
            this.service.saveOrUpdateTalkNote(this.talkNote);
            //呼叫轨迹：转入语音信箱
            if(this.user.isTraceFlag()) {
                this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), this.currentBindPhone == null ? null : this.currentBindPhone.getOrigBindPhoneNo(), this.districtDesc, "语音信箱", "转入语音信箱", null));
            }
            String voiceRing = null;
            if(currentCrbtRing != null && currentCrbtRing.getVoiceRingLsh()!=null) {
                this.debug("用户炫铃配置了留言流水号：{}", currentCrbtRing.getVoiceRingLsh());
                voiceRing = this.service.getRingFile(this.user.getNumber(), currentCrbtRing.getVoiceRingLsh());
                this.debug("用户炫铃配置了留言彩铃：{}}", voiceRing);
            }
            if(voiceRing == null) {
                voiceRing = currentDept == null ? null : this.service.getVoiceBoxAlertRing(currentDept);
            }
            if (voiceRing == null && MidwareConstants.DEFAULT_RING_TRANSFER_DEPT_CODE.equals(this.currentDept.getVoiceRingLsh())) {
                voiceRing = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_VOICEBOX);
            } 
            this.debug("部门的留言彩铃{}", voiceRing);
            if (voiceRing == null || !this.service.isRingExist(voiceRing)) {
                voiceRing = this.service.getVoiceBoxAlertRing(this.user.getNumber());
                this.debug("未找到部门的留言彩铃！使用号码的留言彩铃{}", voiceRing);
            }
            if ((voiceRing == null || !this.service.isRingExist(voiceRing))) {
                voiceRing = this.service.getDefaultRing(DEFAULT_RING_VOICEBOX);
                this.debug("未找到用户的留言彩铃！使用默认彩铃{}", voiceRing);
            }
            this.mainCall.play(voiceRing, false, false, false, 60, 0);
            if (this.mainCall.getState() == Constants.CALL_CONTENT) {
                //				this.voiceBox = this.service.startVoicebox(this.talkNote.getCallerNo(),
                //						this.user.getNumber(), this.currentDept);
                //				this.voiceBox.setTalkNoteId(this.talkNote.getId());
                //				this.service.saveOrUpdateVoiceBox(this.voiceBox);
                //
                //				this.talkNote.setOutgoingTime(this.voiceBox.getStartTime());
                //				this.talkType = TALK_TYPE_VOICE_BOX;
                this.debug("用户的留言路径：{}", this.voiceBox.getFilePath());
                String recordPath = this.voiceBox.getFilePath();
                if(this.service.isSipFlag()) {
                    //外部路径替换成xcc内部路径
                    recordPath = recordPath.replace(service.getSipVoiceBoxPath(), SIP_LOCAL_PATH);
                }
                this.mainCall.recordSync(recordPath, this.service.getVoiceBoxMaxTime(), false);
                if (this.mainCall.getState() == Constants.CALL_CONTENT) {
                    this.mainCall.onHook();
                }
            }
        }
    }

    private void addToQueue(String callId, int size) {
        //呼叫轨迹：进入排队
        if(this.user.isTraceFlag()) {
            this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), null, this.districtDesc, this.currentDept.getName(), "遇忙，等待队列人数：" + (size + 1), this.currentDept.getDeptLsh()));
        }
        this.service.addToQueue(this.currentDept, callId);
        this.inQueue = true;
        this.leaveFromQueue = true;
        this.palyQueueBg(false);
    }
    
    private void reEnterQueue(String callId) {
        this.service.reEnterQueue(this.currentDept, callId);
        this.inQueue = true;
        this.leaveFromQueue = true;
        this.palyQueueBg(false);
    }
    
    private int processQueue() {
        this.debug("开始处理队列");
        if (!this.queueUseable) {
            this.warn("当前部门：{}队列功能不可用！", this.currentDept.getName());
            return QUEUE_STATE_OFF;
        }
        //开始排队时间
        long startTime = System.currentTimeMillis();
        //排队总分钟数
        int totalMinutes = Integer.MAX_VALUE;
        if (functionConfig != null && functionConfig.getQueuePlayTime() != null) {
            totalMinutes = functionConfig.getQueuePlayTime();
            this.debug("此号码配置了最长等待时间：{}分钟", totalMinutes);
        } else {
            this.debug("此号码未配置最长等待时间");
        }
        //判断是否有人在此部门上排队
        int size = this.service.getQueueSize(this.currentDept);
        this.debug("当前在此部门上的排队人数：{}", size);
        if(size > 0) {
            this.debug("当前队列明细：{}", this.service.getQueueInfo(currentDept));
        }
        String callId = this.fixedCallerNo + "_" + this.getName();
        boolean reEnter = this.service.queueExist(currentDept, callId);
        if (!reEnter && size >= this.currentDept.getMaxQueueCount()) {
            return QUEUE_STATE_FULL;
        } else if (size == 0) {
            this.debug("当前无人在排队中！");
            this.agents = this.service.getIdleAgentsByDept(this.currentDept, this.fixedCallerNo);
            if (this.agents.isEmpty()) {
                this.debug("无空闲坐席，添加队列！");
                this.addToQueue(callId, size);
            } else {
                return QUEUE_STATE_INCOME;
            }
        } else if(reEnter) {
            //重进入
            this.debug("重新进入队列，修改状态为等待中！");
            this.reEnterQueue(callId);
        } else {
            //添加队列
            this.debug("前面有人在排队，添加入队列！");
            this.addToQueue(callId, size);
        }
        int state = QUEUE_STATE_QUEUING;
        while (this.inQueue) {
            this.await(1000);
            int[] ret = this.service.getQueueStatus(this.currentDept, callId);
            switch (ret[0]) {
                case QUEUE_STATE_LEAVEQUEUE:
                    //已经不在队列中
                    throw new MidwareException("主叫不在队列中！");
                case QUEUE_STATE_INCOME:
                    state = QUEUE_STATE_INCOME;
                    this.inQueue = false;
                    break;
                case QUEUE_STATE_TIMOUT:
                    if (this.currentDept.isQueueWaitFlag() && this.mainCall.getState() == Constants.CALL_CONTENT && (System.currentTimeMillis() - startTime) / 1000 / 60 < totalMinutes) {
                        int queueState = this.queueWait(ret[1]);
                        if (queueState == QUEUE_STATE_QUEUING) {
                            this.debug("排队超时，当前队列明细：{}", this.service.getQueueInfo(currentDept));
                            //呼叫轨迹：继续等待
                            if(this.user.isTraceFlag()) {
                                this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), null, this.districtDesc, this.currentDept.getName(), "继续等待，等待队列人数：" + (ret[1] + 1), this.currentDept.getDeptLsh()));
                            }
                            int s = this.service.continueWait(this.currentDept, callId);
                            if(s == QUEUE_STATE_INCOME) {
                                state = QUEUE_STATE_INCOME;
                                this.inQueue = false;
                            } else {
                                this.palyQueueBg(true);
                            }
                        } else if(queueState == QUEUE_STATE_OFFLINE) {
                            this.offline(service.getQueueIndex(currentDept, callId));
                            this.inQueue = false;
                            state = queueState;
                        } else {
                            this.inQueue = false;
                            state = queueState;
                        }
                    } else {
                        this.inQueue = false;
                        state = QUEUE_STATE_TIMOUT;
                    }
                    break;
                default:
                    break;
            }
        }
        if(state != QUEUE_STATE_INCOME) {
            //非接入状态直接退出队列，可接入状态在实际呼叫坐席后再退出队列
            this.service.removeQueue(this.currentDept, callId, false);
        }
        return state;
    }

    /**
     * Checks if is queue wait.
     *
     * @param size the size
     * @return the int
     */
    private int queueWait(int size) {
        this.mainCall.stopPlay();
        if("on".equals(service.getFunctionData(this.user.getNumber(), FunctionSwitchConstants.QUEUE_TIMEOUT_NO_INPUT))) {
            this.debug("用户配置了排队超时不收码！");
            if (queueNotPlayNum()) {
                this.playQueueTip();//不报数
            } else {
                this.playQueueNum2(size);//报数
            }
            return QUEUE_STATE_QUEUING;
        } else {
            this.debug("用户开启了排队超时收码功能！");
            for (int i = 0; i < this.service.getTimeOutInputCount(); i++) {
                if (queueNotPlayNum()) {
                    this.playQueueTip();//不报数
                } else {
                    this.playQueueNum2(size);//报数
                }
                int timeout = 10;
                String fixedTimeout = service.getFunctionData(this.user.getNumber(), FunctionSwitchConstants.QUEUE_TIMEOUT_INPUT_TIME);
                if(StringUtils.isNumeric(fixedTimeout)) {
                    timeout = Integer.parseInt(fixedTimeout);
                }
                String code = this.mainCall.receiveDTMFSync(1, null, timeout);
                this.debug("坐席等待提示音收到码为：{}", code);
                if(this.mainCall.getState() == Constants.CALL_IDLE) {
                    throw new MidwareException("主叫已挂机！");
                }
                if (StringUtils.isEmpty(code) || "1".equals(code)) {
                    return QUEUE_STATE_QUEUING;
                } else if (this.user.isVoiceBoxFlag() && "2".equals(code)) {
                    return QUEUE_STATE_TIMOUT;
                } else if (isOfflineUseable(size + 1) && "3".equals(code)) {
                    return QUEUE_STATE_OFFLINE;
                } else if (i < this.service.getTimeOutInputCount() - 1) {
                    this.playTimeOutInputErrorRing(false);
                }
            }
            return QUEUE_STATE_OVER;
        }
    }
    
    private boolean queueNotPlayNum() {
        //部门自定义彩铃>用户开关排队等待播放人数开关>默认彩铃1、默认彩铃2,(一种是【当前排队人数较多】，一种是【当前排队人数*】)>【当前排队人数*】
        if(this.currentDept.getQueueWaitRing() != null) {
            return true;
        }
        if(this.functionConfig != null && this.functionConfig.isNotPlayWaitNum()) {
            return true;
        }
        return "100000000".equals(this.currentDept.getQueueWaitRingLsh());
    }

    private boolean isOfflineUseable(int size) {
        return this.user.getQueueOfflineCountIntValue() > 0 && size >= this.user.getQueueOfflineCountIntValue() && MyStringUtil.isMobile(this.mainCall.getCaller());
    }
    
    private int processMakeCall() {
        this.debug("坐席的接听策略为：{},对坐席重排序！排序前顺序：{}", this.currentDept.getCallModleDescription(), this.agents);
        try {
            this.service.sortAgents(this.currentDept, this.agents);
            this.info("重新排序后的坐席顺序：{}", this.agents);
            this.agents = this.service.judgeMemoryFlag(this.user, this.currentDept, this.agents, this.talkNote.getCallerNo());
            this.info("记忆功能判断后再次重新排序后的坐席顺序：{}", this.agents);
        } catch (Exception e) {
            this.warn("对坐席重新排序失败!" + e.getMessage());
        }
        boolean actualCallFlag = false;
        //呼叫前将当前坐席设置为null，防止转接流程时当前坐席还有值
        this.agent = null;
        for (BindPhone phone : this.agents) {
            if (this.service.getAgentState(phone).equals(AGENT_STATE_IDLE)) {
                this.currentBindPhone = this.service.getLocalBindPhone(phone.getCustomerNo(), phone.getLsh());
                this.debug("根据400号码：{}和绑定号码流水号：{}，获得本地绑定号码：{}", phone.getCustomerNo(), phone.getLsh(),
                        this.currentBindPhone);
                if(this.currentBindPhone == null) {
                    this.warn("未找到本地绑定号码，取下一个空闲坐席！");
                    continue;
                }
                if (!this.service.allowCalleeMobile()
                        && this.service.isMobile(this.currentBindPhone.getOrigBindPhoneNo())) {
                    this.warn("当前绑定号码:{}是手机，平台不允许呼叫手机！", this.currentBindPhone.getOrigBindPhoneNo());
                    continue;
                }
                actualCallFlag = true;
                if(this.leaveFromQueue) {
                    this.debug("开始呼叫坐席，主叫：{}移出队列！", this.fixedCallerNo);
                    //退出队列
                    this.service.removeQueue(this.currentDept, this.fixedCallerNo + "_" + this.getName(), false);
                }
                Call callee = this.makeCall();
                if (callee != null) {
                    //呼叫轨迹：坐席接听
                    if(this.user.isTraceFlag()) {
                        this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), this.currentBindPhone.getOrigBindPhoneNo(), this.districtDesc, this.currentDept.getName(), "坐席接听", this.currentDept.getDeptLsh()));
                    }
                    this.agent = callee;
                    break;
                } else {
                    //呼叫轨迹：坐席无应答
                    if(this.user.isTraceFlag() && this.mainCall.getState() != Constants.CALL_IDLE) {
                        this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), this.currentBindPhone.getOrigBindPhoneNo(), this.districtDesc, this.currentDept.getName(), "坐席无应答", this.currentDept.getDeptLsh()));
                    }
                    this.missedBindPhone = phone;
                    if("on".equals(service.getFunctionData(this.user.getNumber(), FunctionSwitchConstants.FUNCTION_AGENT_CALL_ONCE))) {
                        this.info("用户配置了只呼叫一次坐席，终止呼叫下个坐席");
                        break;
                    }
                }
            }
        }
        if(!actualCallFlag && this.queueUseable) {
            return MidwareConstants.CALL_RESULT_TO_QUEUE;
        }
        if (this.agent != null) {
            initCallParams(this.agent, ivrTrans == false ? MidwareConstants.CALL_TYPE_CALLEE : MidwareConstants.CALL_TYPE_TRANS);
            this.leaveFromQueue = false;
            this.calleeOffhook();
            if(this.mainCall.getState() != Constants.CALL_CONTENT || this.agent.getState() != Constants.CALL_CONTENT) {
                throw new MidwareException("主被叫有一方已挂机！");
            }
            //改坐席状态，发送消息
            if (!this.currentBindPhone.isPbx()) {
                this.service.callConnectedAsync(this.currentBindPhone, this.talkNote, this.currentDept, user.getPushStatusFlag(), false);
            }
            if (!this.currentBindPhone.isPbx() || this.currentDept.getCallModle() == 4) {
                //非中继号码或开了权重接听的部门，增加接通记录数
                this.service.addAgentCallNumAsync(this.currentBindPhone);
            }
            
            //播放坐席提示音
            this.playAgentAlertRing(this.agent);
            this.playRecordTip(this.mainCall, this.agent);
            this.playNum(this.mainCall, this.agent);
            boolean connected = this.connectCall(this.mainCall, this.agent);
            if (!connected) {
//                String currentState = this.service.getAgentState(this.currentBindPhone);
//                this.warn("连接主被叫失败！当前电话状态：{}， 当前绑定号码状态：{}", this.agent.getState(), currentState);
//                if (this.agent.getState() != Constants.CALL_IDLE || currentState.startsWith(AGENT_STATE_CALLING)) {
//                    this.service.midwareUpdateAgentState(this.currentBindPhone, AGENT_STATE_IDLE, true);
//                }
//                if (this.agent.getState() == Constants.CALL_IDLE && currentState.equals(AGENT_STATE_CONNECT)) {
//                    this.service.midwareUpdateAgentState(this.currentBindPhone, AGENT_STATE_IDLE, true);
//                }
                throw new MidwareException("连接主被叫失败！");
            }
            this.await(200);
            if (this.mainCall.getState() == Constants.CALL_IDLE || this.agent.getState() == Constants.CALL_IDLE) {
                throw new MidwareException("主被叫有一方挂机！终止流程！");
            }
            this.isCallConnected = true;
            if(this.ivrTrans) {
                this.reStartRecord(this.mainCall, this.agent);
            } else {
                this.processRecord();
            }
            //接收被叫输码，按键转移
            this.calleeReceiveDTMF();
            //开通通话超时的用户，添加定时任务
            Integer callTimeout = this.functionConfig == null ? null : this.functionConfig.getConnetTime();
            if (callTimeout != null) {
                this.debug("用户配置了通话超时时长：{}秒,启动自动挂断定时任务！", callTimeout);
                this.service.addCallTimeoutTask(callTimeout, this);
            }
            return MidwareConstants.CALL_RESULT_SUCCESS;
        } else {
            this.leaveMessage();
            return MidwareConstants.CALL_RESULT_FAIL;
        }
    }

    private boolean connectCall(Call caller, Call callee) {
        if (this.mainCall.isIsPlaying()) {
            this.mainCall.stopPlay();
            this.await(200);
        }
        if (caller.getState() == Constants.CALL_IDLE || callee.getState() == Constants.CALL_IDLE) {
            this.warn("主被叫有一方挂机！退出连接！");
            return false;
        }
        int ret = caller.connectCall(callee, !dualChannel);
        if (ret != Constants.RET_SUCCESS) {
            this.warn("连接主被叫失败！主叫状态：{}，被叫状态：{}，等待一秒后重新尝试一次", this.getCallStateDesc(caller.getState()),
                    this.getCallStateDesc(callee.getState()));
            this.await(1000);
            ret = caller.connectCall(callee, !dualChannel);
            if (ret != Constants.RET_SUCCESS) {
                if (caller.getState() == Constants.CALL_ALERT && callee.getState() == Constants.CALL_CONTENT) {
                    this.await(2000);
                    ret = caller.connectCall(callee, !dualChannel);
                }
                if (ret != Constants.RET_SUCCESS) {
                    this.error("重新连接主被叫失败！主叫状态：{}，被叫状态：{}", this.getCallStateDesc(caller.getState()),
                            this.getCallStateDesc(callee.getState()));
                }
            }
        }
        return ret == Constants.RET_SUCCESS;
    }

    private void playRecordTip(Call caller, Call callee) {
        if (caller.getState() == Constants.CALL_IDLE || callee.getState() == Constants.CALL_IDLE) {
            this.warn("主被叫有一方挂机！退出录音提示音播放！");
            return;
        }
        if (this.user.isRecordRingFlag()) {
            String ring = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_RECORD_TIP);
            this.info("播放录音提示彩铃：{}", ring);
            callee.play(ring, false, false, true, 0, 0);
            caller.play(ring, false, false, false, 0, 0);
        } else {
            this.info("用户未开通录音提示音功能！");
        }
    }
    
    private void playTransTipRing(Call call) {
        if (call.getState() != Constants.CALL_CONTENT) {
            this.warn("被叫挂机！退出转接提示音！");
            return;
        }
        String ring = this.service.getDefaultRing("transfer_tip.wav");
        this.debug("转接提示音地址：{}", ring);
        call.play(ring, false, false, false, 5, 0);
        if (call.isIsPlaying()) {
            call.stopPlay();
        }
    }

    private void playAgentAlertRing(Call call) {
        if (call.getState() != Constants.CALL_CONTENT) {
            this.warn("被叫挂机！退出坐席提示音！");
            return;
        }
        if (this.currentDept.getAgentAlertRing() != null) {
            String ring = this.service.getAgentAlertRing(this.currentDept);
            this.debug("坐席提示音地址：{}", ring);
            int time = this.service.getAgentAlertRingTime();
            call.play(ring, false, false, false, time, 0);
            if (call.isIsPlaying()) {
                call.stopPlay();
            }
        } else if (MidwareConstants.DEFAULT_RING_AGENT_TIP_CODE.equals(this.currentDept.getSalesAlertToneLsh())) {
            this.info("部门：{}进入默认坐席提示音", this.currentDept);
            String ring = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_AGENT_TIP);
            this.debug("坐席提示音地址：{}", ring);
            int time = this.service.getAgentAlertRingTime();
            call.play(ring, false, false, false, time, 0);
            if (call.isIsPlaying()) {
                call.stopPlay();
            }
        } else {
            this.info("部门：{}未配置坐席提示音", this.currentDept);
        }
    }

    private void palyQueueBg(boolean queue) {
        if (!queue && this.mainCall.isIsPlaying()) {
            this.mainCall.stopPlay();
            this.await(200);
        }
        String queueRing = this.service.getQueueBg();
        this.debug("开始播放排队等待音：{}", queueRing);
        this.mainCall.play(queueRing, true, queue, true, 0, 0);
    }

    private void playQueueNum(int size) {
        if (this.mainCall.isIsPlaying()) {
            this.mainCall.stopPlay();
            this.await(500);
        }
        String headFile = this.service.getQueueHeadFile();
        String endFile = this.service.getQueueEndFile(this.user.isVoiceBoxFlag());
        if(this.user.isQueueCountFlag()) {
            this.debug("排队人数计数从1开始");
            size ++;
        }
        String files = headFile + "," + this.service.getNumberWav(size) + "," + endFile;
        this.debug("播放等待提示音：{}", files);
        this.mainCall.play(files, false, true, true, 0, 0);
    }

    private void playQueueNum2(int size) {
        if (this.mainCall.isIsPlaying()) {
            this.mainCall.stopPlay();
            this.await(500);
        }
        String queue_1 = this.service.getQueueFile1();
        String queue_3 = this.service.getQueueFile3();
        String queue_4 = null;
        if ("4009992121".equals(this.user.getNumber())) {
            queue_4 = this.service.getCustomQueueFile4("4009992121");
        } else {
            queue_4 = this.service.getQueueFile4(this.user.isVoiceBoxFlag(), isOfflineUseable(size + 1));
        }
        if(this.user.isQueueCountFlag()) {
            this.debug("排队人数计数从1开始");
            size ++;
        }
        String files = queue_1 + "," + this.service.getQueueNumberWav(size) + "," + queue_3 + "," + queue_4;
        this.debug("播放等待提示音：{}", files);
        this.mainCall.play(files, false, true, true, 0, 0);
    }

    private void playQueueTip() {
        if (this.mainCall.isIsPlaying()) {
            this.mainCall.stopPlay();
            this.await(500);
        }
        ColorRing queueWaitRing = currentDept.getQueueWaitRing();
        String file = queueWaitRing != null ? this.service.getRingFile(queueWaitRing) : this.service.getQueueTipFile(this.user.isVoiceBoxFlag());
        this.debug("播放等待提示音：{}", file);
        this.mainCall.play(file, false, false, true, 0, 0);
    }

    private void stopMainCallPlay() {
        if (this.mainCall.isIsPlaying()) {
            this.mainCall.stopPlay();
            this.await(400);
        }
    }

    private void playNum(Call caller, Call callee) {
        if (caller.getState() != Constants.CALL_CONTENT || callee.getState() != Constants.CALL_CONTENT) {
            this.warn("主被叫有一方还未摘机，等待500毫秒！");
            this.await(500);
        }
        if (this.user.isGhFlag() && this.currentDept.isGhFlag() && StringUtils.isNumeric(this.currentBindPhone.getReportNum())) {
            this.stopMainCallPlay();
            String headFile = this.service.getReportHeadFile();
            String endFile = this.service.getReportEndFile();
            this.debug("报工号头文件：{},尾文件：{},工号：{}", headFile, endFile, this.currentBindPhone.getReportNum());
            if (StringUtils.isNotEmpty(this.currentBindPhone.getReportNum())
                    && this.getLastAgent().getState() == Constants.CALL_CONTENT
                    && this.mainCall.getState() != Constants.CALL_IDLE) {
                String files = headFile + ","
                        + this.service.getNumberWav(Integer.valueOf(this.currentBindPhone.getReportNum())) + "," + endFile;
                this.await(500);
                this.isPlayingNumber = true;
                caller.play(files, false, true, true, 0, 0);
                //				this.mainCall.playNum(headFile,
                //					this.currentBindPhone.getReportNum(), endFile, false, 30);
                callee.play(files, false, true, false, 20, 0);
                //				this.getLastAgent().playNum(headFile,
                //					this.currentBindPhone.getReportNum(), endFile, true, 0);
                this.isPlayingNumber = false;
            }
        } else {
//            this.await(200);
            this.info("用户：{}未开通报工号功能或工号非数字！", this.user.getNumber());
        }
    }

    /**
     * 被叫摘机
     */
    private void calleeOffhook() {
        if(!this.ivrTrans) {
            Date now = new Date();
            if (!this.user.isPreOffhook() || this.service.isAutoTestCaller(this.mainCall.getCaller())) {
                this.stopMainCallPlay();
                this.mainCall.answerSync(3000);
                //如果在摘机前，主叫已经挂机了，防止摘机时间晚于挂机时间，直接取挂机时间
                Date offHookTime = this.talkNote.getOnhookTime() == null ? new Date() : this.talkNote.getOnhookTime();
                this.talkNote.setCallerOffhookTime(offHookTime);
            }
            this.talkType = TALK_TYPE_IVR_CON;
            this.talkNote.setTalkType(this.talkType);
            this.talkNote.setCalleeOffhookTime(now);
        }
        this.talkNote.setLsh(this.currentBindPhone.getId());
        this.talkNote.setGh(StringUtils.isNumeric(this.currentBindPhone.getReportNum()) ? Long.valueOf(this.currentBindPhone.getReportNum()) : null);
        this.talkNote.setDeptId(this.currentDept.getDeptLsh());
        this.talkNote.setCalleeNo(this.currentBindPhone.getOrigBindPhoneNo());
        this.service.saveOrUpdateTalkNote(this.talkNote);
        this.debug("被叫摘机，被叫号码：{},工号：{},摘机时间：{}", this.fixedCalleeNo, this.talkNote.getGh(),
                this.talkNote.getCalleeOffhookTime());
    }

    private void processRecord() {
        this.stopMainCallPlay();
        if (this.user.isRecordFlag()) {
            this.await(500);
            this.voiceRecord = this.service.startRecord(this.user, this.currentBindPhone, this.talkNote.getCallerNo(),
                    this.fixedCalleeNo, dualChannel);
            this.voiceRecord.setTalkNoteId(this.talkNote.getId());
            this.service.saveOrUpdateRecord(this.voiceRecord);
            String recordPath = this.voiceRecord.getFilePath();
            this.debug("用户的录音路径：{}", recordPath);
            if (dualChannel && !service.isSipFlag()) {
                String[] records = recordPath.split(",");
                this.mainCall.record(records[0], 0, false);
                this.agent.record(records[1], 0, false);
            } else {
                this.mainCall.record(recordPath, 0, false);
            }
        } else {
            this.info("当前用户未开通录音功能！");
        }
    }

    private Call makeCall() {
        return this.makeCall(false);
    }
    
    private Call makeCall(boolean trans) {
        if (this.mainCall.getState() == Constants.CALL_IDLE) {
            if (!trans && this.leaveFromQueue) {
                //主叫从队列中出来，未呼叫坐席就挂机，需要通知队列将下一个排队者改为待接入
                this.debug("主叫从队列中出来，未呼叫坐席就挂机");
                this.service.notifyQueueAgentOnhook(this.currentDept);
            }
            throw new MidwareException("主叫已挂机！");
        }
        try {
            //更新坐席状态
            if (this.currentBindPhone.isPbx()) {
                int count = this.service.getPbxCount(this.currentBindPhone);
                this.debug("绑定号码开通了中继数量：{}，当前的通话数量：{}", this.currentBindPhone.getBindNum(), count);
                this.service.saveOrUpdatePbxInfo(this.currentBindPhone, count);
                this.debug("保存绑定号码当前的通话数量：{}", count);
                if (count < this.currentBindPhone.getBindNum()) {
                    //可以呼入
                    this.service.increasePbxCount(this.currentBindPhone);
                } else {
                    this.warn("用户的中继数量超过最大数量：{}", this.currentBindPhone.getBindNum());
                    return null;
                }
            } else {
                int count;
                if(this.multipleNumber) {
                    count = this.service.midwareBatchUpdateAgentState(this.currentBindPhone,
                        AGENT_STATE_CALLING + "_" + this.service.isMainServer(), this.user.getPushStatusFlag(), user.getUniqueName());
                    this.info("多号批量更新呼叫中状态的绑定号码条数：{}", count);
                } else {
                    count = this.service.midwareUpdateAgentState(this.currentBindPhone,
                        AGENT_STATE_CALLING + "_" + this.service.isMainServer(), this.user.getPushStatusFlag());
                    this.info("单号更新呼叫中状态的绑定号码条数：{}", count);
                }
                if(count == 0) {
                    this.warn("更新绑定号码状态失败，直接返回！");
                    return null;
                }
            }
            if (!trans && this.currentDept.getCallModle() == 3) {
                //更新部门呼叫的最后绑定号码
                this.service.updateDeptLastBindPhone(this.user.getNumber(), this.currentDept.getDeptLsh(), this.currentBindPhone.getLsh());
            }
            CacheUtil.put(CacheUtil.CACHE_CALL_PROCESS_NAME, this.currentBindPhone.getCustomerNo() + "_"
                    + this.currentBindPhone.getId(), this);
            this.fixedCalleeNo = this.service.getFixedCalleeNo(this.currentBindPhone.getOrigBindPhoneNo());
            this.debug("处理过后的被叫号码：{}", this.fixedCalleeNo);
            if ((this.user.isScreenFlag() || interactFlag) && !this.currentBindPhone.isPbx()) {
                this.debug("发送弹屏请求");
                String decryptStr = this.talkNote.getCallerNo();
                try {
                    decryptStr = CtiDes.getInstance().decrypt(this.talkNote.getCallerNo());
                } catch (Exception e) {
                    this.error("{}:解密失败", this.talkNote.getCallerNo(), e);
                }
                String ivrKeyName = this.service.getDeptIvrKeyName(this.currentDept);
                this.service.screenRequest(this.talkNote.getId(), this.currentBindPhone, decryptStr,
                        this.fixedCalleeNo, this.districtDesc, ivrKeyName,
                        this.currentBindPhone.getOrigBindPhoneNo(), talkNote.getIncomingTime().getTime());
            }
            LocalDateTime now = LocalDateTime.now();
            int hour = now.getHour();
            if(this.user.isSxSmsFlag() && MyStringUtil.isMobile(this.currentBindPhone.getOrigBindPhoneNo()) && (hour >= 8 && hour< 22)) {
                SmsSendDto dto = sendSmsMessage(6);
                this.debug("发送闪信短信：{}", JSON.toJSON(dto));
            }

            this.debug("开始处理互联互通。。。");
            //处理互联互通问题
            CallNumDto callNumDto = processInterConnection();
            //处理sip中继
            if(this.fixedCalleeNo.startsWith(MidwareConstants.SIP_PHONE_PREFIX)) {
                callNumDto.setCallerNo(this.mainCall.getCaller());
            }
            long begin = System.currentTimeMillis();
            this.debug("开始呼叫坐席：主叫号码：{},被叫号码：{},开始时间：{},超时时间：{}", callNumDto.getCallerNo(), callNumDto.getCalleeNo(), begin,
                    this.currentBindPhone.getWaitTime());
            //呼叫轨迹：呼叫坐席
            if(this.user.isTraceFlag()) {
                this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), this.currentBindPhone.getOrigBindPhoneNo(), this.districtDesc, this.currentDept.getName(), "呼叫坐席", this.currentDept.getDeptLsh()));
            }
            Call callee = this.asyncMakeCall(callNumDto, this.currentBindPhone.getWaitTime());
            long end = System.currentTimeMillis();
            long callTime = (end - begin) / 1000;
            this.debug("呼叫结束，呼叫时间：{}，呼叫结果：{}", callTime, this.isCallSuccess);
            if (callee == null && callTime <= this.service.getMakeCallRetryTime() && this.mainCall.getState() != Constants.CALL_IDLE) {
                this.warn("呼叫失败时间:{}秒,小于重拨时间，重新呼叫一次！", callTime);
                begin = System.currentTimeMillis();
                callee = this.asyncMakeCall(callNumDto, this.currentBindPhone.getWaitTime());
                end = System.currentTimeMillis();
                callTime = (end - begin) / 1000;
            }
            if (callee == null && this.callErrorCode == 129) {
                this.warn("发生同抢，再次重新呼叫一次！", callTime);
                begin = System.currentTimeMillis();
                callee = this.asyncMakeCall(callNumDto, this.currentBindPhone.getWaitTime());
                end = System.currentTimeMillis();
                callTime = (end - begin) / 1000;
            }
            if (callee != null && callTime < 1 && !callNumDto.getCalleeNo().startsWith(MidwareConstants.SIP_PHONE_PREFIX)) {
                //呼叫成功小于1秒，则认为超时终止失败，接通的是上一个坐席
                this.warn("发生并发接通问题，接通被叫：{}，外呼被叫：{}，忽略本次接通！", callee.getCallee(), callNumDto.getCalleeNo());
                callee.onHook();
                callee = null;
            }
            if (callee == null) {
                CacheUtil.remove(CacheUtil.CACHE_CALL_PROCESS_NAME, this.currentBindPhone.getCustomerNo() + "_"
                        + this.currentBindPhone.getId());
                //发送漏接消息
                if (this.functionConfig != null && this.functionConfig.isInteractFlag()) {
                    this.debug("接口客户发送漏接消息！");
                    this.service.sendMissMessage(user, this.currentBindPhone, this.talkNote);
                }
                if (this.currentBindPhone.isPbx()) {
                    this.service.decreasePbxCount(this.currentBindPhone);
                } else {
                    //如果状态任然是呼叫中，则改回空闲
                    if (this.service.getAgentStateByAgentId(this.currentBindPhone.getAgentId()).equals(
                            AGENT_STATE_CALLING + "_" + this.service.isMainServer())) {
                        if(this.multipleNumber) {
                            this.service.midwareBatchUpdateAgentStateByBindPhone(this.currentBindPhone,
                                AGENT_STATE_IDLE, this.user.getPushStatusFlag(), user.getUniqueName());
                        } else {
                            this.service.midwareUpdateAgentStateByAgentId(this.currentBindPhone, AGENT_STATE_IDLE,
                                this.user.getPushStatusFlag());
                        }
                    } else {
                        log.warn("绑定号码发生并发呼叫，忽略还原到空闲状态！");
                    }
                }
            }
            return callee;
        } catch (Exception e) {
            this.error(e.getMessage(), e);
            if (this.service.getAgentStateByAgentId(this.currentBindPhone.getAgentId()).equals(
                    AGENT_STATE_CALLING + "_" + this.service.isMainServer())) {
                if(this.multipleNumber) {
                    this.service.midwareBatchUpdateAgentState(this.currentBindPhone,
                        AGENT_STATE_IDLE, this.user.getPushStatusFlag(), user.getUniqueName());
                } else {
                    this.service.midwareUpdateAgentState(this.currentBindPhone, AGENT_STATE_IDLE,
                        this.user.getPushStatusFlag());
                }
            } else {
                log.warn("绑定号码发生并发呼叫，忽略还原到空闲状态！");
            }
            return null;
        }
    }

    private CallNumDto processInterConnection() {
        CallNumDto callNumDto = this.service.getCallNumDto(this.user.getNumber(), this.fixedCallerNo,
                this.currentBindPhone.getOrigBindPhoneNo());
        if (callNumDto != null) {
            this.service.setCallDtoDefaultValue(callNumDto);
            //如果互联互通里没有设置被叫，使用默认被叫
            if (StringUtils.isEmpty(callNumDto.getCalleeNo())) {
                callNumDto.setCalleeNo(this.fixedCalleeNo);
            }
            this.info("查找到对应的互联互通配置:{}", callNumDto);
            if (!this.currentBindPhone.isPassthrough()) {
                this.info("绑定号码透传功能不可用！使用小号作为主叫号码");
                String origNo = this.user.getOriginalNo();
                callNumDto.setCallerNo(this.service.isOriginalNoPrefix() ? this.service.getlocalNo() + origNo : origNo);
            }
            return callNumDto;
        }
        
        if(MyStringUtil.isMobile(this.currentBindPhone.getOrigBindPhoneNo()) && "on".equals(service.getFunctionData(BusinessConstants.GLOBAL_IONN, FunctionSwitchConstants.CALLEE_GLOBAL_RULE))) {
            String location = service.getLocationByBindPhone(this.currentBindPhone.getOrigBindPhoneNo());
            if(StringUtils.startsWith(location, "广东")
                && (location.contains("联通") || location.contains("移动"))) {
                callNumDto = this.service.getDefaultCallNumDto(this.user.getOriginalNo(), this.getDefaultRuleCaller(),
                    service.getDistrictNo(this.currentBindPhone.getOrigBindPhoneNo()) + this.currentBindPhone.getOrigBindPhoneNo());
                this.info("广东地区，移动、联通被叫默认互联互通配置加被区号, 原始被叫：{}，处理后被叫：{}，地区：{}",this.currentBindPhone.getOrigBindPhoneNo(), callNumDto.getCalleeNo(), location);
                return callNumDto;
            }
        }
        
        this.info("未找到互联互通配置，使用默认规则！");
        callNumDto = this.service.getDefaultCallNumDto(this.user.getOriginalNo(), this.getDefaultRuleCaller(),
                this.fixedCalleeNo);
        return callNumDto;
    }

    private String getDefaultRuleCaller() {
        String localAddress = this.service.getlocalNo();
        String remoteAddress = null;//外地手机的区号
        boolean passThrough = this.currentBindPhone.isPassthrough();
        String originalNo = this.user.getOriginalNo();
        //判断是否是国外号码
        if (this.mainCall.getCaller().startsWith("00")) {
            String caller = this.service.isOriginalNoPrefix() ? localAddress + originalNo : originalNo;
            this.info("国外主叫号码直接改成小号{}", caller);
            return caller;
        }
        String caller = passThrough ? this.fixedCallerNo : this.service.isOriginalNoPrefix() ? localAddress
                + originalNo : originalNo;

        if (caller.length() > 12) {
            remoteAddress = caller.substring(0, caller.length() - 11);
        }
        if (passThrough && caller.startsWith(localAddress) && !this.service.isKeepLocalDistrict(caller)) {
            //透传号码的主叫去掉本地区号
            caller = caller.substring(localAddress.length());
        }
        //主叫号码如果是外地手机，改成“0” + 手机号码，默认fixedCallerNo是区号 + 手机号码
        if (remoteAddress != null && !remoteAddress.equals(localAddress)) {
            caller = caller.substring(remoteAddress.length());
            if (!this.service.isAddCallerNationCode()) {
                caller = "0" + caller;
            }
        }
        if (this.service.isAddCallerNationCode()) {
            caller = "0086" + caller;
        }
        return caller;
    }

    private Call asyncMakeCall(CallNumDto dto, long timeoutSeconds) {
        if (this.mainCall.getState() == Constants.CALL_IDLE) {
            this.error("主叫已挂机！");
            return null;
        }
        String originalNo = null;
        if(dto.getCalleeNo().startsWith(MidwareConstants.SIP_PHONE_PREFIX)) {
            //中继号码
            originalNo = this.service.getlocalNo() + dto.getOriginalNo();
        } else {
            //处理号码功能配置
            if (this.functionConfig != null && (StringUtils.contains(this.functionConfig.getAccessShortNum(), this.mainCall.getCaller()) || this.functionConfig.containsShortNum(this.mainCall.getCaller()))) {
                this.debug("主叫号码：{} 采用号码配置功能配置的短号码外显：{}", this.mainCall.getCaller(), this.functionConfig.getShortShowNum());
                dto.setCallerNo(this.functionConfig.getShortShowNum());
            } else {
                if (this.currentBindPhone.isPassthrough()) {
                    originalNo = dto.getCallType() == CallNumDto.CALL_TYPE_TRANS ? dto.getOriginalNo() : null;
                }
                if (originalNo != null && dto.getTransPrefix() == CallNumDto.TRANS_PREFIX_ON) {
                    originalNo = this.service.getlocalNo() + originalNo;
                }
                //判断有没有配置特殊的小号
                String specialOrigNo = this.service.getSpecialOrigNo(this.user.getNumber());
                if (originalNo != null && specialOrigNo != null) {
                    this.debug("使用配置的特殊小号：{}", specialOrigNo);
                    originalNo = specialOrigNo;
                }

                //判断是否用400号码作为原始被叫及改发号码
                if (originalNo != null && this.service.isOrigUseNetphone()) {
                    this.info("原始小号使用400号码");
                    originalNo = this.user.getNumber();
                }
            }
        }
        Call call = null;
        boolean terminate = false;
        try {
            synchronized (this.makeCallSync) {
                this.isMakingCall = true;
                this.isCallOver = false;
                this.isCallSuccess = false;
                this.isRejectCall = false;
                if (this.mainCall.getState() == Constants.CALL_IDLE) {
                    this.error("主叫已挂机！");
                    return null;
                }
                call = this.manager.makeCall(dto.getCallerNo(), dto.getCalleeNo(), originalNo, true, timeoutSeconds,
                        this.getName());
                if (call == null) {
                    return null;
                }
                call.setParam(MAIN_CALL_THREAD, this);
                long begin = System.currentTimeMillis();
                while (!this.isCallOver) {
                    this.makeCallSync.wait(timeoutSeconds * 1000);
                    this.info("主线程被唤醒...isCallOver={}, isCallSuccess={}", this.isCallOver, this.isCallSuccess);
                    terminate = this.isRejectCall || (!this.isCallOver && System.currentTimeMillis() - begin >= timeoutSeconds * 1000);
                    if (terminate) {
                        this.warn((this.isRejectCall ? "用户拒接" : "超时") + "，终止呼叫！");
                        this.manager.stopMakeCall(call.getDeviceId(), call.getLsh());
                        this.isCallSuccess = false;
                        this.isCallOver = true;
                    }
                }
                this.isMakingCall = false;
            }
            if(terminate) {
                this.await(500);
            }
            return this.isCallSuccess ? call : null;
        } catch (Exception ex) {
            this.errorStackTrace(ex.getMessage(), ex);
            return null;
        }
    }

    @Override
    public void callOver(int status, int errorCode) {
        this.debug("呼叫结束！呼叫结果：{}，失败原因：{}", status, errorCode);
        synchronized (this.makeCallSync) {
            if (this.isMakingCall) {
                this.isCallOver = true;
                this.isCallSuccess = (status == 1);
                this.callErrorCode = errorCode;
                this.makeCallSync.notifyAll();
                this.debug("唤醒等待线程！");
            }
        }
    }

    public Call getLastAgent() {
        if (this.transList.isEmpty()) {
            return this.agent;
        } else {
            return this.transList.get(this.transList.size() - 1);
        }
    }

    /**
     * 挂断
     */
    public void handupAgent() {
        if (this.isMakingCall) {
            this.debug("终止呼叫坐席！");
            synchronized (this.makeCallSync) {
                this.isCallOver = true;
                this.isCallSuccess = false;
                this.isRejectCall = true;
                this.makeCallSync.notifyAll();
            }
        } else {
            Call callee = this.getLastAgent();
            if (callee != null && callee.getState() == Constants.CALL_CONTENT) {
                this.debug("挂断坐席！");
                callee.onHook();
            }
        }
    }

    /**
     * 保持
     */
    public void callholdAgent() {
        Call oldAgent = this.getLastAgent();
        this.debug("保持请求，当前保持状态：{},主叫状态：{},被叫状态：{}", this.inHold, this.mainCall.getState(), oldAgent.getState());
        if (!this.inHold && this.mainCall.getState() == Constants.CALL_CONTENT
                && oldAgent.getState() == Constants.CALL_CONTENT) {
            this.debug("断开主叫和被叫");
            this.await(300);
            int delay = this.service.getSipConnectDelay();
            if (delay > 0) {
                this.mainCall.stopPlay();
                this.getLastAgent().stopPlay();
                this.await(delay);
            }
            this.mainCall.disconnectCall(oldAgent);
            this.await(200);
            String ring = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_HOLD);
            this.debug("等待背景彩铃：{}", ring);
            this.mainCall.play(ring, true, false, true, 0, 0);
            oldAgent.play(ring, true, false, true, 0, 0);
            this.inHold = true;
        }
    }

    /**
     * 接回
     */
    public void callBackAgent() {
        Call oldAgent = this.getLastAgent();
        this.debug("接回请求，当前保持状态：{},主叫状态：{},被叫状态：{}", this.inHold, this.mainCall.getState(), oldAgent.getState());
        if (this.inHold && this.mainCall.getState() == Constants.CALL_CONTENT
                && oldAgent.getState() == Constants.CALL_CONTENT) {
            this.debug("接回主叫和被叫");
            this.mainCall.stopPlay();
            oldAgent.stopPlay();
            this.await(200);
            this.mainCall.connectCall(oldAgent, !dualChannel);
            this.await(500);
            this.inHold = false;
            int delay = this.service.getSipConnectDelay();
            if (delay > 0 && this.voiceRecord != null) {
                String recordPath = this.voiceRecord.getFilePath();
                this.debug("用户的录音路径：{}", recordPath);
                if (dualChannel && !service.isSipFlag()) {
                    String[] records = recordPath.split(",");
                    this.mainCall.record(records[0], 0, true);
                    oldAgent.record(records[1], 0, true);
                } else {
                    recordPath = recordPath.substring(0, recordPath.lastIndexOf(".")) + ".wav";
                    this.mainCall.record(recordPath, 0, true);
                }
            }
        }
    }

    /**
     * 转移
     *
     * @param bindPhoneLsh
     * @param playMainCallWaitRing
     * @param stopRecord
     */
    public void transCall(String bindPhoneLsh, boolean playMainCallWaitRing, boolean stopRecord) {
        if(!this.isCallConnected) {
            this.warn("上个坐席尚未接通，不能转移！");
            return;
        }
        Call oldAgent = this.getLastAgent();
        oldAgent.setParam(MidwareConstants.CALL_HAS_NEXT, true);
        if(playMainCallWaitRing) {
            //页面发起的转接，先断开主被叫
            this.debug("页面转接请求，主叫状态：{},被叫状态：{}", this.mainCall.getState(), oldAgent.getState());
            this.mainCall.disconnectCall(oldAgent);
        }
        if(this.user.getTransBackFlag()) {
            String agentTip = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_TRANS_ANGET_TIP);
            this.debug("配置了转接失败接回原坐席，播放坐席转接提示音：", agentTip);
            oldAgent.play(agentTip, true, false, true, 0, 0);
        } else {
            this.info("配置了转接失败不接回，挂断原被叫！");
            oldAgent.onHook();
        }
        this.debug("转接请求，转接的工号：{},主叫状态：{},被叫状态：{}", bindPhoneLsh, this.mainCall.getState(), oldAgent.getState());
        if (this.mainCall.getState() == Constants.CALL_CONTENT) {
            //this.mainCall.disconnectCall(oldAgent); 挂断oldAgent时默认会disconnect
            if (stopRecord && this.voiceRecord != null) {
                this.debug("停止正在进行的录音。");
                this.mainCall.stopRecord();
                this.await(300);
            }
            BindPhone bp = this.service.getLocalBindPhone(this.user.getNumber(), bindPhoneLsh);
            if (bp == null || !MidwareConstants.AGENT_STATE_IDLE.equals(this.service.getAgentState(bp))) {
                if(this.user.getTransBackFlag()) {
                    this.warn("根据绑定号码流水号：{}未找到对应空闲坐席！接回原坐席", bindPhoneLsh);
                    //接回原坐席
                    callBackAgent(this.mainCall, oldAgent);
                } else {
                    this.warn("根据绑定号码流水号：{}未找到对应空闲坐席！挂断主叫", bindPhoneLsh);
                    this.mainCall.onHook();
                }
                return;
            }
            //呼叫轨迹：工号转接
            if(this.user.isTraceFlag()) {
                this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), null, this.districtDesc, "工号转接", "工号转接成功，匹配到工号：" + bp.getReportNum(), null));
            }
            if (playMainCallWaitRing) {
                String ring = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_IVR);
                this.debug("等待背景彩铃：{}", ring);
                this.mainCall.play(ring, true, false, true, 0, 0);
            }
            this.isCallConnected = false;
            this.currentBindPhone = bp;
            
            this.currentDept = this.service.getLocalDept(bp.getCustomerNo(), bp.getDeptLsh());
            Call callee = this.makeCall(true);
            if (callee != null) {
                this.debug("转移的坐席呼通！");
                oldAgent.onHook();
                initCallParams(callee, MidwareConstants.CALL_TYPE_TRANS);
                this.transList.add(callee);
                //呼叫轨迹：坐席接听
                if(this.user.isTraceFlag()) {
                    this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), this.currentBindPhone.getOrigBindPhoneNo(), this.districtDesc, this.currentDept.getName(), "坐席接听", this.currentDept.getDeptLsh()));
                }
                if(this.functionConfig != null && this.functionConfig.isTransferTipFlag()) {
                    //播放转接提示音
                    this.debug("播放转接提示音！");
                    this.playTransTipRing(callee);
                } else {
                    this.debug("未开通转接提示音，播放转移坐席提示音！");
                    this.playAgentAlertRing(callee);
                }
                if(this.mainCall.getState() != Constants.CALL_CONTENT || callee.getState() != Constants.CALL_CONTENT) {
                    throw new MidwareException("主被叫有一方已挂机！");
                }
                //更新坐席状态，发消息
                if (!this.currentBindPhone.isPbx()) {
                    this.service.callConnectedAsync(this.currentBindPhone, this.talkNote, this.currentDept, user.getPushStatusFlag(), true);
                }
                this.playNum(this.mainCall, this.getLastAgent());
                if (this.mainCall.isIsPlaying()) {
                    this.mainCall.stopPlay();
                }
                boolean connected = this.connectCall(this.mainCall, callee);
                if (!connected) {
                    String currentState = this.service.getAgentState(this.currentBindPhone);
                    this.warn("连接主被叫失败！当前电话状态：{}， 当前绑定号码状态：{}", this.agent.getState(), currentState);
//                    if (callee.getState() != Constants.CALL_IDLE || currentState.startsWith(AGENT_STATE_CALLING)) {
//                        this.service.midwareUpdateAgentState(this.currentBindPhone, AGENT_STATE_IDLE, true);
//                    }
//                    if (this.agent.getState() == Constants.CALL_IDLE && currentState.equals(AGENT_STATE_CONNECT)) {
//                        this.service.midwareUpdateAgentState(this.currentBindPhone, AGENT_STATE_IDLE, true);
//                    }
                    throw new MidwareException("连接主被叫失败！");
                }
                this.isCallConnected = true;
                //                this.talkNote.setCalleeOffhookTime(new Date());
                this.talkNote.setLsh(this.currentBindPhone.getId());
                this.talkNote.setGh(StringUtils.isNumeric(this.currentBindPhone.getReportNum()) ? Long.valueOf(this.currentBindPhone.getReportNum()) : null);
                this.talkNote.setDeptId(this.currentDept.getDeptLsh());
                this.talkNote.setCalleeNo(this.currentBindPhone.getOrigBindPhoneNo());
                this.service.saveOrUpdateTalkNote(this.talkNote);
                this.await(300);
                reStartRecord(this.mainCall, callee);
                this.calleeReceiveDTMF();
            } else {
                //呼叫轨迹：坐席无应答
                if(this.user.isTraceFlag() && this.mainCall.getState() != Constants.CALL_IDLE) {
                    this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), this.currentBindPhone.getOrigBindPhoneNo(), this.districtDesc, this.currentDept.getName(), "坐席无应答", this.currentDept.getDeptLsh()));
                }
                if(this.user.getTransBackFlag()) {
                    //接回原坐席
                    callBackAgent(this.mainCall, oldAgent);
                } else {
                    this.mainCall.onHook();
                }
            }
        }
    }
    
    private void callBackAgent(Call caller, Call angent) {
        this.callBackAgent(caller, angent, true);
    }
    
    private void callBackAgent(Call caller, Call angent, boolean callerPlayTip) {
        if(caller.getState() == Constants.CALL_CONTENT && angent.getState() == Constants.CALL_CONTENT) {
            this.debug("转接失败，转回原坐席！");
            caller.stopPlay();
            this.await(200);
            if(callerPlayTip) {
                String callerTip = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_TRANS_CALLER_TIP);
                this.debug("播放主叫接回提示音：{}", callerTip);
                caller.play(callerTip, false, false, false, 10, 0);
            }
            angent.stopPlay();
            if(caller.getState() != Constants.CALL_CONTENT) {
                this.warn("接回原被叫失败，主叫已挂机！");
                return;
            }
            if(angent.getState() != Constants.CALL_CONTENT) {
                this.warn("接回原被叫失败，原坐席已挂机！");
                this.callFail();
                return;
            }
            caller.connectCall(angent, !dualChannel);
            this.isCallConnected = true;
            reStartRecord(caller, angent);
            angent.setParam(RECEIVED_DTMF_TIME, 0L);
            angent.receiveDTMFAsync();
        } else {
            this.warn("接回原坐席失败，主叫或原坐席已挂机！");
            if(this.mainCall.getState() == Constants.CALL_CONTENT) {
                this.callFail();
            }
        }
    }
    
    private void reStartRecord(Call caller, Call callee) {
        if (this.voiceRecord != null) {
            String recordPath = this.voiceRecord.getFilePath();
            this.debug("用户的录音路径：{}", recordPath);
            if (dualChannel && !service.isSipFlag()) {
                String[] records = recordPath.split(",");
                caller.record(records[0], 0, true);
                callee.record(records[1], 0, true);
            } else {
                recordPath = recordPath.substring(0, recordPath.lastIndexOf(".")) + ".wav";
                caller.record(recordPath, 0, true);
            }
        }
    }

    private void calleeReceiveDTMF() {
        if (this.user.isGhFlag() || this.user.isIvrFlag()) {
            if (this.functionConfig != null && this.functionConfig.isInputTransferFlag()) {
                this.warn("用户关闭按键转接功能！");
            } else {
                this.getLastAgent().receiveDTMFAsync();
                this.debug("坐席开始异步收码");
            }
        } else {
            this.warn("用户未开通报工号及ivr功能！按键转接功能不能使用！");
        }
    }

    /**
     * 按键转移
     */
    @Override
    public void dtmfTrans() {
        if(!this.user.isGhFlag()) {
            this.warn("用户未开通报工号功能，工号转接功能不可用！");
            return;
        }
        Call callee = this.getLastAgent();
        this.debug("按键转接请求，主叫状态：{},被叫状态：{}", this.mainCall.getState(), callee.getState());
        int delay = this.service.getSipConnectDelay();
        if (delay > 0) {
            this.mainCall.stopPlay();
            callee.stopPlay();
            this.await(delay);
        }
        this.mainCall.disconnectCall(callee);
        if (this.mainCall.getState() == Constants.CALL_CONTENT) {
            if (this.voiceRecord != null) {
                this.debug("停止正在进行的录音。");
                this.mainCall.stopRecord();
                if (dualChannel) {
                    callee.stopRecord();
                }
            }
            String waitRing = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_IVR);
            this.debug("等待背景彩铃：{}", waitRing);
            this.mainCall.play(waitRing, true, false, true, 0, 0);

            String transRing = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_TRANSFER);
            this.debug("坐席转移彩铃：{}", transRing);
            callee.play(transRing, true, false, true, 0, 0);

            String gh = callee.receiveDTMFSync(10, "#", 20);
            this.debug("坐席输入的工号为：{}", gh);
            if (StringUtils.isNotEmpty(gh)) {
                BindPhone bp = this.service.getBindPhoneByCustomerNoAndReportNum(this.user.getNumber(), gh);
                if (bp != null) {
                    this.transCall(bp.getLsh(), false, false);
                } else {
                    //呼叫轨迹：工号转接
                    if(this.user.isTraceFlag()) {
                        this.addTrace(new CallTraceDto(user.getNumber(), this.mainCall.getCaller(), null, this.districtDesc, "工号转接", "工号转接失败，工号" + gh + "不存在，或该工号" + gh + "忙", null));
                    }
                    if(this.user.getTransBackFlag()) {
                        //接回原坐席
                        this.debug("未找到对应工号的绑定号码，接回原坐席");
                        String agentTip = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_TRANS_ANGET_TIP);
                        this.debug("播放坐席转接提示音：", agentTip);
                        callee.play(agentTip, true, false, true, 0, 0);
                        callBackAgent(this.mainCall, callee);
                    } else if(this.mainCall.getState() == Constants.CALL_CONTENT) {
                        this.debug("未找到对应工号的绑定号码，挂断主被叫");
                        this.hangupBothCall();
                    }
                }
            } else {
                if(this.user.getTransBackFlag()) {
                    this.debug("按键收码超时，接回原被叫");
                    if(this.mainCall.getState() == Constants.CALL_CONTENT && callee.getState() == Constants.CALL_CONTENT) {
                        //主被叫都未挂机，接回原坐席
                        this.debug("主被叫都未挂机，接回原坐席");
                        String agentTip = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_TRANS_ANGET_TIP);
                        this.debug("播放坐席转接提示音：", agentTip);
                        callee.play(agentTip, true, false, true, 0, 0);
                        callBackAgent(this.mainCall, callee);
                    } else if(this.mainCall.getState() == Constants.CALL_CONTENT && this.voiceScore == null) {
                        this.warn("接回原被叫失败，原坐席已挂机！主叫进入满意度");
                        this.processSatisfy();
                    } else {
                        this.warn("主叫已挂机");
                    }
                } else if(this.mainCall.getState() == Constants.CALL_CONTENT) {
                    this.debug("按键收码超时，挂断主被叫");
                    this.hangupBothCall();
                }
            }
        }
    }

    private void playDeptRing(Dept dept, boolean loop, boolean async) {
        String ring;
        if (MidwareConstants.DEFAULT_RING_TRANSFER_DEPT_CODE.equals(this.currentDept.getColorRingLsh())) {
            ring = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_TRANSFER_DEPT_CODE);
        } else {
            ring = this.service.getRingFile(dept);
            if ((ring == null || !this.service.isRingExist(ring))) {
                this.warn("未找到部门：{}的彩铃！终止播放", dept.getName());
                return;
            }
        }
        this.debug("获得部门：{}的彩铃文件：{}", dept.getName(), ring);
        if (this.mainCall.isIsPlaying()) {
            this.mainCall.stopPlay();
            this.await(200);
        }
        this.mainCall.play(ring, loop, false, async, 0, 0);
    }

    private void playDeptCompanyRing(Dept dept) {
        String ring = this.service.getCompanyRingFile(dept);
        if ((ring == null || !this.service.isRingExist(ring))) {
            this.warn("未找到部门：{}的宣传彩铃！终止播放", dept.getName());
            return;
        }
        this.debug("获得部门：{}的宣传彩铃文件：{}", dept.getName(), ring);
        if (this.mainCall.isIsPlaying()) {
            this.mainCall.stopPlay();
            this.await(200);
        }
        this.mainCall.play(ring, false, false, false, 0, 0);
    }

    private void playDeptResetRing(Dept dept) {
        String ring = this.service.getResetRingFile(dept);
        if ((ring == null || !this.service.isRingExist(ring))) {
            this.warn("未找到部门：{}的按键重播彩铃！终止播放", dept.getName());
            return;
        }
        this.debug("获得部门：{}的按键重播彩铃文件：{}", dept.getName(), ring);
        this.mainCall.play(ring, false, false, true, 0, 0);
    }

    private String getCallStateDesc(int callState) {
        switch (callState) {
            case 0:
                return "空闲";
            case 1:
                return "呼叫中";
            case 2:
                return "振铃";
            case 3:
                return "通话中";
            default:
                return "其他";
        }
    }

    private void initCallParams(Call callee, String callType) {
        callee.attachTeleThread(this);
        callee.setParam(RECEIVED_DTMF, new StringBuffer());
        callee.setParam(RECEIVED_DTMF_TIME, 0L);
        callee.setParam(CALL_TYPE, callType);
        callee.setParam(CALL_BINDPHONE_ID, this.currentBindPhone.getId());
        callee.setParam(CALL_BINDPHONE_LSH, this.currentBindPhone.getCustomerNo() + "_" + this.currentBindPhone.getLsh());
        callee.setParam(CALL_AGENT_ID, this.currentBindPhone.getAgentId());
    }

    private void gotoSupport() {
        if (this.mainCall.getState() == Constants.CALL_IDLE) {
            throw new MidwareException("主叫已挂机！");
        }
        if (this.mainCall.isIsPlaying()) {
            this.mainCall.stopPlay();
            this.await(500);
        }
        String supportRing = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_SUPPORT);
        this.debug("播放支持彩铃{}", supportRing);
        this.mainCall.play(supportRing + "," + supportRing, false, true, false, 60, 0);
        this.mainCall.onHook();
    }

    private int playSmartBroadRing(Dept dept) {
        if (this.mainCall.isIsPlaying()) {
            this.mainCall.stopPlay();
            this.await(500);
        }
        String ring = service.getSmartBroadRingFile(dept);
        this.currentRing = ring;
        if (ring != null) {
            this.debug("播放部门语音播报彩铃：{}", ring);
            return this.mainCall.play(ring, !this.user.isSmartBroadOnceFlag(), false, true, 0, 0);
        } else {
            this.warn("未配置语音播报彩铃！");
            return 0;
        }
    }
    
    private void addTrace(CallTraceDto dto) {
        this.traceList.add(dto);
    }
    
    private void sendTraceMessage() {
        if(this.traceList != null && !this.traceList.isEmpty()) {
            this.service.sendTraceMessage(traceList);
            this.traceList.clear();
        }
    }

    @Override
    public void cdr(String record) {
        this.debug("收到cdr事件，录音地址：{}", record);
        if(this.voiceRecord != null) {
            this.voiceRecord.setFilePath(record);
            this.voiceRecord.setFileName(record.substring(record.lastIndexOf("/") + 1));
            this.service.saveOrUpdateRecord(voiceRecord);
        }
        if(this.voiceBox != null) {
            this.voiceBox.setFilePath(record);
            this.voiceBox.setFileName(record.substring(record.lastIndexOf("/") + 1));
            this.service.saveOrUpdateVoiceBox(this.voiceBox);
        }
    }
    
    private void offline(int queueIndex) {
        //定时发送短信提醒
        this.debug("添加离线排队发送消息任务，号码：{}，队列位置：{}", this.mainCall.getCaller(), queueIndex);
        this.service.addOfflineTask(this.user.getNumber(), this.currentDept.getDeptLsh(), this.mainCall.getCaller(), queueIndex);
    }
    
    private boolean isBlackInIvr(String ivrKey) {
        return service.isBlackInIvr(this.user.getNumber(), this.fixedCallerNo, ivrKey);
    }
    
    private void blackInIvr() {
        if(this.isPlayBlackTip()) {
            String blackTip = this.getBlackTipRing();
            this.info("播放黑名单彩铃：{}", blackTip);
            this.mainCall.stopPlay();
            this.await(200);
            this.mainCall.play(blackTip, false, false, false, 10, 0);
        }
        if(this.mainCall.getState() != Constants.CALL_IDLE) {
            this.mainCall.onHook();
        }
    }
    
    private boolean isPlayBlackTip() {
        //目前写死播放提示彩铃，如果做成通用的需要改造
        return true;
    }
    
    private String getBlackTipRing() {
        //目前写死太平洋的提示彩铃，如果做成通用的需要改造
        return this.service.getDefaultRing("TPY_BLACK_TIP.wav");
    }

    /**
     * 防伪码验证流程
     */
    private void processSecurityCheck() {
        String inputRing = service.getDefaultRing(MidwareConstants.DEFAULT_RING_VALID_INPUT);
        this.debug("开始播放防伪码彩铃：{}", inputRing);
        this.mainCall.play(inputRing, false, false, true, 0, 0);
        int timeoutSecond = this.currentDept.getIvrStayTime() != null ? this.currentDept.getIvrStayTime() + 1 : this.service.getIvrTimeoutSeconds();
        if (this.functionConfig != null) {
            if (functionConfig.getInputTimeOut() != null) {
                timeoutSecond = functionConfig.getInputTimeOut();
                this.debug("号码配置了按键收码等待时长：{}", timeoutSecond);
            }
        }
        this.debug("开始防伪码收码，超时时间：{}", timeoutSecond);
        this.securityDtmfFlag = true;
        String code = this.receiveIvrDtmf(30, timeoutSecond, null);
        if(StringUtils.endsWith(code, "#")) {
            code = code.substring(0, code.length() -1);
        }
        this.debug("用户输入的防伪码：{}", code);
        if(StringUtils.isEmpty(code)) {
            this.warn("未收到用户按键，结束流程");
            if(this.mainCall.getState() == Constants.CALL_CONTENT) {
                this.mainCall.onHook();
            }
            return;
        }
        int result = this.service.checkSecurityCode(this.user.getNumber(), code);
        this.debug("防伪码验证返回：{}", result);
        String ring = this.service.getSecurityRing(result);
        this.debug("播放防伪码结果音频：{}", ring);
        this.mainCall.play(ring, false, false, false, 5, 0);
        if(this.mainCall.getState() == Constants.CALL_CONTENT) {
            this.mainCall.onHook();
        }
    }

    @Override
    public void dtmfToIvr() {
        //转移到导航
        if(!this.user.isIvrFlag()) {
            this.warn("用户未开通ivr功能，转接导航功能不可用！");
            return;
        }
        Call callee = this.getLastAgent();
        this.debug("按键转导航请求，主叫状态：{},被叫状态：{}", this.mainCall.getState(), callee.getState());
        int delay = this.service.getSipConnectDelay();
        if (delay > 0) {
            this.mainCall.stopPlay();
            callee.stopPlay();
            this.await(delay);
        }
        this.mainCall.disconnectCall(callee);
        if (this.voiceRecord != null) {
            this.debug("停止正在进行的录音。");
            this.mainCall.stopRecord();
            if (dualChannel) {
                callee.stopRecord();
            }
        }
        String waitRing = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_IVR);
        this.debug("主叫播放等待背景彩铃：{}", waitRing);
        this.mainCall.play(waitRing, true, false, true, 0, 0);
        
        String transRing = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_TRANS_TO_IVR);
        this.debug("坐席播放导航转接彩铃：{}", transRing);
        callee.play(transRing, true, false, true, 0, 0);
        
        String ivrKey = callee.receiveDTMFSync(10, "#", 20);
        Dept dept = this.service.getTopDeptByIvrKey(this.user.getNumber(), ivrKey);
        this.debug("坐席输入的导航按键为：{}，根据输入的导航按键，找到的最上层部门：{}", ivrKey, dept);
        if (dept == null) {
            String failTip = this.service.getDefaultRing(MidwareConstants.DEFAULT_RING_TRANS_TO_IVR_FAIL);
            this.debug("坐席播放转接失败提示彩铃：{}", failTip);
            callee.play(failTip, false, false, false, 15, 0);
            if (this.mainCall.getState() == Constants.CALL_CONTENT && callee.getState() == Constants.CALL_CONTENT) {
                //主被叫都未挂机，接回原坐席
                this.debug("主被叫都未挂机，接回原坐席");
                callBackAgent(this.mainCall, callee, false);
            } else if (this.mainCall.getState() == Constants.CALL_CONTENT) {
                this.warn("接回原被叫失败，原坐席已挂机！主叫进入满意度");
                this.processSatisfy();
            } else {
                this.warn("主叫已挂机");
            }
        } else {
            this.debug("找到按键对应的导航，挂断原坐席！");
            callee.setParam(MidwareConstants.CALL_HAS_NEXT, true);
            callee.onHook();
            this.currentDept = dept;
            this.mainCall.stopPlay();
            this.await(200);
            this.playCurrentDeptRing(false, true, true);
            Dept lastDept = this.getIvrDept();
            this.debug("ivr导航找到的最终部门：{}", lastDept);
            if (lastDept == null) {
                this.processSatisfy();
            } else if (lastDept.isLoopPlayRing()) {
                this.info("开始循环播放彩铃。。。");
                this.playDeptRing(dept, true, true);
            } else {
                this.ivrTrans = true;
                this.queueUseable = this.service.isQueueUseable(this.currentDept);
                processCallAgent();
            }
        }
    }
}
