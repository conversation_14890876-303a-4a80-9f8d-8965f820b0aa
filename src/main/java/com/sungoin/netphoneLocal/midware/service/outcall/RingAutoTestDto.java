package com.sungoin.netphoneLocal.midware.service.outcall;

public class RingAutoTestDto {
    /**
     * 测试记录Id
     */
    public String testId;
    /**
     * 400号码
     */
    public String numberCode;

    /**
     * 主叫号码
     */
    public String caller;

    /**
     * 被叫号码
     */
    public String callee;

    /**
     * 彩铃流水号码
     */
    public Long ringLsh;

    public String notifyUrl;

    /**
     * 呼叫时间
     */
    public Long callTime;

    public String getTestId() {
        return testId;
    }

    public void setTestId(String testId) {
        this.testId = testId;
    }

    public String getNumberCode() {
        return numberCode;
    }

    public void setNumberCode(String numberCode) {
        this.numberCode = numberCode;
    }

    public String getCaller() {
        return caller;
    }

    public void setCaller(String caller) {
        this.caller = caller;
    }

    public String getCallee() {
        return callee;
    }

    public void setCallee(String callee) {
        this.callee = callee;
    }

    public Long getRingLsh() {
        return ringLsh;
    }

    public void setRingLsh(Long ringLsh) {
        this.ringLsh = ringLsh;
    }

    public Long getCallTime() {
        return callTime;
    }

    public void setCallTime(Long callTime) {
        this.callTime = callTime;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }
}
