/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.midware.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.SendResult;
import com.sungoin.netphoneLocal.business.bean.BusinessConstants;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.AGENT_STATE_CALLING;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.AGENT_STATE_CONNECT;

import java.io.File;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.sungoin.netphoneLocal.business.bean.CallNumDto;
import com.sungoin.netphoneLocal.business.bean.RingAutoNotifyInfoDto;
import com.sungoin.netphoneLocal.business.bean.SecurityCodeCheckDto;
import com.sungoin.netphoneLocal.business.bean.SystemMessageDto;
import com.sungoin.netphoneLocal.business.bean.TalkNoteDto;
import com.sungoin.netphoneLocal.business.bean.ZeroCallDto;
import com.sungoin.netphoneLocal.business.mq.CallTraceDto;
import com.sungoin.netphoneLocal.business.mq.SmsSendDto;
import com.sungoin.netphoneLocal.business.mq.WhitelistCallCountSyncDto;
import com.sungoin.netphoneLocal.business.po.*;
import com.sungoin.netphoneLocal.business.rmi.RmiService;
import com.sungoin.netphoneLocal.business.service.BaseService;
import com.sungoin.netphoneLocal.business.service.BusinessService;
import com.sungoin.netphoneLocal.business.service.InterConnectionService;
import com.sungoin.netphoneLocal.business.service.NumberFuncConfigService;
import com.sungoin.netphoneLocal.config.CommonSettings;
import com.sungoin.netphoneLocal.constants.FunctionSwitchConstants;
import com.sungoin.netphoneLocal.constants.MqConstants;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import com.sungoin.netphoneLocal.midware.config.MidwareSettings;
import com.sungoin.netphoneLocal.midware.service.sungoin.SungoinResponse;
import com.sungoin.netphoneLocal.midware.service.task.*;
import com.sungoin.netphoneLocal.socket.message.AgentMessage;
import com.sungoin.netphoneLocal.socket.message.CalleeOnhookMessage;
import com.sungoin.netphoneLocal.socket.message.CallincomeMessage;
import com.sungoin.netphoneLocal.socket.message.DeptMessage;
import com.sungoin.netphoneLocal.socket.message.SocketMessage;
import com.sungoin.netphoneLocal.util.CacheUtil;
import com.sungoin.netphoneLocal.util.ConcurrentHelper;
import com.sungoin.netphoneLocal.util.DateTimeUtil;
import com.sungoin.netphoneLocal.util.HttpHelper;
import com.sungoin.netphoneLocal.util.JsonHelper;
import com.sungoin.netphoneLocal.util.MyStringUtil;
import com.sungoin.netphoneLocal.util.VoiceUtil;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> 2015-7-28
 */
@Service
public class ProcessService {

    private static final Logger log = LoggerFactory.getLogger(ProcessService.class);
    
    public static final String MONITOR_PREFIX = "L_";
    
    @Resource
    private CommonSettings commonSettings;

    @Resource
    private MidwareSettings settings;

    @Resource
    private BaseService baseService;
    
    public BaseService getBaseService() {
        return this.baseService;
    }

    @Resource
    private BusinessService businessService;

    public BusinessService getBusinessService() {
        return this.businessService;
    }

    @Resource
    RmiService rmiService;

    @Resource
    InterConnectionService ics;

    ExecutorService es;
	ScheduledExecutorService ses;
    Map<String, String> specialOrigNoMap;
    
    @Resource
    private NumberFuncConfigService configService;

    @Resource
    @Qualifier("PlatformMessageProducer")
    private Producer mqProducer;
    
    private final Map<String, Future> orderMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        if (this.commonSettings.isMainServer()) {
            this.baseService.startResetAgentStatus(AGENT_STATE_CALLING + "_" + this.commonSettings.isMainServer());
            this.baseService.startResetAgentStatusWithOrder(AGENT_STATE_CONNECT);
        } else {
            this.rmiService.startResetAgentStatus(AGENT_STATE_CALLING + "_" + this.commonSettings.isMainServer());
        }
        this.es = Executors.newCachedThreadPool();
		this.ses = Executors.newScheduledThreadPool(2);
        this.specialOrigNoMap = new HashMap();
        String speNoStr = this.commonSettings.getSpecialOrig();
        if (StringUtils.isNotEmpty(speNoStr)) {
            String[] array = speNoStr.split(",");
			Stream.of(array).filter(s -> s.length() > 11).forEach(s -> {
				 this.specialOrigNoMap.put(s.substring(0, 10), s.substring(11));
			});
        }
    }
    
    public void clearOrderMap() {
        orderMap.clear();
    }

    public void putOrder(String key, Future future) {
        orderMap.put(key, future);
    }
    
    public void removeOrder(String key) {
        orderMap.remove(key);
    }
    
    public void stopOrder(String key) {
        Future future = orderMap.remove(key);
        if(future != null) {
            log.debug("找到需要终止的整理任务，key={}", key);
            future.cancel(true);
        }
    }
    
    public String getFixedCallerNo(String callNo) {
        //118开头的号码特殊处理，不改变主叫
        if(callNo.length() == 8 && callNo.startsWith("118")) {
            return callNo;
        }
        return this.baseService.processCallerNo(callNo);
    }

    public String getFixedCalleeNo(String callNo) {
        return this.baseService.processCallNo(callNo);
    }

    public int getTimeOutInputCount() {
        return this.settings.getTimeOutInputCount();
    }

    public User getUserByOriginalNo(boolean groupCall, String originalNo, String callerNo) {
        return this.baseService.getUserByOriginalNo(groupCall, originalNo, callerNo);
    }
    
    public boolean isGroupCall(String originalNo, String callerNo) {
        return this.baseService.isGroupCall(originalNo, callerNo);
    }

    @Transactional(readOnly = true)
    public boolean isCallerPassBlackAndWhiteNo(User user, String caller) {
        Date now = new Date();
        user = this.baseService.getUser(user.getId());
        String checkCaller = this.getCheckCaller(caller);
        List<BlackList> globalBlackList = user.getGlobalBlackList();
        if (!user.getWhiteList().isEmpty()) {
            Optional<WhiteList> opt = user.getWhiteList().stream().filter(w -> (w.getExpireDate() == null || !w.getExpireDate().before(now)) && w.isNoInWhiteList(checkCaller)).findAny();
            return opt.isPresent();
        } else if(!globalBlackList.isEmpty()){
            Optional<BlackList> opt = globalBlackList.stream().filter(b -> (b.getExpireDate() == null || !b.getExpireDate().before(now)) && b.isNoInBlackList(checkCaller)).findAny();
            return !opt.isPresent();
        } 
		return true;
    }
    
    private String getCheckCaller(String caller) {
        String checkCaller = caller;
        if (MyStringUtil.isMobile(caller)) {
            String fixedCaller = caller.startsWith("0") ? caller.substring(1) : caller;
            checkCaller = this.baseService.getDistrictNo(fixedCaller) + fixedCaller;
        } else {
            //本地固话补上本地区号
            if (!caller.startsWith("0")) {
                checkCaller = this.commonSettings.getLocalNo() + caller;
            }
        }
        return checkCaller;
    }

    public void saveOrUpdateTalkNote(final TalkNote talkNote) {
        this.es.submit(() -> {
            try {
                ProcessService.this.businessService.saveOrUpdate(talkNote);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
        });
    }

    @Transactional
    public void endTalkNote(TalkNote talkNote) {
        if (talkNote.isEndFlag() && talkNote.getCalleeOffhookTime() != null) {
            talkNote.setTalkInterval(DateTimeUtil.getTimeDifference(talkNote.getCalleeOffhookTime(),
                talkNote.getOnhookTime()));
        }
        final TalkNote note = talkNote;
        this.es.submit(() -> {
            try {
                ProcessService.this.businessService.saveOrUpdate(note);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
            ProcessService.this.businessService.saveTalkTask(new OutDataTask(note.getId(), OutDataType.TALK));
        });
    }

    @Transactional(readOnly = true)
    public Dept getRootDept(User user) {
        long deptCount = this.baseService.findDeptCountByCustomerNo(user.getNumber());
        log.debug("{}:导航总数:{}", user.getNumber(), deptCount);
        if (deptCount > this.settings.getMaxDeptCount()) {
            return this.baseService.getDeptByCustomerNoAndDeptLsh(user.getNumber(), user.getNumber());
        } else {
            user = this.baseService.getUser(user.getId());
            return user.getRootDept();
        }
    }

    @Transactional(readOnly = true)
    public CrbtRing getCrbtRingFile(Dept dept) {
        dept = this.baseService.getDept(dept.getId());
        CrbtRing cring = this.baseService.findCrbtRingWithTemplate(dept);
        log.debug("根据时间模板找到的炫铃：{}", cring);
        if(cring != null) {
            return cring;
        }
        return dept.getCurrentCrbtRing(this.baseService.isHoliday(new Date()));
    }

    @Transactional
    public String getRingFile(Dept dept) {
        CrbtRing cring = null;
        if(dept.isRootDept()) {
            cring = this.getCrbtRingFile(dept);
        }
        ColorRing ring = null;
        if (cring != null && cring.getColorRing() != null) {
            ring = cring.getColorRing();
        } else {
//            ring = dept.getColorRing();
            User user=baseService.getUserByNumber(dept.getCustomerNo());
            if(user.isIvrFlag() || user.isColorringFlag()){
            	ring = dept.getColorRing();
            } else {
            	log.info("{}:导航、彩铃开关均关闭，彩铃返回空", dept.getCustomerNo());
            }
            
			//去掉容错代码
//			if(ring == null && dept.getColorRingLsh() != null) {
//				ring = this.baseService.findColorRingByCustomerAndLsh(dept.getCustomerNo(), dept.getColorRingLsh());
//				if(ring != null) {
//					dept.setColorRing(ring);
//					this.baseService.saveOrUpdateDept(dept);
//				}
//			}
        }
        return ring != null ? this.settings.getVocDataPath() + ring.getFilePath() : null;
    }
    
    @Transactional
    public String getRingFile(CrbtRing ring) {
        CrbtRing localRing = this.baseService.getCrbtRing(ring.getId());
        return this.settings.getVocDataPath() + localRing.getColorRing().getFilePath();
    }
    
    @Transactional
    public String getRingFile(String customerNo, String ringLsh) {
        ColorRing localRing = this.baseService.findColorRingByCustomerAndLsh(customerNo, ringLsh);
        return localRing == null ? null : this.settings.getVocDataPath() + localRing.getFilePath();
    }
    
    @Transactional
    public String getRingFile(ColorRing ring) {
        ColorRing localRing = this.baseService.getColorRing(ring.getId());
        return this.settings.getVocDataPath() + localRing.getFilePath();
    }

    @Transactional
    public String getSmartBroadRingFile(Dept dept) {
        dept = this.baseService.getDept(dept.getId());
        ColorRing ring = dept.getSmartBroadRing();
        return ring != null ? this.settings.getVocDataPath() + ring.getFilePath() : null;
    }
    
    @Transactional(readOnly = true)
    public String getCompanyRingFile(Dept dept) {
        dept = this.baseService.getDept(dept.getId());
        ColorRing ring = dept.getCompanyRing();
        return ring != null ? this.settings.getVocDataPath() + ring.getFilePath() : null;
    }
    
    @Transactional(readOnly = true)
    public String getResetRingFile(Dept dept) {
        dept = this.baseService.getDept(dept.getId());
        ColorRing ring = dept.getResetRing();
        return ring != null ? this.settings.getVocDataPath() + ring.getFilePath() : null;
    }
    
    public String getDefaultRing(String ringName) {
        return this.settings.getDefaultRingFile(ringName);
    }

    public boolean isRingExist(String ringFile) {
        if (!this.settings.isRingFileCheck()) {
            log.info("配置文件不需要检测彩铃文件是否存在！返回true");
            return true;
        } else {
            return Files.exists(Paths.get(ringFile));
        }
    }

    public int getIvrTimeoutSeconds() {
        return this.settings.getIvrTimeoutSeconds();
    }

    @Transactional(readOnly = true)
    public boolean hasChildDepts(Dept parent) {
        parent = this.baseService.getDept(parent.getId());
        return parent.hasChildDepts();
    }

    @Transactional(readOnly = true)
    public Dept getChildDeptByDivide(Dept parent, String callerNo) {
        parent = this.baseService.getDept(parent.getId());
        return this.baseService.getChildDeptBySatisfyTimePlan(parent, callerNo);
    }

	public boolean hasChildDeptsBySql(Dept parent) {
        if(parent.isRootDept() && StringUtils.equals("on", this.getFunctionData(parent.getCustomerNo(), FunctionSwitchConstants.FUNCTION_ROOT_HAS_CHILD_DEPT))) {
            log.debug("{} 配置了根部门下存在子导航", parent.getCustomerNo());
            return true;
        }
		long count = this.baseService.findDeptCountByCustomerNoAndParentDeptLsh(parent.getCustomerNo(),
            parent.getDeptLsh());
		return count > 0;
	}
	
    @Transactional(readOnly = true)
    public Dept getChildDeptByIvrKey(Dept parent, String ivrKey) {
        Long count = this.baseService.findDeptCountByCustomerNoAndParentDeptLsh(parent.getCustomerNo(),
            parent.getDeptLsh());
        log.debug("号码:{},导航流水号:{},子导航个数:{}", parent.getCustomerNo(), parent.getDeptLsh(), count);
        if (count > this.settings.getMaxDeptCount()) {
            return this.baseService.findDeptByParentDeptAndIvrKey(parent, ivrKey);
        } else {
            parent = this.baseService.getDept(parent.getId());
            return parent.getChildDeptByIvrKey(ivrKey);
        }
    }

    public int getVoiceBoxMaxTime() {
        return this.settings.getVoiceboxMaxSeconds();
    }

    public VoiceScore startSatisfy(TalkNote talkNote) {
        VoiceScore vs = new VoiceScore();
        vs.setCalleeNo(talkNote.getCalleeNo());
        vs.setCallerNo(talkNote.getCallerNo());
        vs.setCustomerNo(talkNote.getCustomerNo());
        vs.setGh(talkNote.getGh() + "");
        vs.setStartTime(new Date());
        vs.setTalkNoteId(talkNote.getId());
        this.businessService.saveOrUpdateVoiceScore(vs);
        return vs;
    }

    public String getVoiceBoxAlertRing(String customerNo) {
        ColorRing cr = this.baseService.getVoiceBoxAlertRing(customerNo);
        return cr != null ? this.settings.getVocDataPath() + cr.getFilePath() : null;
    }

    @Transactional(readOnly = true)
    public String getAgentAlertRing(Dept dept) {
        dept = this.baseService.getDept(dept.getId());
        return dept.getAgentAlertRing() != null ? this.settings.getVocDataPath()
            + dept.getAgentAlertRing().getFilePath() : null;
    }

    @Transactional(readOnly = true)
    public String getSatiRing(Dept dept) {
        dept = this.baseService.getDept(dept.getId());
        return dept.getSatiRing() != null ? this.settings.getVocDataPath() + dept.getSatiRing().getFilePath() : this
            .getDefaultRing(MidwareConstants.DEFAULT_RING_SCORE);
    }

    public int getAgentAlertRingTime() {
        return this.settings.getAgentAlertRingTime();
    }

    public String getReportHeadFile() {
        return this.settings.getDefaultRingFile(MidwareConstants.DEFAULT_RING_NUMHEAD);
    }

    public String getReportEndFile() {
        return this.settings.getDefaultRingFile(MidwareConstants.DEFAULT_RING_NUMEND);
    }

    public String getQueueBg() {
        return this.settings.getDefaultRingFile(MidwareConstants.DEFAULT_RING_QUEUEBG);
    }

    public String getQueueHeadFile() {
        return this.settings.getDefaultRingFile(MidwareConstants.DEFAULT_RING_QUEUEHEAD);
    }
	
	public String getQueueFile1() {
        return this.settings.getDefaultRingFile(MidwareConstants.DEFAULT_RING_QUEUE_1);
    }
	
	public String getQueueFile3() {
        return this.settings.getDefaultRingFile(MidwareConstants.DEFAULT_RING_QUEUE_3);
    }
	
	public String getQueueFile4(boolean leaveMessage) {
        return this.settings.getDefaultRingFile(leaveMessage ? MidwareConstants.DEFAULT_RING_QUEUE_4 : 
				MidwareConstants.DEFAULT_RING_QUEUE_4_NOMESSAGE);
    }
    
    public String getQueueFile4(boolean leaveMessage, boolean offline) {
        String file;
        if(leaveMessage) {
            file = offline ? MidwareConstants.DEFAULT_RING_QUEUE_4_OFFLINE : MidwareConstants.DEFAULT_RING_QUEUE_4;
        } else {
            file = offline ? MidwareConstants.DEFAULT_RING_QUEUE_4_OFFLINE_NOMESSAGE : MidwareConstants.DEFAULT_RING_QUEUE_4_NOMESSAGE;
        }
        return this.settings.getDefaultRingFile(file);
    }
	
	public String getQueueKeyErrorFile() {
        return this.settings.getDefaultRingFile(MidwareConstants.DEFAULT_RING_QUEUE_KEY_ERROR);
    }
	
	public String getCustomQueueFile4(String number) {
        return this.settings.getDefaultRingFile("queue_" + number + ".wav");
    }

    public String getQueueEndFile(boolean leaveMessage) {
        return this.settings.getDefaultRingFile(leaveMessage ? MidwareConstants.DEFAULT_RING_QUEUEEND1
            : MidwareConstants.DEFAULT_RING_QUEUEEND2);
    }

    public String getQueueTipFile(boolean leaveMessage) {
        return this.settings.getDefaultRingFile(leaveMessage ? MidwareConstants.DEFAULT_RING_QUEUETIP_VOICEBOX : MidwareConstants.DEFAULT_RING_QUEUETIP);
    }

    public String getInputErrorFile() {
        return this.settings.getDefaultRingFile(MidwareConstants.DEFAULT_RING_INPUTERROR);
    }
    
    public String getIvrErrorQuitFile() {
        return this.settings.getDefaultRingFile(MidwareConstants.DEFAULT_RING_IVRERRORQUIT);
    }

    public String getTimeOutInputErrorFile() {
        return this.settings.getDefaultRingFile(MidwareConstants.DEFAULT_RING_TIMEOUT_INPUTERROR);
    }

    public VoiceBox startVoicebox(String callerNo, String customerNo, Dept dept) {
        Date now = new Date();
        VoiceBox vb = new VoiceBox();
        vb.setCallerNo(callerNo);
        vb.setCustomerNo(customerNo);
        vb.setDeptLsh(dept != null ? dept.getDeptLsh() : customerNo);
        vb.setStartTime(now);
        String fileName, filePath;
        if(this.isSipFlag()) {
            fileName = "boxes_" + callerNo + "_" + System.currentTimeMillis() + ".wav";
            filePath = this.getSipVoiceBoxPath() + "/storage/recordings/" + fileName;
        } else {
            fileName = callerNo + "_" + System.currentTimeMillis() + ".wav";
            filePath = this.settings.getBoxPath() + "/" + DateTimeUtil.formatShortDate(now) + "/" + customerNo;
            File file = new File(filePath);
            if (!file.exists() || !file.isDirectory()) {
                file.mkdirs();
            }
            filePath += "/" + fileName;
        }
        vb.setFileName(fileName);
        vb.setFilePath(filePath);
        return vb;
    }

    public void saveOrUpdateVoiceBox(final VoiceBox vb) {
        this.es.submit(() -> {
            ProcessService.this.businessService.saveOrUpdateVoiceBox(vb);
        });
    }

    public void endVoiceBox(final TalkNote talkNote, VoiceBox vb) {
        if (vb.isEndFlag()) {
            vb.setInterval(DateTimeUtil.getTimeDifference(vb.getStartTime(), vb.getEndTime()));
        }
        final VoiceBox box = vb;
        this.es.submit(() -> {
            if (talkNote.getId() == null) {
                try {
                    ProcessService.this.businessService.saveOrUpdate(talkNote);
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }
            }
            if (box.getTalkNoteId() == null) {
                box.setTalkNoteId(talkNote.getId());
            }
            ProcessService.this.businessService.saveOrUpdateVoiceBox(box);
            ProcessService.this.businessService.saveTalkTask(new OutDataTask(talkNote.getId(), OutDataType.VOICE_BOX));
        });
    }

    public VoiceRecord startRecord(User user, BindPhone bindPhone, String caller, String callee, boolean jmyFlag) {
        String prefix = "";
        if (this.baseService.isNotRecordConvertWhiteList(user.getNumber())) {
            log.debug("{} 开启用户监听！", user.getNumber());
            prefix = MONITOR_PREFIX;
        }
        Date now = new Date();
        VoiceRecord record = new VoiceRecord();
        record.setCalleeNo(callee);
        record.setCallerNo(caller);
        record.setStartTime(now);
        record.setCustomerNo(user.getNumber());
        String filePath = this.settings.getRecordPath() + "/" + DateTimeUtil.formatShortDate(now) + "/"
            + user.getNumber();
        File file = new File(filePath);
        if (!file.exists() || !file.isDirectory()) {
            file.mkdirs();
        }
        Long timestamp = System.currentTimeMillis();
        String fileName = null;
        String recordFilePath = null;
        if(jmyFlag && !this.isSipFlag()) {
            String fileNameLeft = prefix + caller + "_" + timestamp + "_L.wav";
            String fileNameRight = prefix + caller + "_" + timestamp + "_R.wav";
            fileName = fileNameLeft + "," + fileNameRight;
            String filePathLeft = filePath + "/" + fileNameLeft;
            String filePathRight = filePath + "/" + fileNameRight;
            recordFilePath = filePathLeft + "," + filePathRight;
        } else {
            fileName = prefix + caller + "_" + timestamp + ".wav";
            recordFilePath = filePath + "/" + fileName;
        }
        record.setFileName(fileName);
        record.setFilePath(recordFilePath);
        return record;
    }

    public void saveOrUpdateRecord(final VoiceRecord vr) {
        this.es.submit(() -> {
            ProcessService.this.businessService.saveOrUpdateVoiceRecord(vr);
        });
    }

    public void endVoiceScore(final TalkNote talkNote, VoiceScore vs) {
        vs.setEndTime(new Date());
        final VoiceScore score = vs;
        this.es
            .submit(() -> {
                if (talkNote.getId() == null) {
                    try {
                        ProcessService.this.businessService.saveOrUpdate(talkNote);
                    } catch (Exception ex) {
                        log.error(ex.getMessage(), ex);
                    }
                }
                if (score.getTalkNoteId() == null) {
                    score.setTalkNoteId(talkNote.getId());
                }
                ProcessService.this.businessService.saveOrUpdateVoiceScore(score);
                ProcessService.this.businessService.saveTalkTask(new OutDataTask(score.getTalkNoteId(),
                    OutDataType.VOICE_SCORE));
            });
    }

    public void endRecord(final TalkNote talkNote, VoiceRecord vr, final boolean transMp3) {
        endRecord(talkNote, vr, transMp3, false);
    }
    
    public void endRecord(final TalkNote talkNote, VoiceRecord vr, final boolean transMp3, final boolean jmyFlag) {
        if (vr.isEndFlag()) {
            vr.setInterval(DateTimeUtil.getTimeDifference(vr.getStartTime(), vr.getEndTime()));
        }
        final VoiceRecord record = vr;
        this.es.submit(() -> {
            if (talkNote.getId() == null) {
                try {
                    ProcessService.this.businessService.saveOrUpdate(talkNote);
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }
            }
            if (record.getTalkNoteId() == null) {
                record.setTalkNoteId(talkNote.getId());
            }
            if(jmyFlag && !this.isSipFlag()) {
                try {
                    Thread.sleep(300);
                } catch (Exception e) {
                }
                log.info("基木鱼合成双声道录音：{}", record.getFilePath());
                String[] filePathArr=record.getFilePath().split(",");
                String filePath = filePathArr[0].substring(0,filePathArr[0].lastIndexOf("/"));
                String prefix = record.getFileName().startsWith(MONITOR_PREFIX) ? MONITOR_PREFIX : "";
                String complexFileName = prefix + vr.getCallerNo() + "_" + vr.getStartTime().getTime() + "_LR.mp3";
                String complexFilePath = filePath + "/" + complexFileName;
                
                VoiceUtil.wavToMp3(filePathArr[0], filePathArr[1], complexFilePath);
                record.setFileName(complexFileName);
                record.setFilePath(complexFilePath);
            } else if(transMp3) {
                try {
                    Thread.sleep(300);
                } catch (Exception e) {
                }
				log.info("执行录音转换:{}",record.getFilePath());
				String mp3Path = record.getFilePath().replace("wav", "mp3");
				VoiceUtil.wavToMp3(record.getFilePath(), mp3Path);
			} else {
				log.info("此号码配置了录音文件不转换MP3！");
			}
            ProcessService.this.businessService.saveOrUpdateVoiceRecord(record);
            ProcessService.this.businessService.saveTalkTask(new OutDataTask(talkNote.getId(), OutDataType.VOICE));
        });
    }

    @Transactional
    public List<BindPhone> getIdleAgentsByDept(Dept dept, String callerNo) {
        if (this.commonSettings.isMainServer()) {
            return this.baseService.getIdleAgent(dept, callerNo);
        } else {
            log.debug("Rmi 接口调用查询空闲坐席");
            List<BindPhone> list = this.rmiService.getIdleAgent(dept.getCustomerNo(), dept.getDeptLsh(), callerNo);
            log.debug("Rmi 接口调用查询空闲坐席返回");
            return list;
        }
    }

    public List<BindPhone> getAllAgentsByDept(Dept dept, String callerNo) {
        return this.baseService.getAllAgentsByDept(dept, callerNo);
    }
    
    public String getAgentState(BindPhone bindPhone) {
        if (this.commonSettings.isMainServer()) {
            return this.baseService.getAgentStatus(bindPhone.getId());
        } else {
            log.debug("Rmi 接口调用获取坐席状态");
            String s = this.rmiService.getAgentStatus(bindPhone.getCustomerNo(), bindPhone.getLsh());
            log.debug("Rmi 接口调用获取坐席状态返回");
            return s;
        }
    }

    public String getAgentStateByAgentId(String agentId) {
        if (this.commonSettings.isMainServer()) {
            return this.baseService.getAgentStatusByAgentId(agentId);
        } else {
            log.debug("Rmi 接口调用获取坐席状态");
            String s = this.rmiService.getAgentStatusByAgentId(agentId);
            log.debug("Rmi 接口调用获取坐席状态返回");
            return s;
        }
    }

    public void sortAgents(Dept dept, List<BindPhone> agents) {
        if (dept.getCallModle() == null) {
            log.warn("部门：{} 没有配置接听策略！", dept.getDeptLsh());
            return;
        }
        switch (dept.getCallModle()) {
            case 1:
                //随机
                Collections.shuffle(agents);
                break;
            case 2:
                //平均
                Collections.sort(agents, (BindPhone o1, BindPhone o2) -> o1.getCallnum() - o2.getCallnum());
                break;
            case 3:
                //轮训
                String last = getDeptLastBindPhone(dept);
                log.debug("轮询策略，上次接听的绑定号码流水号：{}", last);
                if (last != null) {
                    BindPhone lastBindPhone = this.getLocalBindPhone(dept.getCustomerNo(), last);
                    if (lastBindPhone != null) {
                        Long lastOrderBy = lastBindPhone.getOrderBy();
                        log.debug("轮询策略，上次接听的绑定号码 orderBy：{}", lastOrderBy);
                        List<BindPhone> sortList = new ArrayList(agents.size());
                        int index = 0;
                        for (int i = 0; i < agents.size(); i++) {
                            if (agents.get(i).getOrderBy() > lastOrderBy) {
                                index = i;
                                log.debug("找到大于上次接听排序号的坐席，此坐席将排首位！：{}", agents.get(i));
                                break;
                            }
                        }
                        for (int i = index; i < agents.size(); i++) {
                            sortList.add(agents.get(i));
                        }
                        for (int i = 0; i < index; i++) {
                            sortList.add(agents.get(i));
                        }
                        agents.clear();
                        agents.addAll(sortList);
                        sortList.clear();
                    }
                }
                break;
            case 4:
                //权重模式
                log.debug("权重策略，根据接通数/权重值排序");
                List<BindPhone> ratioList = agents.stream().filter(b -> b.getRatioIntValue() > 0).sorted((BindPhone o1, BindPhone o2) -> {
                    if (o1.getWeightValue().doubleValue() == o2.getWeightValue().doubleValue()) {
                        return o2.getRatioIntValue() - o1.getRatioIntValue();
                    }
                    return o1.getWeightValue().compareTo(o2.getWeightValue());
                }).collect(Collectors.toList());
                
                List<BindPhone> emptyRatioList = agents.stream().filter(b -> b.getRatioIntValue() == 0).sorted((BindPhone o1, BindPhone o2) -> {
                    return o1.getCallnum() - o2.getCallnum();
                }).collect(Collectors.toList());
                agents.clear();
                agents.addAll(ratioList);
                agents.addAll(emptyRatioList);
                ratioList.clear();
                emptyRatioList.clear();
                break;
            case 5:
                log.debug("等级策略，根据接听等级排序，值越小级别越高，级别相同则随机排序");
                agents.sort((BindPhone o1, BindPhone o2) ->{
                     int a = o1.getPhoneLevel();
                     int b = o2.getPhoneLevel();
                     if(a != b) {
                         return a - b;
                     } else {
                         return o1.getRandomChar().compareTo(o2.getRandomChar());
                     }
                });
                break;
//                Collections.sort(agents, Comparator.comparingInt(BindPhone::getPhoneLevel));
            default:
                break;
        }
    }
    
    public List<BindPhone> judgeMemoryFlag(User user, Dept dept, List<BindPhone> agents, String mainCall){
    	if(agents.isEmpty()){
    		return agents;
    	}
    	if(!user.isMemoryFlag()){
    		log.debug("号码:{},部门流水号:{},未开通记忆功能",dept.getCustomerNo(),dept.getDeptLsh());
    		return agents;
    	}
    	List<TalkNoteDto> list=baseService.findByDeptAndMainCall(dept, mainCall);
    	final String[] backEndpoints = this.commonSettings.getBackupServers();
    	if(backEndpoints.length>0){
    		//获取备份机器的主叫来电数据
			for (String endpoint : backEndpoints) {
				if(!endpoint.startsWith("http")){
					continue;
				}
				String data =HttpHelper.SendPostRequest(endpoint + "getOffHookCallee/" + dept.getCustomerNo() + "/" + dept.getDeptLsh()+ "/" + mainCall, null);
				list.addAll(JsonHelper.json2List(data, TalkNoteDto.class));
			}
        } 
    	if(list.isEmpty()){
    		log.debug("号码:{},部门流水号:{},来电号码:{}:未找到时间段内历史呼入话单",
    				dept.getCustomerNo(),dept.getDeptLsh(),mainCall);
    		return agents;
    	}
    	log.debug("排序前的集合:{}",list);
    	//按呼入时间排序，取出最近的来电
    	Collections.sort(list);
    	log.debug("排序后的集合:{}",list);
    	String bindPhoneNo=list.get(0).getCalleeNo();
    	log.debug("记忆功能安排呼叫的坐席绑定号码:{}",bindPhoneNo);
//		Optional<BindPhone> find = agents.stream().filter(phone -> phone.getBindPhoneNo().equals(bindPhoneNo)).findFirst();
//    	if(find.isPresent()){
//    		agents.set(0, find.get());
//    	}
    	BindPhone filterBindPhone=null;
    	for(BindPhone phone:agents){
    		if(phone.getOrigBindPhoneNo().equals(bindPhoneNo)){
    			filterBindPhone=phone;
    			break;
    		}
    	}
    	if(filterBindPhone!=null){
    		List<BindPhone> convertAgents=new ArrayList<BindPhone>();
    		convertAgents.add(filterBindPhone);
    		for(BindPhone phone:agents){
    			if(!phone.getOrigBindPhoneNo().equals(bindPhoneNo)){
    				convertAgents.add(phone);
    			}
    		}
    		log.debug("agents:{},convertAgents:{}",agents,convertAgents);
    		return convertAgents;
//    		agents=convertAgents;
//    		agents.add(0, filterBindPhone);
    	}
    	return agents;
    }

    @Transactional
    public void screenRequest(String talkNoteId, BindPhone phone, String callin, String callout, String districtDesc, String ivrKey,String callee,long incomingTime) {
        String agentId = phone.getAgentId();
        //        StringBuilder sb = new StringBuilder();
        //        sb.append(this.settings.getPlatformEndpoint()).append(agentId == null ? "callIncome" : "callIncomeNew")
        //            .append("/").append(phone.getCustomerNo()).append("/").append(agentId == null ? phone.getLsh() : agentId)
        //            .append("/").append(callin).append("/").append(callout);
        //        final String url = sb.toString();
        //        log.info("来电弹屏请求地址：" + url);
        //        Map<String, String> param = new HashMap();
        //        param.put("talkId", talkNoteId);
        //        param.put("gh", phone.getReportNum());
        SocketMessage message = new CallincomeMessage(phone.getCustomerNo(), agentId, callin, talkNoteId,
                districtDesc, phone.getReportNum(), ivrKey, callee, incomingTime);
        HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendCallinMessage", message);
        //        this.asyncRequestPlatform(url, param);

    }
    
    public String getDistrictDesc(String caller) {
        return this.baseService.getDistrictDesc(caller);
    }

    public int getMakeCallRetryTime() {
        return this.settings.getMakecallRetrySeconds();
    }

    public void notifyQueueAgentOnhook(Dept dept) {
        if (this.commonSettings.isMainServer()) {
            this.businessService.MessageAgentOnHook(dept.getId());
        } else {
            log.debug("Rmi 接口通知坐席挂机");
            this.rmiService.MessageAgentOnHook(dept.getCustomerNo(), dept.getDeptLsh());
            log.debug("Rmi 接口通知坐席挂机返回");
        }
    }

    public void notifyQueueAgentOnhook(BindPhone bindPhone) {
        if (this.commonSettings.isMainServer()) {
            this.businessService.notifyQueueAgentOnhook(bindPhone.getCustomerNo(), bindPhone.getAgentId());
        } else {
            log.debug("Rmi 接口通知坐席挂机");
            this.rmiService.notifyQueueAgentOnhook(bindPhone.getCustomerNo(), bindPhone.getAgentId());
            log.debug("Rmi 接口通知坐席挂机返回");
        }
    }

    @Transactional
    public void midwareUpdateAgentState(String bindPhoneId, String state, boolean sendMessage) {
        this.midwareUpdateAgentState(this.baseService.getBindPhone(bindPhoneId), state, sendMessage);
    }

    @Transactional
    public int midwareUpdateAgentState(BindPhone bindPhone, String state, boolean sendMessage) {
        return midwareUpdateAgentState(bindPhone, state, sendMessage,true);
    }
    
    @Transactional
    public int midwareUpdateAgentState(BindPhone bindPhone, String state, boolean sendMessage, boolean multipleFlag) {
        int count;
        if (this.commonSettings.isMainServer()) {
            count = this.baseService.modifyAgentStatus(bindPhone.getId(), state);
        } else {
            log.debug("Rmi 接口调用更新坐席状态");
            count =this.rmiService.modifyAgentStatus(bindPhone.getCustomerNo(), bindPhone.getLsh(), state);
            log.debug("Rmi 接口调用更新坐席状态返回");
        }
        if(count == 0) {
            return count;
        }
        if (sendMessage) {
            log.debug("运营点更新坐席状态：{}，发送socket消息通知中心端。", state);
            SocketMessage message = new AgentMessage(bindPhone.getCustomerNo(), bindPhone.getAgentId(),
                bindPhone.getLsh(), state, bindPhone.getOrigBindPhoneNo(), bindPhone.isNeedLogin());
            HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendAgentMessage", message);
        }
        if (MidwareConstants.AGENT_STATE_IDLE.equals(state)) {
            log.debug("运营点更新坐席状态变闲，通知排队队列");
            if (StringUtils.isNotBlank(bindPhone.getAgentId())) {
                //				List<BindPhone> bps = this.baseService.getBindPhoneByAgentId(bindPhone.getCustomerNo(), bindPhone.getAgentId());
                //				for(BindPhone bp : bps) {
                //					this.notifyQueueAgentOnhook(bp.getDept());
                //				}
                this.notifyQueueAgentOnhook(bindPhone);
            } else {
                this.notifyQueueAgentOnhook(bindPhone.getDept());
            }
        }
//        if(multipleFlag) {
//            updateMultipleAgentState(user, bindPhone.getBindPhoneNo(), state);
//        }
        return count;
    }
    
//    @Transactional
//    public void updateMultipleAgentState(User user, String bindPhone, String state) {
//        if(StringUtils.isEmpty(user.getUniqueName())) {
//            return;
//        }
//        List<BindPhone> multiplePhone = this.businessService.findMultipleBindPhone(bindPhone, user.getUniqueName(), user.getNumber());
//        multiplePhone.forEach(bp -> {
//            if(state.startsWith(MidwareConstants.AGENT_STATE_CALLING) && bp.getStatus().startsWith(MidwareConstants.AGENT_STATE_CALLING)) {
//                log.warn("同步的状态为呼叫中，多号的状态为呼叫中，忽略同步");
//            } else {
//                User multipleUser = this.baseService.getUserByNumber(bp.getCustomerNo());
//                log.debug("更新多号相同的绑定号码状态，号码：{}，状态：{}，推送标识：{}", multipleUser.getNumber(), state, multipleUser.getPushStatusFlag());
//                midwareUpdateAgentState(bp, state, multipleUser.getPushStatusFlag(), false);
//            }
//        });
//    }
    
    @Transactional(readOnly = true)
    public List<BindPhone> findMultipleBindPhone(String bindPhone, String uniqueName, String excludeNo) {
        return this.businessService.findMultipleBindPhone(bindPhone, uniqueName, excludeNo);
    }

    @Transactional
    public void midwareUpdateAgentStateByAgentId(BindPhone bindPhone, String state, boolean sendMessage) {
        if (this.commonSettings.isMainServer()) {
            this.baseService.modifyAgentStatusByAgentId(bindPhone.getCustomerNo(), bindPhone.getAgentId(), state);
        } else {
            log.debug("Rmi 接口调用更新坐席状态");
            this.rmiService.modifyAgentStatusByAgentId(bindPhone.getCustomerNo(), bindPhone.getAgentId(), state);
            log.debug("Rmi 接口调用更新坐席状态返回");
        }
        if (sendMessage) {
            log.debug("运营点更新坐席状态：{}，发送socket消息通知中心端。", state);
            SocketMessage message = new AgentMessage(bindPhone.getCustomerNo(), bindPhone.getAgentId(),
                bindPhone.getLsh(), state, bindPhone.getOrigBindPhoneNo(), bindPhone.isNeedLogin());
            HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendAgentMessage", message);
        }
        if (MidwareConstants.AGENT_STATE_IDLE.equals(state)) {
            log.debug("运营点更新坐席状态变闲，通知排队队列");
            if (StringUtils.isNotBlank(bindPhone.getAgentId())) {
                this.notifyQueueAgentOnhook(bindPhone);
            } else {
                this.notifyQueueAgentOnhook(bindPhone.getDept());
            }
        }
    }

    @Transactional
    public int midwareUpdateAgentStateAndNeedLogin(BindPhone bindPhone, String state, boolean isNeedLogin) {
        return midwareUpdateAgentStateAndNeedLogin(bindPhone, state, isNeedLogin, true);
    }
    
    @Transactional
    public int midwareUpdateAgentStateAndNeedLogin(BindPhone bindPhone, String state, boolean isNeedLogin, boolean multipleFlag) {
        int count = 0;
        if (this.commonSettings.isMainServer()) {
            count = this.baseService.modifyAgentStatusAndNeedlogin(bindPhone.getId(), state, isNeedLogin);
        } else {
            log.debug("Rmi 接口调用更新坐席状态");
            count = this.rmiService.modifyAgentStatusAndNeedlogin(bindPhone.getCustomerNo(), bindPhone.getLsh(), state,
                isNeedLogin);
            log.debug("Rmi 接口调用更新坐席状态返回");
        }
        User user = this.baseService.getUserByNumber(bindPhone.getCustomerNo());
        boolean sendMessage = user.getPushStatusFlag();
        log.debug("{} 发送消息标识：{}", user.getNumber(), sendMessage);
        if (sendMessage) {
            SocketMessage message = new AgentMessage(bindPhone.getCustomerNo(), bindPhone.getAgentId(),
                bindPhone.getLsh(), state, bindPhone.getOrigBindPhoneNo(), isNeedLogin);
            log.debug("新用户平台发送Http消息：{}", message);
            HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendAgentMessage", message);
        }
        if (MidwareConstants.AGENT_STATE_IDLE.equals(state)) {
            this.notifyQueueAgentOnhook(bindPhone);
        }
        return count;
//        if(multipleFlag) {
//            updateMultipleAgentStateAndNeedLogin(user, bindPhone.getBindPhoneNo(), state, isNeedLogin);
//        }
    }
    
    @Transactional
    public int midwareBatchUpdateAgentStateAndNeedLogin(BindPhone bindPhone, String state, boolean isNeedLogin, String uniqueName) {
        int count;
        if (this.commonSettings.isMainServer()) {
            count = this.baseService.modifyBatchAgentStatusAndNeedlogin(bindPhone.getId(), state, isNeedLogin, uniqueName);
        } else {
            log.debug("Rmi 接口调用更新坐席状态");
            count = this.rmiService.modifyBatchAgentStatusAndNeedlogin(bindPhone.getCustomerNo(), bindPhone.getLsh(), state, isNeedLogin, uniqueName);
            log.debug("Rmi 接口调用更新坐席状态返回");
        }
        log.info("多号批量更新坐席状态及登录标识，条数：{}", count);
        if(count == 0) {
            log.warn("多号批量更新坐席状态及登录标识失败！");
            return 0;
        }
        User user = this.baseService.getUserByNumber(bindPhone.getCustomerNo());
        boolean sendMessage = user.getPushStatusFlag();
        ConcurrentHelper.doInBackground(() -> {
            List<BindPhone> multiplePhone = this.businessService.findMultipleBindPhone(bindPhone.getBindPhoneNo(), uniqueName);
            multiplePhone.forEach(bp -> {
                //发送多号状态消息
                if (sendMessage) {
                    SocketMessage message = new AgentMessage(bp.getCustomerNo(), bp.getAgentId(), 
                            bp.getLsh(), state, bp.getOrigBindPhoneNo(), isNeedLogin);
                    log.debug("新用户平台发送Http消息：{}", message);
                    HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendAgentMessage", message);
                }    
                if (MidwareConstants.AGENT_STATE_IDLE.equals(state)) {
                    this.notifyQueueAgentOnhook(bp);
                }
            });
        });
        return count;
    }
    
//    @Transactional
//    public void updateMultipleAgentStateAndNeedLogin(User user, String bindPhone, String state, boolean isNeedLogin) {
//        if(StringUtils.isEmpty(user.getUniqueName())) {
//            return;
//        }
//        List<BindPhone> multiplePhone = this.businessService.findMultipleBindPhone(bindPhone, user.getUniqueName(), user.getNumber());
//        multiplePhone.forEach(bp -> {
//            log.debug("更新多号相同的绑定号码状态，号码：{}，状态：{}", bp.getCustomerNo(), state);
//            midwareUpdateAgentStateAndNeedLogin(bp, state, isNeedLogin, false);
//        });
//    }

    public void midwareUpdateAgentNeedLogin(BindPhone bindPhone, boolean isNeedLogin) {
        if (this.commonSettings.isMainServer()) {
            this.baseService.modifyAgentNeedlogin(bindPhone.getId(), isNeedLogin);
        } else {
            log.debug("Rmi 接口调用更新坐席状态");
            this.rmiService.modifyAgentNeedlogin(bindPhone.getCustomerNo(), bindPhone.getLsh(), isNeedLogin);
            log.debug("Rmi 接口调用更新坐席状态返回");
        }
    }
    
    public void midwareBatchUpdateAgentNeedLogin(BindPhone bindPhone, boolean isNeedLogin, String uniqueName) {
        if (this.commonSettings.isMainServer()) {
            this.baseService.modifyBatchAgentNeedlogin(bindPhone.getId(), isNeedLogin, uniqueName);
        } else {
            log.debug("Rmi 接口调用批量更新坐席状态");
            this.rmiService.modifyBatchAgentNeedlogin(bindPhone.getCustomerNo(), bindPhone.getLsh(), isNeedLogin, uniqueName);
            log.debug("Rmi 接口调用批量更新坐席状态返回");
        }
    }

    public void controllerUpdateAgentState(String customerNo, String lsh, String state) {
        log.info("客户:{},坐席流水号:{}:中心端手动更新状态:{}", customerNo, lsh, state);
        BindPhone bindPhone = this.baseService.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
        boolean sendMessage = this.baseService.getUserByNumber(customerNo).getPushStatusFlag();
        User user = this.getUserByNumber(customerNo);
        if(StringUtils.isNotEmpty(user.getUniqueName())) {
            this.midwareBatchUpdateAgentState(bindPhone, state, sendMessage, user.getUniqueName());
        } else {
            this.midwareUpdateAgentState(bindPhone, state, sendMessage);
        }
    }

    public void controllerUpdateAgentNeedlogin(String customerNo, String lsh, boolean isNeedlogin) {
        BindPhone bindPhone = this.baseService.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
        User user = this.getUserByNumber(customerNo);
        if(StringUtils.isNotEmpty(user.getUniqueName())) {
            this.midwareBatchUpdateAgentNeedLogin(bindPhone, isNeedlogin, user.getUniqueName());
        } else {
            this.midwareUpdateAgentNeedLogin(bindPhone, isNeedlogin);
        }
        
    }

    public int controllerUpdateAgentStateAndNeedlogin(String customerNo, String lsh, String state, boolean isNeedlogin) {
        BindPhone bindPhone = this.baseService.getBindPhoneByCustomerNoAndLsh(customerNo, lsh);
        User user = this.getUserByNumber(customerNo);
        if(StringUtils.isNotEmpty(user.getUniqueName())) {
            return this.midwareBatchUpdateAgentStateAndNeedLogin(bindPhone, state, isNeedlogin, user.getUniqueName());
        } else {
            return this.midwareUpdateAgentStateAndNeedLogin(bindPhone, state, isNeedlogin);
        }
    }
    
    public String getQueueInfo(Dept dept) {
        if (this.commonSettings.isMainServer()) {
            return this.businessService.getQueueInfo(dept.getId());
        } else {
            log.debug("Rmi 接口调用获取队列信息");
            String s = this.rmiService.getQueueInfo(dept.getCustomerNo(), dept.getDeptLsh());
            log.debug("Rmi 接口调用获取队列信息返回");
            return s;
        }
    }

    public int getQueueSize(Dept dept) {
        if (this.commonSettings.isMainServer()) {
            return this.businessService.getQueueSizeByDept(dept.getId());
        } else {
            log.debug("Rmi 接口调用获取队列大小");
            int i = this.rmiService.getQueueSizeByDept(dept.getCustomerNo(), dept.getDeptLsh());
            log.debug("Rmi 接口调用获取队列大小");
            return i;
        }
    }

    @Transactional
    public void addToQueue(Dept dept, String callId) {
        int count = 0;
        if (this.commonSettings.isMainServer()) {
            count = this.businessService.addToQueue(dept.getId(), callId);
        } else {
            log.debug("Rmi 接口调用添加队列");
            count = this.rmiService.addToQueue(dept.getCustomerNo(), dept.getDeptLsh(), callId);
            log.debug("Rmi 接口调用添加队列返回");
        }
        SocketMessage message = new DeptMessage(dept.getCustomerNo(), dept.getDeptLsh(), count + "");
        HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendDeptMessage", message);
    }
    
    @Transactional
    public void reEnterQueue(Dept dept, String callId) {
        if (this.commonSettings.isMainServer()) {
            this.businessService.updateQueueState(dept.getId(), callId, BusinessConstants.QUEUE_WAIT);
        } else {
            log.debug("Rmi 接口调用重入队列");
            this.rmiService.updateQueueState(dept.getCustomerNo(), dept.getDeptLsh(), callId, BusinessConstants.QUEUE_WAIT);
            log.debug("Rmi 接口调用重入队列返回");
        }
    }
    
    @Transactional
    public boolean queueExist(Dept dept, String callId) {
        if(dept == null) {
            return false;
        }
        if (this.commonSettings.isMainServer()) {
            return this.businessService.queueExist(dept.getId(), callId);
        } else {
            log.debug("Rmi 接口调用退出队列");
            boolean b = this.rmiService.queueExist(dept.getCustomerNo(), dept.getDeptLsh(), callId);
            log.debug("Rmi 接口调用退出队列返回");
            return b;
        }
    }

    public int[] getQueueStatus(Dept dept, String callId) {
        if (this.commonSettings.isMainServer()) {
            return this.businessService.getQueueStatus(dept.getId(), callId);
        } else {
            log.debug("Rmi 接口调用获取队列状态");
            int[] i = this.rmiService.getQueueStatus(dept.getCustomerNo(), dept.getDeptLsh(), callId);
            log.debug("Rmi 接口调用获取队列状态返回");
            return i;
        }
    }
    
    public int getQueueIndex(Dept dept, String callId) {
        if (this.commonSettings.isMainServer()) {
            return this.businessService.getQueueIndex(dept.getId(), callId);
        } else {
            log.debug("Rmi 接口调用获取队列状态");
            int i = this.rmiService.getQueueIndex(dept.getCustomerNo(), dept.getDeptLsh(), callId);
            log.debug("Rmi 接口调用获取队列状态返回");
            return i;
        }
    }

    @Transactional
    public void removeQueue(Dept dept, String callId, boolean spreadState) {
        int count = 0;
        if (this.commonSettings.isMainServer()) {
            count = this.businessService.removeQueue(dept.getId(), callId, spreadState);
        } else {
            log.debug("Rmi 接口调用移除队列");
            count = this.rmiService.removeQueue(dept.getCustomerNo(), dept.getDeptLsh(), callId, spreadState);
            log.debug("Rmi 接口调用移除队列返回");
        }
        SocketMessage message = new DeptMessage(dept.getCustomerNo(), dept.getDeptLsh(), count + "");
        HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendDeptMessage", message);
    }

    public int continueWait(Dept dept, String callId) {
        if (this.commonSettings.isMainServer()) {
            return this.businessService.continueWait(dept.getId(), callId);
        } else {
            log.debug("Rmi 接口调队列继续等待");
            int i = this.rmiService.continueWait(dept.getCustomerNo(), dept.getDeptLsh(), callId);
            log.debug("Rmi 接口调队列继续等待返回");
            return i;
        }
    }

    @Transactional
    public void calleeOnhook(final BindPhone bindPhone, final boolean isOffhook, final boolean isTransCall,
        final TalkNote talkNote, NumberFuncConfig config) {
        User user = this.baseService.getUserByNumber(bindPhone.getCustomerNo());
        final boolean sendMessage = user.getPushStatusFlag();
        final Integer finishingTime = user.getFinishingTime();
        if(sendMessage || (config != null && config.isInteractFlag())) {
            //发送挂机消息
            log.debug("{} 发送挂机消息", user.getNumber());
            SocketMessage message = new CalleeOnhookMessage(bindPhone.getCustomerNo(), bindPhone.getAgentId(),
                bindPhone.getReportNum(), talkNote.getId(), (isTransCall ? "1" : "0"));
            HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendOnhookMessage", message);
        }
        if (bindPhone.isNeedLogin() || !isOffhook || finishingTime <= 1 || user.isSetAcsType()) {
            //坐席未登录，或者未呼通，未开整理功能，直接更新数据库为空闲状态
            String state = user.isSetAcsType() ? MidwareConstants.AGENT_STATE_BUSY : MidwareConstants.AGENT_STATE_IDLE;
            log.debug("{} 坐席未登录，或者未呼通，未开整理功能，直接更新数据库为{}状态", user.getNumber(), state);
            if(StringUtils.isNotEmpty(user.getUniqueName())) {
                this.midwareBatchUpdateAgentState(bindPhone, state, sendMessage, user.getUniqueName());
            } else {
                this.midwareUpdateAgentState(bindPhone, state, sendMessage);
            }
        } else {
            if(user.isPlatformPlus()) {
                //新用户平台,进入整理状态
                if(StringUtils.isNotEmpty(user.getUniqueName())) {
                    this.midwareBatchUpdateAgentState(bindPhone, MidwareConstants.AGENT_STATE_ORDER + "_" + finishingTime, sendMessage, user.getUniqueName());
                } else {
                    this.midwareUpdateAgentState(bindPhone, MidwareConstants.AGENT_STATE_ORDER + "_" + finishingTime, sendMessage);
                }
                log.debug("{} 用户开通整理功能：{}秒，添加整理任务！", user.getNumber(), finishingTime);
                Future future = this.ses.schedule(new OrderFinishTask(user.getNumber(), bindPhone.getId(), finishingTime, this, bindPhone.getAgentId()), finishingTime, TimeUnit.SECONDS);
                this.putOrder(user.getNumber() + bindPhone.getAgentId(), future);
            } else {
                //老用户平台
                //中心端进入整理状态，中间件不将坐席置闲
                log.info("添加保护缓存，防止中心端一直进整理状态！key={}", bindPhone.getId());
                CacheUtil.put(CacheUtil.CACHE_AGENT_STATE_NAME, bindPhone.getId(), bindPhone.getCustomerNo());
            }
        }
    }

    @Transactional
    public void calleeOnhook(final BindPhone bindPhone, final TalkNote talkNote, NumberFuncConfig config) {
        this.calleeOnhook(bindPhone, true, true, talkNote, config);
    }

    @Transactional
    public void callConnected(BindPhone bindPhone, TalkNote talkNote, Dept currentDept, boolean pushFlag, boolean isTransCall) {
        User user = this.baseService.getUserByNumber(bindPhone.getCustomerNo());
        if(StringUtils.isNotEmpty(user.getUniqueName())) {
            multipleCallConnected(bindPhone, talkNote, currentDept, pushFlag, isTransCall, user.getUniqueName());
        } else {
            callConnected(bindPhone, talkNote, currentDept, pushFlag, isTransCall, true);
        }
    }
    
    @Transactional
    public void callConnected(BindPhone bindPhone, TalkNote talkNote, Dept currentDept, boolean pushFlag, boolean isTransCall, boolean multipleFlag) {
        int count = 0;
		if (this.commonSettings.isMainServer()) {
            count = this.baseService.modifyAgentStatus(bindPhone.getId(), MidwareConstants.AGENT_STATE_CONNECT);
		} else {
            log.debug("Rmi 接口调用更新坐席状态");
			count = this.rmiService.modifyAgentStatus(bindPhone.getCustomerNo(), bindPhone.getLsh(),
				MidwareConstants.AGENT_STATE_CONNECT);
            log.debug("Rmi 接口调用更新坐席状态返回");
		}
		log.info("号码:{}, 更新connect状态条数：{}，pushFlag:{}", bindPhone.getCustomerNo(), count, pushFlag);
        if(count == 0) {
            log.warn("更新坐席状态connect失败！");
            return;
        }
		if (pushFlag) {
            try {
				long calleeOffhookTime = isTransCall ? (new Date().getTime()) : talkNote.getCalleeOffhookTime().getTime();
				SocketMessage message = new AgentMessage(bindPhone.getCustomerNo(), bindPhone.getAgentId(),
					bindPhone.getLsh(), AgentMessage.STATE_CONNECT, bindPhone.getOrigBindPhoneNo(), talkNote.getId(),
					calleeOffhookTime, talkNote.getIncomingTime().getTime(), bindPhone.getReportNum(),
					currentDept.getIvrKey(), bindPhone.isNeedLogin());
				HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendAgentMessage", message);
				log.info("号码:{}, sendMsg success:{}", bindPhone.getCustomerNo(), message);
			} catch (Exception e) {
				log.error("send connect msg error 号码:{}", bindPhone.getCustomerNo(), e);
			}
		}
//        if(multipleFlag) {
//            updateMultipleAgentState(user, bindPhone.getBindPhoneNo(), MidwareConstants.AGENT_STATE_BUSY);
//        }
    }
    
    @Transactional
    public void multipleCallConnected(BindPhone bindPhone, TalkNote talkNote, Dept currentDept, boolean pushFlag, boolean isTransCall, String uniqueName) {
        int count = 0;
		if (this.commonSettings.isMainServer()) {
            count = this.baseService.batchModifyAgentStatus(bindPhone.getId(), MidwareConstants.AGENT_STATE_CONNECT, uniqueName);
		} else {
            log.debug("Rmi 接口调用批量更新坐席状态");
			count = this.rmiService.batchModifyAgentStatus(bindPhone.getCustomerNo(), bindPhone.getLsh(), MidwareConstants.AGENT_STATE_CONNECT, uniqueName);
            log.debug("Rmi 接口调用批量更新坐席状态");
		}
		log.info("多号更新connect状态条数：{}，pushFlag:{}",  count, pushFlag);
        if(count == 0) {
            log.warn("多号更新坐席状态connect失败！");
            return;
        }
		if (pushFlag) {
            ConcurrentHelper.doInBackground(() -> {
                try {
                    List<BindPhone> multiplePhone = this.businessService.findMultipleBindPhone(bindPhone.getBindPhoneNo(), uniqueName);
                    multiplePhone.forEach(bp -> {
                        //发送多号状态消息
                        if (pushFlag) {
                            long calleeOffhookTime = isTransCall ? (new Date().getTime()) : talkNote.getCalleeOffhookTime().getTime();
                            SocketMessage message = new AgentMessage(bp.getCustomerNo(), bp.getAgentId(),
                                bp.getLsh(), AgentMessage.STATE_CONNECT, bp.getOrigBindPhoneNo(), talkNote.getId(),
                                calleeOffhookTime, talkNote.getIncomingTime().getTime(), bp.getReportNum(),
                                currentDept.getIvrKey(), bp.isNeedLogin());
                            HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendAgentMessage", message);
                            log.info("多号号码:{}, 绑定号码：{}，发送接通消息成功:{}", bp.getCustomerNo(), bp.getOrigBindPhoneNo(), message);
                        }    
                    });
                } catch (Exception e) {
                    log.error("多号发送接通消息失败，号码:{}", bindPhone.getCustomerNo(), e);
                }
            });
		}
    }
	
	public void callConnectedAsync(BindPhone bindPhone, TalkNote talkNote, Dept currentDept, boolean pushFlag, boolean isTransCall) {
		this.es.submit(()->{
			this.callConnected(bindPhone, talkNote, currentDept, pushFlag, isTransCall);
		});
	}

    public void addAgentCallNumAsync(BindPhone bindPhone) {
        this.es.submit(()->{
			this.addAgentCallNum(bindPhone);
		});
    }
    
    public void addAgentCallNum(BindPhone bindPhone) {
        if (this.commonSettings.isMainServer()) {
            this.baseService.modifyAgentCallNum(bindPhone.getId());
        } else {
            log.debug("Rmi 接口调用更新坐席接通次数");
            this.rmiService.modifyAgentCallNum(bindPhone.getCustomerNo(), bindPhone.getLsh());
            log.debug("Rmi 接口调用更新坐席接通次数返回");
        }
    }

    public String getAgentStatus(String customNo, String lsh, String agentId) {
        if (this.commonSettings.isMainServer()) {
            return this.baseService.getAgentStatus(customNo, agentId, lsh);
        } else {
            log.debug("Rmi 接口调用获取坐席状态");
            String s = this.rmiService.getAgentStatus(customNo, agentId, lsh);
            log.debug("Rmi 接口调用获取坐席状态返回");
            return s;
        }
    }

    public BindPhone getLocalBindPhone(String customNo, String lsh) {
        return this.baseService.getBindPhoneByCustomerNoAndLsh(customNo, lsh);
    }
    
    public Dept getLocalDept(String customNo, String lsh) {
        return this.baseService.getDeptByCustomerNoAndDeptLsh(customNo, lsh);
    }
    
    public BindPhone getBindPhoneByCustomerNoAndAgentId(String customNo, String agentId) {
        List<BindPhone> list = this.baseService.getBindPhoneByCustomerNoAndAgentId(customNo, agentId);
        return list.isEmpty() ? null : list.get(0);
    }

    public BindPhone getLocalBindPhone(String bindPhoneId) {
        return this.baseService.getBindPhone(bindPhoneId);
    }

    public void updateBindPhone(BindPhone bindPhone) {
        this.baseService.saveOrUpdateBindPhone(bindPhone);
    }

    public List<Object[]> getDeptQueueSize(String customerNo) {
        return this.businessService.getDeptQueueSize(customerNo);
    }

    public List<Object[]> getAllAgentState(String customerNo) {
        return this.baseService.getStatusByCustomerNo(customerNo);
    }

    public void missedCall(String caller, BindPhone bindPhone) {
        //        StringBuilder sb = new StringBuilder();
        //        sb.append(this.settings.getPlatformEndpoint()).append(this.settings.getServiceMissedCall()).append("/")
        //            .append(bindPhone.getCustomerNo()).append("/").append(bindPhone.getAgentId()).append("/")
        //            .append(bindPhone.getLsh()).append("/").append(caller);
        //        final String url = sb.toString();
        //        log.info("请求地址：" + url);
        //        this.asyncRequestPlatform(url, null);
    }

    public int getPbxCount(BindPhone bindPhone) {
        if (this.commonSettings.isMainServer()) {
            return this.businessService.getConnectCount(bindPhone.getId());
        } else {
            log.debug("Rmi 接口调用获取中继数量");
            int i = this.rmiService.getConnectCount(bindPhone.getCustomerNo(), bindPhone.getLsh());
            log.debug("Rmi 接口调用获取中继数量返回");
            return i;
        }
    }

    public void increasePbxCount(BindPhone bindPhone) {
        if (this.commonSettings.isMainServer()) {
            this.businessService.addConnect(bindPhone.getId());
        } else {
            log.debug("Rmi 接口调用增加中继数量");
            this.rmiService.addConnect(bindPhone.getCustomerNo(), bindPhone.getLsh());
            log.debug("Rmi 接口调用增加中继数量");
        }
    }

    public void decreasePbxCount(BindPhone bindPhone) {
        if (this.commonSettings.isMainServer()) {
            this.businessService.subtractConnect(bindPhone.getId());
        } else {
            log.debug("Rmi 接口调用减少中继数量");
            this.rmiService.subtractConnect(bindPhone.getCustomerNo(), bindPhone.getLsh());
            log.debug("Rmi 接口调用减少中继数量");
        }
    }
    
    public void decreaseGloablePbxCount(String customerNo, String bindPhone) {
        if (this.commonSettings.isMainServer()) {
            this.businessService.subtractGloableConnect(customerNo, bindPhone);
        } else {
            log.debug("Rmi 接口调用减少全局中继数量");
            this.rmiService.subtractConnect(customerNo, bindPhone);
            log.debug("Rmi 接口调用减少全局中继数量返回");
        }
    }

    public int getIvrRetryCount() {
        return this.settings.getIvrRetryCount();
    }

    public BindPhone getBindPhoneByCustomerNoAndReportNum(String customerNo, String gh) {
        if (this.commonSettings.isMainServer()) {
            return this.baseService.getBindPhoneByCustomerNoAndReportNum(customerNo, gh);
        } else {
            log.debug("Rmi 接口调用获取绑定号码");
            BindPhone b = this.rmiService.getBindPhoneByCustomerNoAndReportNum(customerNo, gh);
            log.debug("Rmi 接口调用获取绑定号码返回");
            return b;
        }
    }

    private void asyncRequestPlatform(final String url, final Map params) {
        this.asyncRequestPlatform(url, params, this.settings.getHttpRepeatCount());
    }

    private void asyncRequestPlatform(final String url, final Map params, final int repeatCount) {
        ConcurrentHelper.doInBackground(() -> {
            int count = 0;
            boolean needSend = true;
            while (needSend && count < repeatCount) {
                count++;
                try {
                    if (count > 1) {
                        TimeUnit.SECONDS.sleep(2);
                    }
                    log.info("第" + count + "次发送请求！");
                    HttpHelper.SendPostRequest(url, params, null, null, false);
                    needSend = false;
                } catch (Exception ex) {
                    log.error(ex.getMessage());
                }
            }
            if (needSend) {
                log.warn("发送失败到达重复次数" + repeatCount + "次！忽略发送！");
            }
        });
    }

    @Transactional(readOnly = true)
    public boolean isBindPhoneEmpty(Dept dept) {
        if (this.commonSettings.isMainServer()) {
            return this.baseService.isBindPhoneEmpty(dept.getId());
        }
        log.debug("Rmi 接口调用判断部门是否没有绑定号码");
        boolean b = this.rmiService.isBindPhoneEmpty(dept.getCustomerNo(), dept.getDeptLsh());
        log.debug("Rmi 接口调用判断部门是否没有绑定号码返回");
        return b;
    }

    @Transactional(readOnly = true)
    public Dept getParentDept(Dept dept) {
        dept = this.baseService.getDept(dept.getId());
        return dept.getParentDept() != null ? dept.getParentDept() : dept;
    }
    
    /**
     * 返回上级的按键部门，如果上级不是，则继续往上找，直到根部门
     * @param dept
     * @return 
     */
    @Transactional(readOnly = true)
    public Dept getParentInputDept(Dept dept) {
        Dept current = this.baseService.getDept(dept.getId());
        if(current.isRootDept()) {
            return current;
        }
        Dept parent = current.getParentDept();
        while(!parent.isRootDept() && parent.getChildinType() == 1) {
            parent = parent.getParentDept();
        }
        return parent;
    }

    public void cleanAgentCallNumByCustomerNo(String customerNo) {
        this.baseService.cleanAgentCallNum(customerNo, null);
    }

    public void cleanAgentCallNumByDept(String customerNo, String deptLsh) {
        this.baseService.cleanAgentCallNum(customerNo, deptLsh);
    }

    public boolean isQueueUseable(Dept dept) {
        return this.baseService.isQueueUseable(dept);
    }

    public boolean isCallTypeTrans() {
        return this.settings.isCallTypeTrans();
    }

    public String getlocalNo() {
        return this.commonSettings.getLocalNo();
    }

    public String getDistrictNo(String mobile) {
        return this.baseService.getDistrictNo(mobile);
    }

    public boolean isOrigCalleePrefix() {
        return this.commonSettings.isOrigCalleePrefix();
    }

    public boolean isMainServer() {
        return this.commonSettings.isMainServer();
    }

    public String getNumberWav(int num) {
        if (num < 10) {
            return this.settings.getDefaultRingFile(num + ".wav");
        } else {
            StringBuilder sb = new StringBuilder();
            String s = num + "";
            char[] data = s.toCharArray();
            for (int i = 0; i < data.length; i++) {
                sb.append(this.settings.getDefaultRingFile(data[i] + ".wav")).append(",");
            }
            return sb.deleteCharAt(sb.length() - 1).toString();
        }
    }
	
	public String getQueueNumberWav(int num) {
        if (num < 10) {
            return this.settings.getQueueNumFile(num);
        } else {
            //十位
            int decade = num / 10;
            //个位
            int unit = num % 10;
            String file = this.settings.getQueueNumFile(decade * 10);
            if(unit > 0) {
                file += "," + this.settings.getQueueNumFile(unit);
            }
            return file;
        }
    }

    public CallNumDto getCallNumDto(String customerNo, String caller, String callee) {
        return this.ics.interconnectionProcess(customerNo, caller, callee);
    }

    public CallNumDto getDefaultCallNumDto(String originNo, String caller, String callee) {
        CallNumDto dto = new CallNumDto();
        dto.setCallerNo(caller);
        dto.setCalleeNo(callee);
        dto.setOriginalNo(originNo);
        dto.setCallType(this.settings.isCallTypeTrans() ? CallNumDto.CALL_TYPE_TRANS : CallNumDto.CALL_TYPE_DIRECT);
        dto.setTransPrefix(this.commonSettings.isOrigCalleePrefix() ? CallNumDto.TRANS_PREFIX_ON
            : CallNumDto.TRANS_PREFIX_OFF);
        return dto;
    }

    public void setCallDtoDefaultValue(CallNumDto dto) {
        if (dto.getCallType() == CallNumDto.CALL_TYPE_DEFAULT) {
            dto.setCallType(this.settings.isCallTypeTrans() ? CallNumDto.CALL_TYPE_TRANS : CallNumDto.CALL_TYPE_DIRECT);
        }
        if (dto.getTransPrefix() == CallNumDto.TRANS_PREFIX_DEFAULT) {
            dto.setTransPrefix(this.commonSettings.isOrigCalleePrefix() ? CallNumDto.TRANS_PREFIX_ON
                : CallNumDto.TRANS_PREFIX_OFF);
        }
    }

    public boolean isOriginalNoPrefix() {
        return this.commonSettings.isOriginalNoPrefix();
    }

    public String getSpecialOrigNo(String customerNo) {
        return this.specialOrigNoMap.get(customerNo);
    }

    public boolean allowCalleeMobile() {
        return this.commonSettings.isAllowCalleeMobile();
    }

    public boolean isMobile(String callee) {
//        String no = this.getFixedCallerNo(callee);
//        if (no.length() > 13) {
//            return true;
//        }
//        return no.length() == 11 && no.startsWith("1");
        return MyStringUtil.isMobile(callee);
    }

    public String getBindPhoneStatus(String bindPhoneId) {
        BindPhone bp = this.baseService.getBindPhone(bindPhoneId);
        if (this.commonSettings.isMainServer()) {
            return this.baseService.getBindPhoneByCustomerNoAndLsh(bp.getCustomerNo(), bp.getLsh()).getStatus();
        } else {
            log.debug("Rmi 接口调用获取绑定号码状态");
            String s = this.rmiService.getBindPhoneByCustomerNoAndLsh(bp.getCustomerNo(), bp.getLsh()).getStatus();
            log.debug("Rmi 接口调用获取绑定号码状态返回");
            return s;
        }
    }

    public boolean isOrigUseNetphone() {
        return this.commonSettings.isOrigUseNetphone();
    }

    public boolean isForeignCallerAccess(String num) {
        String foreignNum = this.commonSettings.getForeignPrefixNum();
        return foreignNum != null && foreignNum.contains(num);
    }

    public String getDeptBindphoneDetails(Dept dept) {
        if (this.commonSettings.isMainServer()) {
            return this.baseService.getDeptBindphoneDetails(dept.getId());
        } else {
            log.debug("Rmi 接口调用获取部门绑定号码明细");
            String s = this.rmiService.getDeptBindphoneDetails(dept.getCustomerNo(), dept.getDeptLsh());
            log.debug("Rmi 接口调用获取部门绑定号码明细返回");
            return s;
        }
    }
	
	@Transactional(readOnly = true)
	public String getVoiceBoxAlertRing(Dept dept) {
        Dept localDept = this.baseService.getDept(dept.getId());
        return localDept.getVoiceRing() != null ? this.settings.getVocDataPath() + localDept.getVoiceRing().getFilePath() : null;
    }
	
	public boolean isFunctionOpen(String numberCode, String function) {
        return this.baseService.isCustomerFunctionOpen(numberCode, function);
    }
	
	public String getFunctionData(String numberCode, String function) {
        return this.baseService.getCustomerFunctionData(numberCode, function);
    }
	
	public void addCallTimeoutTask(int callTimeout, MainProcessThread mpt) {
		CallTimeoutTask task = new CallTimeoutTask(mpt);
		this.ses.schedule(task, callTimeout, TimeUnit.SECONDS);
	}
	
	public boolean isKeepLocalDistrict(String caller) {
		return caller.length() <= 12 && this.commonSettings.isKeepCallerLocalDistrict();
	}
    
    public boolean isAddCallerNationCode() {
		return this.commonSettings.isAddCallerNationCode();
	}
    
    public String getSupportIvrKey() {
        return this.settings.getSupportIvrKey();
    }
    
    public NumberFuncConfig getNumberConfig(String numberCode) {
        return configService.getByNumberCode(numberCode);
    }
    
    
    public void saveOrUpdatePbxInfo(BindPhone phone,Integer useCount){
    	this.baseService.saveOrUpdatePbxInfo(phone, useCount);
    }
    
    public int getSipConnectDelay() {
        return this.commonSettings.getSipConnectDelay();
    }
    
    public SecondSaleNo getSecondSaleNo(String originalNo) {
        return baseService.getSecondSaleNo(originalNo);
    }
    
    public boolean isSecondSaleOffhook() {
        return commonSettings.isSecondSaleOffhook();
    }
    
    public boolean isAutoTestCaller(String caller) {
        return StringUtils.equals(caller, commonSettings.getAutoTestCaller());
    }
    
    private String getDeptLastBindPhone(Dept dept) {
        if (this.commonSettings.isMainServer()) {
            return dept.getLastBindPhoneLsh();
        } else {
            log.debug("Rmi 接口获取部门最后接听绑定号码");
            String s = this.rmiService.getDeptLastBindPhone(dept.getCustomerNo(), dept.getDeptLsh());
            log.debug("Rmi 接口获取部门最后接听绑定号码返回");
            return s;
        }
    }
    
    public void updateDeptLastBindPhone(String customNo, String deptLsh, String bindPhoneLsh) {
        if (this.commonSettings.isMainServer()) {
            this.baseService.updateDeptLastBindPhone(customNo, deptLsh, bindPhoneLsh);
        } else {
            log.debug("Rmi 接口更新部门最后接听绑定号码");
            this.rmiService.updateDeptLastBindPhone(customNo, deptLsh, bindPhoneLsh);
            log.debug("Rmi 接口更新部门最后接听绑定号码返回");
        }
    }
    
    public void sendSmsMessage(SmsSendDto dto) {
        Message msg = new Message(MqConstants.MESSAGE_TOPIC, MqConstants.SMS_SEND_TAG, JsonHelper.Object2Json(dto).getBytes());
        SendResult result = mqProducer.send(msg);
        log.info("platform sms-messageid:{},topic:{}", result.getMessageId(), result.getTopic());
    }
    
    public void sendErrorMessage(String numberCode, Exception e) {
    	SystemMessageDto dto=new SystemMessageDto();
		dto.setMessageContent(String.format("netphoneLocal-异常原因：%s,异常堆栈：%s", org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage(e), ExceptionUtils.getStackTrace(e)));
		dto.setNumberCode(numberCode);
        dto.setMessageTitle("netphoneLocal 运行异常");
		Message msg = new Message(MqConstants.ERROR_MESSAGE_TOPIC, MqConstants.EXCEPTION_LOG_TAG, JSON.toJSONString(dto).getBytes());
		SendResult sendResult =mqProducer.send(msg);
		assert sendResult != null;
		log.info("send error-messageid:{},topic:{}", sendResult.getMessageId(), sendResult.getTopic());
    }
    
    public void sendMissMessage(User user, BindPhone bindPhone, TalkNote talkNote) {
        SocketMessage message = new CalleeOnhookMessage(bindPhone.getCustomerNo(), bindPhone.getAgentId(),
                bindPhone.getReportNum(), talkNote.getId(), "0");
        HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendOnhookMessage", message);
    }
    
    public void sendTraceMessage(List<CallTraceDto> list) {
        Message msg = new Message(MqConstants.MESSAGE_TOPIC, MqConstants.CALL_TRACE_TAG, JsonHelper.Object2Json(list).getBytes());
        SendResult result = mqProducer.send(msg);
        log.info("platform sms-messageid:{},topic:{}", result.getMessageId(), result.getTopic());
    }
    
    public void sendWhiteListCount(WhitelistCallCountSyncDto dto) {
        Message msg = new Message(MqConstants.MESSAGE_TOPIC, MqConstants.WHITELIST_CALL_COUNT_SYNC_TAG, JsonHelper.Object2Json(dto).getBytes());
        SendResult result = mqProducer.send(msg);
        log.info("platform sms-messageid:{},topic:{}", result.getMessageId(), result.getTopic());
    }
    
    public boolean isSipFlag() {
        return this.settings.isSipFlag();
    }
    
    public String getSipVoiceBoxPath() {
        return this.settings.getSipVoiceboxPath();
    }
    
    public void addOfflineTask(String numberCode, String deptLsh, String caller, int index) {
        this.ses.schedule(new OfflineMessageTask(numberCode, deptLsh, caller, this), index * 2 * 60, TimeUnit.SECONDS);
    }
    
    public void addOfflineCache(String numberCode, String deptLsh, String caller) {
        if (this.commonSettings.isMainServer()) {
            this.baseService.addOfflineCache(numberCode, deptLsh, caller);
        } else {
            this.rmiService.addOfflineCache(numberCode, deptLsh, caller);
        }
    }
    
    public Object getOfflineCache(String numberCode, String deptLsh, String caller) {
        String key = numberCode + "_" + deptLsh + "_" + caller;
        return CacheUtil.get(CacheUtil.CACHE_OFFLINE_NAME, key);
    }
    
    public int getPlatformId(){
    	return commonSettings.getPlatformId();
    }
    
    public boolean isBlackInIvr(String numberCode, String caller, String ivrKey) {
        List<BlackList> blackList = this.baseService.getBlackListWithIvrKey(numberCode, ivrKey);
        if(blackList.isEmpty()) {
            return false;
        }
        for (BlackList black : blackList) {
            boolean inBlackList = black.isNoInBlackList(caller);
            if (inBlackList) {
                return true;
            }
        }
        return false;
    }
    
    public long checkCustomerWhiteList(String customerNo, String caller) {
        return this.commonSettings.isMainServer() ? this.baseService.checkCustomerWhiteList(customerNo, caller) 
                : this.rmiService.checkCustomerWhiteList(customerNo, caller);
    }
    
    @Transactional
    public void subtractWhiteListCount(String customerNo, Long xh) {
        WhiteList whiteList = this.commonSettings.isMainServer() ? this.baseService.subtractWhiteListCount(customerNo, xh) 
                : this.rmiService.subtractWhiteListCount(customerNo, xh);
        if(whiteList != null) {
            WhitelistCallCountSyncDto dto = new WhitelistCallCountSyncDto();
            dto.setNumberCode(customerNo);
            dto.setCallCount(whiteList.getCallCount());
            dto.setUserNo(whiteList.getXh().toString());
            this.sendWhiteListCount(dto);
        }
    }
    
//    public String getZeroCaller() {
//        return this.commonSettings.getZeroCaller();
//    }
//    
//    public boolean isZeroCaller(String caller, String fixedCaller) {
//        String zeroCaller = this.getZeroCaller();
//        return StringUtils.equals(caller, zeroCaller) || StringUtils.equals(fixedCaller, zeroCaller);
//    }
    
    public void sendZeroCallMessage(ZeroCallDto dto) {
        Message msg = new Message(MqConstants.MESSAGE_TOPIC, MqConstants.ZERO_CALL_SYNC_TAG, JsonHelper.Object2Json(dto).getBytes());
        SendResult result = mqProducer.send(msg);
        log.info("platform zeroCall-messageid:{},topic:{}", result.getMessageId(), result.getTopic());
    }
    
    public long getZeroDelayTime() {
        String time = this.commonSettings.getZeroDelayTime();
        return time == null ? 0 : Long.parseLong(time);
    }
    
    public Dept findDeptByKeyWords(String numberCode, String keyWords) {
        return this.baseService.findDeptByKeyWords(numberCode, keyWords);
    }

    public void sendAutoNotifyMessage(RingAutoNotifyInfoDto dto) {
        Message msg = new Message(MqConstants.TLCENTER_PLUS_MESSAGE_TOPIC, MqConstants.AUTO_NOTIFY_SYNC_TAG, JsonHelper.Object2Json(dto).getBytes());
        SendResult result = mqProducer.send(msg);
        log.info("platform autoNotify-messageid:{},topic:{}", result.getMessageId(), result.getTopic());
    }
    
    public int checkSecurityCode(String numberCode, String code) {
        SecurityCodeCheckDto dto = new SecurityCodeCheckDto(numberCode, code);
        try {
            SungoinResponse resp = HttpHelper.postSungoinHttps(this.commonSettings.getSecurityCheckUrl(), dto);
            if(resp == null || resp.getData() == null) {
                return -1;
            }
            return (Integer) ((Map)resp.getData()).get("result");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return -1;
        }
    }
    
    public String getSecurityRing(int result) {
        switch (result) {
            case -1:
                return this.getDefaultRing(MidwareConstants.DEFAULT_RING_VALID_ERROR);
            case 0:
                return this.getDefaultRing(MidwareConstants.DEFAULT_RING_VALID_FAIL);
            case 1:
                return this.getDefaultRing(MidwareConstants.DEFAULT_RING_VALID_SUCCESS);
            case 2:
                return this.getDefaultRing(MidwareConstants.DEFAULT_RING_VALID_CHECKED);
            default:
                return this.getDefaultRing(MidwareConstants.DEFAULT_RING_VALID_ERROR);
        }
    }
    
    @Transactional
    public String getAgentStatusWithMultiple(BindPhone bp) {
        User user = this.baseService.getUserByNumber(bp.getCustomerNo());
        if(StringUtils.isEmpty(user.getUniqueName())) {
            return bp.getStatus();
        }
        List<BindPhone> multiplePhone = this.businessService.findMultipleBindPhone(bp.getBindPhoneNo(), user.getUniqueName(), user.getNumber());
        Optional<BindPhone> opt = multiplePhone.stream().filter(bindPhone -> bindPhone.getStatus().equals(MidwareConstants.AGENT_STATE_CONNECT)).findAny();
        if(opt.isPresent()) {
            BindPhone callBindPhone = opt.get();
            log.info("多号：{}的绑定号码状态为 CONNECT,直接返回 CONNECT", callBindPhone.getCustomerNo());
            return callBindPhone.getStatus();
        } else {
            return bp.getStatus();
        }
    }
    
    public String getRecordPath() {
        return this.settings.getRecordPath();
    }

    public List<ColorRing> findAllColorRingByCustomerAndLsh(String customerNo, String lsh) {
        return this.baseService.findAllColorRingByCustomerAndLsh(customerNo, lsh);
    }
    
    @Transactional(readOnly = true)
    public Dept getTopDeptByIvrKey(String customerNo, String ivrKey) {
        return this.baseService.findTopDeptByCustomerNoAndIvrKey(customerNo, ivrKey);
    }

    public String getLocationByBindPhone(String bindPhone) {
        return this.baseService.getLocation(bindPhone);
    }
    
    public User getUserByNumber(String number) {
        return this.baseService.getUserByNumber(number);
    }
    
    /**
     * 多号批量更新状态
     * @param bindPhone
     * @param state
     * @param sendMessage
     * @return 
     */
    @Transactional
    public int midwareBatchUpdateAgentState(BindPhone bindPhone, String state, boolean sendMessage, String uniqueName) {
        int count;
        if (this.commonSettings.isMainServer()) {
            count = this.baseService.batchModifyAgentStatus(bindPhone.getId(), state, uniqueName);
        } else {
            log.debug("Rmi 接口批量更新坐席状态");
            count =this.rmiService.batchModifyAgentStatus(bindPhone.getCustomerNo(), bindPhone.getLsh(), state, uniqueName);
            log.debug("Rmi 接口批量更新坐席状态返回");
        }
        log.debug("多号批量更新状态，更新条数：{}", count);
        if(count > 0) {
            ConcurrentHelper.doInBackground(() -> {
                List<BindPhone> multiplePhone = this.businessService.findMultipleBindPhone(bindPhone.getBindPhoneNo(), uniqueName);
                multiplePhone.forEach(bp -> {
                    //发送多号状态消息
                    if (sendMessage) {
                        SocketMessage message = new AgentMessage(bp.getCustomerNo(), bp.getAgentId(),
                        bp.getLsh(), state, bp.getOrigBindPhoneNo(), bp.isNeedLogin());
                        log.debug("运营点多号更新坐席状态，发送socket消息通知中心端。{}", message);
                        HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendAgentMessage", message);
                    }    
                    if (MidwareConstants.AGENT_STATE_IDLE.equals(state)) {
                        log.debug("运营点多号更新坐席状态变闲，通知排队队列");
                        if (StringUtils.isNotBlank(bp.getAgentId())) {
                            this.notifyQueueAgentOnhook(bp);
                        } else {
                            this.notifyQueueAgentOnhook(bp.getDept());
                        }
                    }
                });
            });
        }
        return count;
    }
    
    @Transactional
    public int midwareBatchUpdateAgentStateByBindPhone(BindPhone bindPhone, String state, boolean sendMessage, String uniqueName) {
        int count;
        if (this.commonSettings.isMainServer()) {
            count = this.baseService.batchModifyAgentStatusByBindphone(bindPhone.getBindPhoneNo(), state, uniqueName);
        } else {
            log.debug("Rmi 接口批量更新坐席状态");
            count =this.rmiService.batchModifyAgentStatusByBindphone(bindPhone.getBindPhoneNo(), state, uniqueName);
            log.debug("Rmi 接口批量更新坐席状态返回");
        }
        log.debug("多号批量更新状态，更新条数：{}", count);
        if(count > 0) {
            ConcurrentHelper.doInBackground(() -> {
                List<BindPhone> multiplePhone = this.businessService.findMultipleBindPhone(bindPhone.getBindPhoneNo(), uniqueName);
                multiplePhone.forEach(bp -> {
                    //发送多号状态消息
                    if (sendMessage) {
                        SocketMessage message = new AgentMessage(bp.getCustomerNo(), bp.getAgentId(),
                        bp.getLsh(), state, bp.getOrigBindPhoneNo(), bp.isNeedLogin());
                        log.debug("运营点多号更新坐席状态，发送socket消息通知中心端。{}", message);
                        HttpHelper.postPlatformClient(commonSettings.getPlatformClientEndpoint() + "sendAgentMessage", message);
                    }    
                    if (MidwareConstants.AGENT_STATE_IDLE.equals(state)) {
                        log.debug("运营点多号更新坐席状态变闲，通知排队队列");
                        if (StringUtils.isNotBlank(bp.getAgentId())) {
                            this.notifyQueueAgentOnhook(bp);
                        } else {
                            this.notifyQueueAgentOnhook(bp.getDept());
                        }
                    }
                });
            });
        }
        return count;
    }
    
    @Transactional
    public String getDeptIvrKeyName(Dept dept) {
        Dept currentDept = this.baseService.getDept(dept.getId());
        while(StringUtils.isEmpty(currentDept.getIvrKey()) && !currentDept.isRootDept()) {
            currentDept = currentDept.getParentDept();
        }
        return (currentDept.getIvrKey() == null ? "" : currentDept.getIvrKey()) + ":" + currentDept.getName();
    }
}
