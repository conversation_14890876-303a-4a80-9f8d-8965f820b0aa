/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.midware.service;

import com.sungoin.cti.client.api.Call;

/**
 *
 * <AUTHOR>
 */
public interface EventHandler {
    void callerOnhook();
    void calleeOnhook();
    void transOnhook(Call trans);
    void callOver(int status, int errorCode);
    void ivrReceived(String key);
    void dtmfTrans();
    void cdr(String record);
    void detectSpeech(String text);
    void dtmfToIvr();
}
