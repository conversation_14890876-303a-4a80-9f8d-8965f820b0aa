/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.midware.service.baidu;

import com.sungoin.netphoneLocal.business.bean.CallNumDto;
import com.sungoin.netphoneLocal.business.bean.baidu.BaiduResult;
import com.sungoin.netphoneLocal.business.po.OutDataTask;
import com.sungoin.netphoneLocal.business.po.OutDataType;
import com.sungoin.netphoneLocal.business.po.TalkNote;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.business.po.VoiceRecord;
import com.sungoin.netphoneLocal.business.service.BaiduService;
import com.sungoin.netphoneLocal.business.service.BaseService;
import com.sungoin.netphoneLocal.business.service.BusinessService;
import com.sungoin.netphoneLocal.business.service.SpeechSynthesizerService;
import com.sungoin.netphoneLocal.config.CommonSettings;
import com.sungoin.netphoneLocal.midware.config.MidwareSettings;
import com.sungoin.netphoneLocal.util.DateTimeUtil;
import com.sungoin.netphoneLocal.util.VoiceUtil;

import java.io.File;
import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.sungoin.netphoneLocal.midware.service.ProcessService.MONITOR_PREFIX;

/**
 *
 * <AUTHOR>
 */
@Service
public class BaiduProcessService {

    private static final Logger log = LoggerFactory.getLogger(BaiduProcessService.class);

    @Resource
    private MidwareSettings settings;

    @Resource
    private CommonSettings commonSettings;

    @Resource
    private BaiduService baiduService;

    @Resource
    private SpeechSynthesizerService ttsService;

    @Resource
    private BaseService baseService;

    @Resource
    private BusinessService businessService;

    ExecutorService es = Executors.newCachedThreadPool();

    public BaiduResult getIvrInfo(TalkNote talk, String ivrKey) {
        return baiduService.queryIvrInfo(talk, ivrKey);
    }

    public BaiduResult getBindInfo(TalkNote talk, String ivrKey) {
        return baiduService.queryBindPhone(talk, ivrKey);
    }

    public String getFixedCallerNo(String callNo) {
        return this.baseService.processCallerNo(callNo);
    }

    public void saveOrUpdateTalkNote(final TalkNote talkNote, boolean async) {
        if(async) {
            this.es.submit(() -> {
                try {
                    BaiduProcessService.this.businessService.saveOrUpdate(talkNote);
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }
            });
        } else {
            this.businessService.saveOrUpdate(talkNote);
        }
    }
    
    public void saveOrUpdateTalkNote(final TalkNote talkNote) {
       saveOrUpdateTalkNote(talkNote, true);
    }

    public String getTtsFilePath(String userNo, String content) {
        return ttsService.process(userNo, content);
    }

    @Transactional
    public void endTalkNote(TalkNote talkNote) {
        if (talkNote.isEndFlag() && talkNote.getCalleeOffhookTime() != null) {
            talkNote.setTalkInterval(DateTimeUtil.getTimeDifference(talkNote.getCalleeOffhookTime(),
                    talkNote.getOnhookTime()));
        }
        final TalkNote note = talkNote;
        this.es.submit(() -> {
            try {
                BaiduProcessService.this.businessService.saveOrUpdate(note);
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
            BaiduProcessService.this.businessService.saveTalkTask(new OutDataTask(note.getId(), OutDataType.TALK));
        });
    }

    public void endRecord(final TalkNote talkNote, VoiceRecord vr, final boolean transMp3) {
        if (vr.isEndFlag()) {
            vr.setInterval(DateTimeUtil.getTimeDifference(vr.getStartTime(), vr.getEndTime()));
        }
        final VoiceRecord record = vr;
        this.es.submit(() -> {
            if (talkNote.getId() == null) {
                try {
                    BaiduProcessService.this.businessService.saveOrUpdate(talkNote);
                } catch (Exception ex) {
                    log.error(ex.getMessage(), ex);
                }
            }
            if (record.getTalkNoteId() == null) {
                record.setTalkNoteId(talkNote.getId());
            }
            if(!this.isSipFlag()) {
                String[] filePathArr=record.getFilePath().split(",");
                String filePath = filePathArr[0].substring(0,filePathArr[0].lastIndexOf("/"));
                String complexFileName = vr.getCallerNo() + "_" + vr.getStartTime().getTime() + "_LR.mp3";
                String complexFilePath = filePath + "/" + complexFileName;

                VoiceUtil.wavToMp3(filePathArr[0], filePathArr[1], complexFilePath);
                record.setFileName(complexFileName);
                record.setFilePath(complexFilePath);
            }
            BaiduProcessService.this.businessService.saveOrUpdateVoiceRecord(record);
            BaiduProcessService.this.businessService.saveTalkTask(new OutDataTask(talkNote.getId(), OutDataType.VOICE));
        });
    }
    
    public void saveOrUpdateRecord(final VoiceRecord vr) {
        this.es.submit(() -> {
            BaiduProcessService.this.businessService.saveOrUpdateVoiceRecord(vr);
        });
    }

    public String getDefaultRing(String ringName) {
        return this.settings.getDefaultRingFile(ringName);
    }

    public int getIvrTimeoutSeconds() {
        return this.settings.getIvrTimeoutSeconds();
    }

    public String getFixedCalleeNo(String callNo) {
        return this.baseService.processCallNo(callNo);
    }

    public String getlocalNo() {
        return this.commonSettings.getLocalNo();
    }
    
    public boolean isInTest() {
        return this.commonSettings.isInTest();
    }

    public boolean isForeignCallerAccess(String num) {
        String foreignNum = this.commonSettings.getForeignPrefixNum();
        return foreignNum != null && foreignNum.contains(num);
    }

    public boolean isOriginalNoPrefix() {
        return this.commonSettings.isOriginalNoPrefix();
    }

    public boolean isKeepLocalDistrict(String caller) {
        return caller.length() <= 12 && this.commonSettings.isKeepCallerLocalDistrict();
    }

    public boolean isAddCallerNationCode() {
        return this.commonSettings.isAddCallerNationCode();
    }

    public CallNumDto getDefaultCallNumDto(String originNo, String caller, String callee) {
        CallNumDto dto = new CallNumDto();
        dto.setCallerNo(caller);
        dto.setCalleeNo(callee);
        dto.setOriginalNo(originNo);
        dto.setCallType(this.settings.isCallTypeTrans() ? CallNumDto.CALL_TYPE_TRANS : CallNumDto.CALL_TYPE_DIRECT);
        dto.setTransPrefix(this.commonSettings.isOrigCalleePrefix() ? CallNumDto.TRANS_PREFIX_ON
                : CallNumDto.TRANS_PREFIX_OFF);
        return dto;
    }

    public boolean isOrigUseNetphone() {
        return this.commonSettings.isOrigUseNetphone();
    }

    public VoiceRecord startRecord(User user, String caller, String callee) {
        String prefix = "";
        if (this.baseService.isNotRecordConvertWhiteList(user.getNumber())) {
            log.debug("{} 开启用户监听！", user.getNumber());
            prefix = MONITOR_PREFIX;
        }
        Date now = new Date();
        VoiceRecord record = new VoiceRecord();
        record.setCalleeNo(callee);
        record.setCallerNo(caller);
        record.setStartTime(now);
        record.setCustomerNo(user.getNumber());
        Long timestamp = System.currentTimeMillis();
        String fileNameLeft = prefix + caller + "_" + timestamp + "_L.wav";
        String fileNameRight = prefix + caller + "_" + timestamp + "_R.wav";
        String fileName = fileNameLeft + "," + fileNameRight;
        String filePath = this.settings.getRecordPath() + "/" + DateTimeUtil.formatShortDate(now) + "/"
                + user.getNumber();
        File file = new File(filePath);
        if (!file.exists() || !file.isDirectory()) {
            file.mkdirs();
        }
        String filePathLeft = filePath + "/" + fileNameLeft;
        String filePathRight = filePath + "/" + fileNameRight;
        record.setFileName(fileName);
        record.setFilePath(filePathLeft + "," + filePathRight);
        return record;
    }
    
    public boolean isSipFlag() {
        return this.settings.isSipFlag();
    }
    
    public String getSipVoiceBoxPath() {
        return this.settings.getSipVoiceboxPath();
    }
}
