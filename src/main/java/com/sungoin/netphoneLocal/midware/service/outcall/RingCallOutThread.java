package com.sungoin.netphoneLocal.midware.service.outcall;

import com.sungoin.cloudstorage.client.CloudStorageClient;
import com.sungoin.cloudstorage.client.dto.ClientUploadInputStream;
import com.sungoin.cloudstorage.dto.UploadResponse;
import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.CallManager;
import com.sungoin.netphoneLocal.business.bean.ZeroCallDto;
import com.sungoin.netphoneLocal.business.po.ColorRing;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import com.sungoin.netphoneLocal.midware.service.ProcessService;
import com.sungoin.netphoneLocal.util.DateTimeUtil;
import com.sungoin.netphoneLocal.util.HttpHelper;
import com.sungoin.netphoneLocal.util.VoiceUtil;
import org.apache.commons.io.FileUtils;
import org.apache.http.entity.mime.content.InputStreamBody;
import org.aspectj.util.FileUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.CALL_TYPE_OUTCALL;

public class RingCallOutThread implements Runnable {
    private static final Logger log = LoggerFactory.getLogger(RingCallOutThread.class);
    private static final String SIP_PREFIX = "z";
    private static final String SIP_LOCAL_PATH = "/usr/local/freeswitch";
    private final RingAutoTestDto dto;
    private final ProcessService service;
    private final ColorRing ring;

    public RingCallOutThread(RingAutoTestDto dto, ProcessService service, ColorRing ring) {
        this.dto = dto;
        this.service = service;
        this.ring = ring;
    }
    @Override
    public void run() {
        try {
            Date now = new Date();
            String caller = dto.getCaller();
            String callee = this.service.getFixedCalleeNo(dto.getCallee());;
            log.debug("自动彩铃外呼开始呼叫，主叫：{}， 被叫：{}，", caller, callee);
            if(service.isSipFlag()) {
                //sip平台添加网关前缀
                callee = SIP_PREFIX + callee;
            }
            Call call = CallManager.getInstance().makeCall(caller, callee, null, false, 30, null);
            if(call != null) {
                call.setParam(CALL_TYPE, CALL_TYPE_OUTCALL);
                String filePath;
                if(service.isSipFlag()) {
                    //sip 落地特殊地址,文件名加上boxes标识
                    filePath = SIP_LOCAL_PATH + "/storage/recordings/" + caller + "_" + now.getTime() + "_boxes.wav";
                } else {
                    filePath = this.service.getRecordPath() + "/" + DateTimeUtil.formatShortDate(now) + "/" + caller + "_" + now.getTime() + ".wav";
                }
                log.debug("{}-录音地址：{}", dto.getNumberCode(), filePath);
                call.record(filePath, 30, false);
                String ringPath = service.getRingFile(ring);
                log.debug("{}-呼通后播放音频：{}", dto.getNumberCode(), ringPath);
                call.play(ringPath, false, false, false, 30, 0);
                if(call.getState() == Constants.CALL_CONTENT) {
                    call.onHook();
                }
                //上传录音文件
                File file = new File(service.isSipFlag() ? filePath.replace(SIP_LOCAL_PATH, service.getSipVoiceBoxPath()) : filePath);
                if(file.exists()) {
                    String mp3Path = file.getAbsolutePath().replace("wav", "mp3");
                    VoiceUtil.wavToMp3(file.getAbsolutePath(), mp3Path);
                    File uploadFile = file;
                    File mp3File = new File(mp3Path);
                    if(mp3File.exists()){
                        uploadFile = mp3File;
                    }
                    Long timestamp = System.currentTimeMillis();
                    CloudStorageClient client = new CloudStorageClient("400file", "400file");
                    ClientUploadInputStream clientInputStream=new ClientUploadInputStream();
                    clientInputStream.setCustomId(dto.getTestId());
                    FileInputStream fis = new FileInputStream(uploadFile);
                    InputStreamBody inputStreamBody=new InputStreamBody(fis,uploadFile.getName());
                    clientInputStream.setInputStreamBody(inputStreamBody);
                    UploadResponse resp=client.uploadDefaultInputStreamFile(clientInputStream, timestamp);
                    log.info("{}, 录音地址：{}-上传云存储返回：{}", dto.getNumberCode(), uploadFile.getAbsolutePath(), resp.getCode());
                    dto.setCallTime(timestamp);
                    String result = HttpHelper.postHttpsJson(dto.getNotifyUrl(), dto);
                    log.info("{}-回调通知返回：{}", dto.getNumberCode(), result);
                } else {
                    log.warn("{}-录音文件不存在：{}", dto.getNumberCode(), file.getAbsolutePath());
                }
            } else {
                log.warn("{}-呼叫失败", dto.getNumberCode());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
