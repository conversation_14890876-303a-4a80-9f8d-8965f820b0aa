/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service.elevator;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class OnhookCreateInfo {
    private String project_name;
    private String number_info;
    private String maintenance_name;
    private String property_name;
    private String address;

    public OnhookCreateInfo(Map data) {
        this.project_name = (String) data.get("project_name");
        this.number_info = (String) data.get("number_info");
        this.maintenance_name = (String) data.get("maintenance_name");
        this.property_name = (String) data.get("property_name");
        this.address = (String) data.get("address");
    }

    public String getProject_name() {
        return project_name;
    }

    public void setProject_name(String project_name) {
        this.project_name = project_name;
    }

    public String getNumber_info() {
        return number_info;
    }

    public void setNumber_info(String number_info) {
        this.number_info = number_info;
    }

    public String getMaintenance_name() {
        return maintenance_name;
    }

    public void setMaintenance_name(String maintenance_name) {
        this.maintenance_name = maintenance_name;
    }

    public String getProperty_name() {
        return property_name;
    }

    public void setProperty_name(String property_name) {
        this.property_name = property_name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("OnhookCreateInfo{");
        sb.append("project_name=").append(project_name);
        sb.append(", number_info=").append(number_info);
        sb.append(", maintenance_name=").append(maintenance_name);
        sb.append(", property_name=").append(property_name);
        sb.append(", address=").append(address);
        sb.append('}');
        return sb.toString();
    }
    
}
