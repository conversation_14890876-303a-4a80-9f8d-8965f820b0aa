/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.sungoin.netphoneLocal.midware.exception;

import com.sungoin.netphoneLocal.exception.NetphoneException;

/**
 * 
 * <AUTHOR>
 * 2015-7-29
 */
public class CtiInitFailException extends NetphoneException {
	private static final long serialVersionUID = 6152556293335204515L;
	
	public CtiInitFailException() {
		super();
	}

	public CtiInitFailException(String msg) {
		super(msg);
	}

	public CtiInitFailException(Throwable root) {
		super(root);
	}

	public CtiInitFailException(String msg, Throwable cause) {
		super(msg, cause);
	}
}
