/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.midware.listener;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.api.TeleThreadEventListener;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.CdrEvent;
import com.sungoin.cti.client.event.DetectSpeechEvent;
import com.sungoin.cti.client.event.EventType;
import com.sungoin.cti.client.event.ReceiveDTMFEvent;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.MAIN_CALL_THREAD;
import java.io.Serializable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.sungoin.netphoneLocal.midware.service.EventHandler;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR> 2015-7-28
 */
public class CtiThreadListener implements TeleThreadEventListener, Serializable {

	private static final long serialVersionUID = -8971395185636349158L;
	private static final Logger log = LoggerFactory.getLogger(CtiThreadListener.class);
	public static final CtiThreadListener INSTANCE = new CtiThreadListener();

	private CtiThreadListener() {
	}

	@Override
	public void asyncFinished(BaseEvent be) {
		log.debug("线程异步结束事件：eventType={},eventState={},deviceID={},callLsh={}", be.getType(), be.getEventState(),
			be.getDeviceID(), be.getLsh());
		if (be.getType().equals(EventType.CALLOUT)) {
			Call call = be.getCall();
			if (call != null) {
				EventHandler handler = (EventHandler) call.getParam(MAIN_CALL_THREAD);
				int state = be.getEventState();
				handler.callOver(be.getEventState(), be.getErrorCode());
				if (state != Constants.RET_SUCCESS) {
					call.setParam(MAIN_CALL_THREAD, null);
				}
			}
		} else if (be.getType().equals(EventType.RECEIVEDTMF)) {
			Call call = be.getCall();
			if (call != null) {
				String dtmf = ((ReceiveDTMFEvent) be).getDtmf();
				EventHandler handler = (EventHandler) call.getParam(MAIN_CALL_THREAD);
				handler.ivrReceived(dtmf);
			}
		} else if(be.getType().equals(EventType.CDR)) {
            Call call = be.getCall();
            if(call != null) {
                CdrEvent cdr = (CdrEvent) be;
                EventHandler handler = (EventHandler) call.getParam(MAIN_CALL_THREAD);
                handler.cdr(cdr.getRecord());
            }
        } else if(be.getType().equals(EventType.DETECTSPEECH)) {
            Call call = be.getCall();
            if(call != null) {
                DetectSpeechEvent speech = (DetectSpeechEvent) be;
                if(speech.getEventState() == 1 && StringUtils.isNotEmpty(speech.getText())) {
                    EventHandler handler = (EventHandler) call.getParam(MAIN_CALL_THREAD);
                    handler.detectSpeech(speech.getText());
                }
            }
        }
	}
}
