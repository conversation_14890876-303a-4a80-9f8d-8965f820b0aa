/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.midware.listener;

import javax.annotation.Resource;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.sungoin.cti.client.api.CallManager;
import com.sungoin.netphoneLocal.config.CommonSettings;
import com.sungoin.netphoneLocal.midware.exception.CtiInitFailException;
import com.sungoin.netphoneLocal.util.ConcurrentHelper;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> 2015-7-28
 */
@Component
public class StartupListener implements ServletContextListener {

    private static final Logger log = LoggerFactory
        .getLogger(StartupListener.class);
    CallManager manager;

    @Resource
    private CommonSettings settings;

    @Override
    public void contextInitialized(ServletContextEvent sce) {
        if (this.settings.isBootConnectCti()) {
            log.debug("start init CallManager...");
            int count = 0;
            String errorMessage = null;
			this.manager = CallManager.getInstance();
            do {
                try {
                    if (count > 0) {
                        TimeUnit.SECONDS.sleep(3);
						log.warn("开始第{}次重新连接ctiserver！", count);
                    }
                    this.manager.init(new GlobalCtiEventListener());
					log.info("cti connect success...");
                } catch (Exception ex) {
                    errorMessage = ex.getMessage();
                    count++;
                }
            } while (!this.manager.isRunning() && count < 5);
            if (!this.manager.isRunning()) {
                log.error(errorMessage);
                throw new CtiInitFailException(errorMessage);
            }
        } else {
            log.info("not need connect cti");
        }
    }

    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        log.debug("context destroyed! shutdown CallManager...");
        if (this.manager != null && this.manager.isRunning()) {
            this.manager.shutdown();
        }
        ConcurrentHelper.destory();
    }
}
