/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.midware.listener;

import static com.sungoin.netphoneLocal.midware.MidwareConstants.MAIN_CALL_THREAD;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.RECEIVED_DTMF;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.RECEIVED_DTMF_TIME;

import java.util.Date;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sungoin.cti.client.Constants;
import com.sungoin.cti.client.api.CTIEventListener;
import com.sungoin.cti.client.api.Call;
import com.sungoin.cti.client.event.BaseEvent;
import com.sungoin.cti.client.event.CdrEvent;
import com.sungoin.cti.client.event.DetectSpeechEvent;
import com.sungoin.cti.client.event.EventType;
import com.sungoin.cti.client.event.ReceiveDTMFEvent;
import com.sungoin.netphoneLocal.business.bean.BusinessConstants;
import com.sungoin.netphoneLocal.business.po.NumberFuncConfig;
import com.sungoin.netphoneLocal.business.po.OutDataTask;
import com.sungoin.netphoneLocal.business.po.OutDataType;
import com.sungoin.netphoneLocal.business.po.SecondSaleNo;
import com.sungoin.netphoneLocal.business.po.TalkNote;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.business.service.BaseService;
import com.sungoin.netphoneLocal.config.CommonSettings;
import com.sungoin.netphoneLocal.constants.CallOutErrorCode;
import com.sungoin.netphoneLocal.constants.FunctionSwitchConstants;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import static com.sungoin.netphoneLocal.midware.MidwareConstants.MAIN_CALL_THREAD;
import com.sungoin.netphoneLocal.midware.config.MidwareSettings;
import com.sungoin.netphoneLocal.midware.service.ComboExpireThread;
import com.sungoin.netphoneLocal.midware.service.MainProcessThread;
import com.sungoin.netphoneLocal.midware.service.ProcessService;
import com.sungoin.netphoneLocal.midware.service.baidu.BaiduProcessThread;
import com.sungoin.netphoneLocal.util.SpringHelper;
import java.util.List;
import java.util.concurrent.TimeUnit;
import com.sungoin.netphoneLocal.midware.service.EventHandler;
import com.sungoin.netphoneLocal.midware.service.NoChargeProcessThread;
import com.sungoin.netphoneLocal.midware.service.SecondSaleProcessThread;
import com.sungoin.netphoneLocal.midware.service.cpcc.CpccProcessThread;
import com.sungoin.netphoneLocal.midware.service.elevator.ElevatorProcessThread;
import com.sungoin.netphoneLocal.midware.service.great.GreatProcessThread;
import com.sungoin.netphoneLocal.midware.service.sungoin.SungoinProcessThread;
import com.sungoin.netphoneLocal.util.DateTimeUtil;
import com.sungoin.netphoneLocal.util.MyStringUtil;

/**
 * <AUTHOR> 2015-7-28
 */
public class GlobalCtiEventListener implements CTIEventListener {

    private static final Logger log = LoggerFactory.getLogger(GlobalCtiEventListener.class);

    @Override
    public void callIncome(Call call) {
        log.debug("呼入事件：ano={},bno={},deviceID={},callLsh={}", call.getCaller(), call.getCallee(), call.getDeviceId(),
            call.getLsh());
        try {
            call.setParam(MidwareConstants.CALL_TYPE, "CallIn");
            ProcessService service = SpringHelper.getBean(ProcessService.class);
            boolean isGroupCall = service.isGroupCall(call.getCallee(), call.getCaller());
            log.debug("当前小号：{}，是否群呼号码：{}", call.getCallee(), isGroupCall);
            //判断是否二销号码
            SecondSaleNo ssno = service.getSecondSaleNo(call.getCallee());
            if(ssno != null && !isGroupCall) {
                log.info("二销表中存在此号码：{}", ssno);
                new SecondSaleProcessThread(ssno, service, call).start();
                User user = service.getBaseService().getUserByOriginalNo(call.getCallee());
                if(user!=null && user.isUseFlag()){
                	log.info("二销表中存在此号码：{}，对应用户表：{}，话务已开启，推送异常通知", ssno, user.getNumber());
                	int platformId=service.getPlatformId();
                	service.sendErrorMessage(ssno.getUserNo(), new RuntimeException("号码在二销表中,所属平台Id:"+platformId));	
                }
                return;
            }
            //处理0086开头的情况
            if (call.getCaller().startsWith("0086")) {
                String caller = call.getCaller();
                char flag = caller.charAt(4);
                if (flag == '1') {
                    log.info("0086 + 手机号码，截取0086");
                    call.setCaller(caller.substring(4));
                } else {
                    if (caller.length() > 12) {
                        log.info("0086 + 大于8位固话，截取0086后补0");
                        call.setCaller("0" + caller.substring(4));
                    } else {
                        log.info("0086 + 小于等于8位固话，截取0086");
                        call.setCaller(caller.substring(4));
                    }
                }
            }
            //处理000开头情况
            if (call.getCaller().startsWith("000")) {
                String caller = call.getCaller();
                char flag = caller.charAt(3);
                if (flag == '1') {
                    log.info("000 + 手机号码，截取000");
                    call.setCaller(caller.substring(3));
                } else {
                    if (caller.length() > 11) {
                        log.info("000 + 大于8位固话，截取000后补0");
                        call.setCaller("0" + caller.substring(3));
                    } else {
                        log.info("000 + 小于等于8位固话，截取000");
                        call.setCaller(caller.substring(3));
                    }
                }
            }
            
            CommonSettings commonSettings= SpringHelper.getBean(CommonSettings.class);
            if(commonSettings.isSpecialPrefix()) {
            	log.info("特殊主叫平台");
	            int length=call.getCaller().length();
	            if(length>=9 && length<=11 && !call.getCaller().startsWith("0") && !call.getCaller().startsWith("1")){
	            	call.setCaller("0"+call.getCaller());
	            }
	            if(length>=9 && length<=10 && call.getCaller().startsWith("10")){
	            	call.setCaller("0"+call.getCaller());
	            }
            }
            
			//根据配置文件处理主叫号码
            String fixedCaller = this.fixCaller(call.getCaller());
            if (fixedCaller != null) {
                call.setCaller(fixedCaller);
            }
            User user = service.getUserByOriginalNo(isGroupCall, call.getCallee(), call.getCaller());
            NumberFuncConfig config = user == null ? null : service.getNumberConfig(user.getNumber());
            boolean isForeign = call.getCaller().startsWith("00");
            if (!isForeign && !this.judgeCaller(call.getCaller(), user, config)) {
                call.onHook();
                return;
            }
            Date now = new Date();
            if (user == null) {
                log.warn("根据接入号:{} 找不到对应的400号码！终止呼叫！", call.getCallee());
                call.onHook();
            } else if (isForeign && (config == null || !config.isInternationFlag())) {
                log.warn("主叫号码：{}，未配置允许国际号码呼入！终止呼叫！", call.getCaller());
                call.onHook();
            } else if (!user.isUseFlag() || (user.getEndTime() != null && DateUtils.addDays(now,-1).after(user.getEndTime()))) {
                log.warn("欠费停机/套餐到期！开始套餐到期流程", user.getNumber());
                new ComboExpireThread(user, service, call).start();
            } else if ((user.getStartTime() != null && now.before(user.getStartTime())) //                || (user.getEndTime() != null && now.after(user.getEndTime()))) {
            //modify by chenlei at 2016-6-15 去掉结束日期的判断
            ) {
                log.warn("400号码:{}不在有效期内！终止呼叫！", user.getNumber()); 
                call.onHook();
                //记录话单
                String talkNoteId = saveTalkNote(service, user, call.getCaller(), 6);
                log.info("二销号码话单记录成功！话单ID：" + talkNoteId);
            } else if("on".equals(service.getFunctionData(user.getNumber(), FunctionSwitchConstants.FUNCTION_CUSTOMER_WHITE_LIST))) {
                //自定义白名单验证
                long xh = service.checkCustomerWhiteList(user.getNumber(), call.getCaller());
                if(xh < 0) {
                    //白名单验证不通过
                    TalkNote talkNote = new TalkNote(user.getNumber(), user.getOriginalNo(), call.getCaller(), now);
                    talkNote.setCallerOffhookTime(now);
                    talkNote.setDeptId(user.getNumber());
                    String tipRing = service.getDefaultRing(xh == BusinessConstants.CUSTOMER_WHITE_LIST_NOT_IN ? MidwareConstants.WHITE_LIST_NOT_IN : MidwareConstants.WHITE_LIST_COUNT_LIMIT);
                    log.info("主叫号码:{}未通过白名单验证！播放白名单提示音：{}", call.getCaller(), tipRing);
                    call.answerSync(500);
                    call.play(tipRing, false, false, false, 30, 0);
                    if(call.getState() != Constants.CALL_IDLE) call.onHook();
                    //保存话单
                    String talkNoteId = saveWhiteListTalkNote(service, talkNote);
                    log.info("白名单话单记录成功！话单ID：" + talkNoteId);
                } else {
                    log.info("主叫号码:{},400号码:{}主线程开始！", call.getCaller(), user.getNumber());
                    new MainProcessThread(call, user, CtiThreadListener.INSTANCE, config, xh).start();
                }
            } else if (!service.isCallerPassBlackAndWhiteNo(user, call.getCaller())) {
                //常规白名单验证
                log.warn("主叫号码:{}未能通过400号码:{}的黑白名单验证！终止呼叫！", call.getCaller(), user.getNumber());
                call.onHook();
            } else {
                String customProcess = service.getFunctionData(user.getNumber(), FunctionSwitchConstants.FUNCTION_CUSTOMER_PROCESS);
                if("elevator".equals(customProcess)) {
                    log.info("主叫号码:{},400号码:{}电梯救援主线程开始！", call.getCaller(), user.getNumber());
                    new ElevatorProcessThread(call, user, CtiThreadListener.INSTANCE, config).start();
                } else if("sungoin".equals(customProcess)) {
                    log.info("主叫号码:{},400号码:{}尚景主线程开始！", call.getCaller(), user.getNumber());
                    new SungoinProcessThread(call, user, CtiThreadListener.INSTANCE, config).start();
                } else if("great".equals(customProcess)) {
                    log.info("主叫号码:{},400号码:{}格瑞主线程开始！", call.getCaller(), user.getNumber());
                    new GreatProcessThread(call, user, CtiThreadListener.INSTANCE, config).start();
                } else if("cpcc".equals(customProcess)) {
                    log.info("主叫号码:{},400号码:{}CPCC主线程开始！", call.getCaller(), user.getNumber());
                    new CpccProcessThread(call, user, CtiThreadListener.INSTANCE, config).start();
                } else if(user.isBaiduAifanfan()) {
                    log.info("主叫号码:{},400号码:{}百度主线程开始！", call.getCaller(), user.getNumber());
                    new BaiduProcessThread(call, user, CtiThreadListener.INSTANCE, config).start();
                } else {
                    //默认流程
                    boolean defaultThread = true;
                    String chargeTime = service.getFunctionData(user.getNumber(), FunctionSwitchConstants.MISSED_CALL_CHARGE_TIME);
                    if(StringUtils.isNotEmpty(chargeTime)) {
                        log.info("用户配置了未接不扣费失效时段：{}，在此时段内走默认流程，此时段外走放音流程！", chargeTime);
                        String[] timeArray = chargeTime.split("-");
                        String beginTime = timeArray[0].length() == 1 ? "0" + timeArray[0] : timeArray[0];
                        String endTime = timeArray[1].length() == 1 ? "0" + timeArray[1] : timeArray[1];
                        beginTime = beginTime + ":00";
                        endTime = endTime + ":00";
                        String nowTime = DateTimeUtil.formatShortTime(now);
                        defaultThread = DateTimeUtil.satisfyTime(nowTime, beginTime, endTime);
                    }
                    if(defaultThread) {
                        log.info("主叫号码:{},400号码:{}主线程开始！", call.getCaller(), user.getNumber());
                        new MainProcessThread(call, user, CtiThreadListener.INSTANCE, config).start();
                    } else {
                        log.info("主叫号码:{},400号码:{}不扣费放音流程开始！", call.getCaller(), user.getNumber());
                        new NoChargeProcessThread(user, service, call).start();
                    }
                }
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }
    
    @Override
    public void callEnd(Call call) {
        log.debug("挂机事件：ano={},bno={},deviceID={},callLsh={}", call.getCaller(), call.getCallee(), call.getDeviceId(),
            call.getLsh());
        try {
			call.stopReceiveDTMF();
            String callType = (String) call.getParam(MidwareConstants.CALL_TYPE);
            log.debug("挂机的callType is : {}", callType);
            if(callType == null) {
                TimeUnit.MILLISECONDS.sleep(500);
                callType = (String) call.getParam(MidwareConstants.CALL_TYPE);
                log.debug("等待500毫秒后，挂机的callType is : {}", callType);
            }
            EventHandler handler = getCallEventHandler(call);
            if (null != callType) switch (callType) {
				case MidwareConstants.CALL_TYPE_CALLER:
					handler.callerOnhook();
					break;
				case MidwareConstants.CALL_TYPE_CALLEE:
					handler.calleeOnhook();
					break;
				case MidwareConstants.CALL_TYPE_TRANS:
					handler.transOnhook(call);
					break;
				default:
					break;
			}
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    @Override
    public void asyncFinished(BaseEvent be) {
        log.debug("全局异步结束事件：eventType={},eventState={},deviceID={},callLsh={},threadName={}", be.getType(),
            be.getEventState(), be.getDeviceID(), be.getLsh(), be.getThreadName());
        try {
            if (be.getType().equals(EventType.CALLOUT)) {
                Call call = be.getCall();
                if (call == null) {
                    TimeUnit.MILLISECONDS.sleep(500);
                    call = be.getCall();
                }
                if (call != null) {
                    EventHandler handler = getCallEventHandler(call);
					if(handler != null) {
						int state = be.getEventState();
						handler.callOver(state, be.getErrorCode());
						if (state != Constants.RET_SUCCESS) {
							call.setParam(MAIN_CALL_THREAD, null);
						}
					}
                }
            } else if (be.getType().equals(EventType.RECEIVEDTMF)) {
                Call call = be.getCall();
                ReceiveDTMFEvent dtmfEvent = (ReceiveDTMFEvent) be;
                String key = dtmfEvent.getDtmf();
                log.debug("收到的dtmf码为：{}", key);
                Object calleeReceiveTime = call.getParam(RECEIVED_DTMF_TIME);
                if(calleeReceiveTime == null) {
                    //主叫按键
                    EventHandler handler = getCallEventHandler(call);
                    handler.ivrReceived(key);
                } else {
                    //被叫按键
                    long receivedDtmfTime = (long) calleeReceiveTime;
                    long currentTime = System.currentTimeMillis();
                    StringBuffer sb = (StringBuffer) call.getParam(RECEIVED_DTMF);
                    if (receivedDtmfTime == 0) {
                        call.setParam(RECEIVED_DTMF_TIME, currentTime);
                        sb.append(key);
                    } else {
                        long diffTime = currentTime - receivedDtmfTime;
                        log.debug("距上次收到的dtmf码的间隔时间为：{}/秒", (diffTime / 1000));
                        long intervalTime = SpringHelper.getBean(MidwareSettings.class).getDtmfIntervalTime();
                        call.setParam(RECEIVED_DTMF_TIME, currentTime);
                        if (diffTime > intervalTime) {
                            sb = new StringBuffer();
                            if ("*".equals(key)) {
                                log.debug("距上次收到dtmf码超过间隔时间,坐席按键是*,保留当前按键");
                                call.setParam(RECEIVED_DTMF, sb.append(key));
                            } else {
                                log.debug("距上次收到dtmf码超过间隔时间,坐席按键不是*,重置按键值");
                                call.setParam(RECEIVED_DTMF, sb);
                            }
                        } else {
                            sb.append(key);
                        }
                    }
                    String code = sb.toString();
                    if (code.equals(SpringHelper.getBean(MidwareSettings.class).getDtmfTransKey())) {
                        log.debug("收到转移电话指令：{}", code);
                        call.stopReceiveDTMF();
                        sb.delete(0, sb.length());
                        EventHandler handler = getCallEventHandler(call);
                        handler.dtmfTrans();
                    } else if(code.equals(SpringHelper.getBean(MidwareSettings.class).getDtmfIvrKey())) {
                        log.debug("收到转移到导航指令：{}", code);
                        call.stopReceiveDTMF();
                        sb.delete(0, sb.length());
                        EventHandler handler = getCallEventHandler(call);
                        handler.dtmfToIvr();
                    } else {
                        log.warn("不明确的按键指令：{}", code);
                    }
                }
            } else if(be.getType().equals(EventType.CDR)) {
                Call call = be.getCall();
                if(call != null) {
                    CdrEvent cdr = (CdrEvent) be;
                    EventHandler handler = (EventHandler) call.getParam(MAIN_CALL_THREAD);
                    if(handler != null) handler.cdr(cdr.getRecord());
                }
            } else if(be.getType().equals(EventType.DETECTSPEECH)) {
                Call call = be.getCall();
                if(call != null) {
                    DetectSpeechEvent speech = (DetectSpeechEvent) be;
                    if(speech.getEventState() == 1 && StringUtils.isNotEmpty(speech.getText())) {
                        EventHandler handler = (EventHandler) call.getParam(MAIN_CALL_THREAD);
                        if(handler != null) handler.detectSpeech(speech.getText());
                    }
                }
            }
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    private String fixCaller(String caller) {
        CommonSettings settings = SpringHelper.getBean(CommonSettings.class);
        if (StringUtils.isNotEmpty(settings.getCallerCheckIgnore())) {
            log.info("当前配置了区号截取：{}", settings.getCallerCheckIgnore());
            for (String no : settings.getCallerCheckIgnore().split(",")) {
                if (caller.startsWith(no + no)) {
                    String fixedCaller = caller.substring(no.length());
                    log.debug("截取后的主叫号码：{}", fixedCaller);
                    return fixedCaller;
                } else if(caller.startsWith(no) && MyStringUtil.isMobile(caller.substring(no.length()))) {
                    String fixedCaller = "0" + caller.substring(no.length());
                    log.debug("截取后的主叫号码：{}", fixedCaller);
                    return fixedCaller;
                }
            }
        }
        //处理00551特殊情况
        if (caller.startsWith("00551")) {
            String fixedCaller = caller.substring(1);
            return fixedCaller;
        }
        return null;
    }

    
    private boolean judgeCaller(String caller, User user, NumberFuncConfig config) {
//		CommonSettings settings = SpringHelper.getBean(CommonSettings.class);
		BaseService service = SpringHelper.getBean(BaseService.class);
//		String []irgnors = settings.getIgnorCheckCallerArray();
//		if(ArrayUtils.contains(irgnors, caller)) {
//			log.debug("主叫号码：{} 配置了忽略验证，验证通过...", caller);
//			return true;
//		}
		List<String> globalList = service.findMainCallByNumber("global");
		if(globalList.contains(caller)) {
			log.debug("主叫号码：{} 配置了全局忽略验证，验证通过...", caller);
			return true;
		}
		if(user != null) {
			List<String> mainCallList = service.findMainCallByNumber(user.getNumber());
			if(mainCallList.contains(caller)) {
				log.debug("主叫号码：{} 配置了对应400号码：{}忽略验证，验证通过...", caller, user.getNumber());
				return true;
			}
		}
        if (config != null && config.containsShortNum(caller)) {
            log.debug("号码配置了主叫号码：{}允许呼入，验证通过...", caller);
			return true;
        }
		//log.debug("开始处理主叫号码验证：{}", caller);
        //        
        //        log.debug("判断是否是0086开头并且不是00860开头");
        //        if(caller.startsWith("0086") && caller.charAt(4) != '0') {
        //            caller = '0' + caller.substring(4);
        //            log.debug("处理后的主叫为：{}" + caller);
        //        }
        String origCaller = caller;
        if (caller.length() > 12) {
            log.warn("主叫号码:{}大于12位！终止呼叫！", caller);
            return false;
        }
        //主叫号码长度10或12位且不以0开头
        if ((caller.length() == 10 || caller.length() == 12) && !caller.startsWith("0")) {
            log.warn("主叫号码：{}长度10位或12位且不以0开头！终止呼叫！", caller);
            return false;
        }
        //主叫号码长度11位且不以0或1开头
        if (caller.length() == 11 && !(caller.startsWith("0") || caller.startsWith("1"))) {
            log.warn("主叫号码：{}长度11位且不以0或1开头！终止呼叫！", caller);
            return false;
        }
        boolean endWithProvider = caller.endsWith("10000") || caller.endsWith("10086") || caller.endsWith("10010")
            || caller.endsWith("10050");
        //主叫号码长度等于9位且不是100XX结尾拦截
        if (caller.length() == 9 && !endWithProvider) {
            log.warn("主叫号码：{}长度等于9位！终止呼叫！", caller);
            return false;
        }
        if(caller.length() == 8 && caller.startsWith("1010")) {
            log.warn("主叫号码：{}长度8位且以1010开头！终止呼叫！", caller);
            return false;
        }
        //主叫号码长度8位且以0开头且不是100XX结尾拦截
        if (caller.length() == 8 && caller.startsWith("0") && !endWithProvider) {
            log.warn("主叫号码：{}长度8位且以0开头！终止呼叫！", caller);
            return false;
        }
        //主叫号码长度7位且以0开头拦截
        if (caller.length() == 7 && caller.startsWith("0")) {
            log.warn("主叫号码：{}长度7位且以0开头！终止呼叫！", caller);
            return false;
        }

        if ((caller.startsWith("86") && caller.length() > 8)) {
            log.warn("主叫号码:{}以86开头！终止呼叫！", caller);
            return false;
        }
        if (caller.startsWith("400") || caller.startsWith("800") || caller.startsWith("95")) {
            log.warn("主叫号码:{}以400或800或95开头！终止呼叫！", caller);
            return false;
        }
        if (!StringUtils.isNumeric(caller)) {
            log.warn("主叫号码:{}包含字母！终止呼叫！", caller);
            return false;
        }
//        String[] accessShortNum = settings.getAccessShortNumArray();
        //先处理运营商号码，把区号截取
        if (endWithProvider && caller.length() < 10) {
            caller = caller.substring(caller.indexOf("100"));
        }
        if (caller.length() < 7) {
            log.warn("主叫号码:{}小于7位！终止呼叫！", caller);
            return false;
        }

        //全局黑名单
        boolean inGloable = service.inGlobalBlack(origCaller);
        if (inGloable) {
            log.warn("主叫号码:{}未通过全局黑名单！终止呼叫！", origCaller);
            return false;
        }
        return true;
    }

    private boolean judgeCallerForeign(String num) {
        CommonSettings settings = SpringHelper.getBean(CommonSettings.class);
        String foreignNum = settings.getForeignPrefixNum();
        return foreignNum != null && foreignNum.contains(num);
    }
	
	
	private EventHandler getCallEventHandler(Call call) throws InterruptedException {
		EventHandler handler = (EventHandler) call.getParam(MAIN_CALL_THREAD);
		if (handler == null) {
			TimeUnit.MILLISECONDS.sleep(500);
			handler = (EventHandler) call.getParam(MAIN_CALL_THREAD);
		}
		return handler;
	}
	
	private String saveTalkNote(ProcessService service, User user, String caller, int talkType) {
		TalkNote talkNote = new TalkNote(user.getNumber(), user.getOriginalNo(), caller, new Date());
		talkNote.setTalkType(talkType);
        talkNote.setErrorCode(CallOutErrorCode.FORBID);
		service.getBusinessService().saveOrUpdate(talkNote);
		service.getBusinessService().saveTalkTask(new OutDataTask(talkNote.getId(), OutDataType.TALK));
		return talkNote.getId();
	}
   
    private String saveWhiteListTalkNote(ProcessService service, TalkNote talkNote) {
        talkNote.setCallerOnHook(true);
        talkNote.setOnhookTime(new Date());
        service.getBusinessService().saveOrUpdate(talkNote);
        service.getBusinessService().saveTalkTask(new OutDataTask(talkNote.getId(), OutDataType.TALK));
        return talkNote.getId();
    }
}
