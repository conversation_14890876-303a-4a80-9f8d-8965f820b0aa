/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.midware.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.FutureTask;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.sungoin.netphoneLocal.business.po.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.sungoin.netphoneLocal.business.bean.AgentStatusEnum;
import com.sungoin.netphoneLocal.business.bean.RingAutoNotifyInfoDto;
import com.sungoin.netphoneLocal.business.bean.TalkNoteDto;
import com.sungoin.netphoneLocal.business.bean.ZeroCallDto;
import com.sungoin.netphoneLocal.business.service.BaseService;
import com.sungoin.netphoneLocal.config.CommonSettings;
import com.sungoin.netphoneLocal.midware.service.outcall.*;
import com.sungoin.netphoneLocal.midware.service.MainProcessThread;
import com.sungoin.netphoneLocal.midware.service.ProcessService;
import com.sungoin.netphoneLocal.socket.message.AgentMessage;
import com.sungoin.netphoneLocal.socket.message.SocketMessage;
import com.sungoin.netphoneLocal.util.CacheUtil;
import com.sungoin.netphoneLocal.util.ConcurrentHelper;
import com.sungoin.netphoneLocal.util.HttpHelper;
import com.sungoin.netphoneLocal.util.JsonHelper;

import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR> 2015-8-12
 */
@RestController
@RequestMapping(value = "/branch/service")
public class MidwareController {

    private static final Logger log = LoggerFactory.getLogger(MidwareController.class);
    private static final String RESULT_SUCCESS = "1";
    private static final String RESULT_FAIL = "0";
    
    @Resource
    CommonSettings settings;

    @Resource
    ProcessService service;

    @Resource
    BaseService baseService;
	
	@Resource
	CommonSettings commonSetting;
    
    /**
     * 保存基本配置
     * @param dto
     * @return 
     */
    @RequestMapping(method = RequestMethod.POST, value = "/base/config")
    public String updateBaseConfig(@RequestBody BaseConfigDto dto) {
        this.baseService.updateBaseConfig(dto);
        return RESULT_SUCCESS;
    }

    /**
     * 获取坐席状态
     *
     * @param customerNo 400号码
     * @param agentId 坐席ID
     * @param lsh 绑定号码流水号
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "/agent/getstatus/{customerNo}/{agentId}/{lsh}")
    public String getAgentStatus(@PathVariable String customerNo, @PathVariable String agentId, @PathVariable String lsh) {
        return this.service.getAgentStatus(customerNo, lsh, agentId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/agent/getdeptqueuecount/{customerNo}")
    public String getDeptqueueCount(@PathVariable String customerNo) {

        String data = JsonHelper.Object2Json(this.service.getDeptQueueSize(customerNo));
        Map<String, String> resultmap = new HashMap<String, String>();
        String result = "";
        try {
            resultmap.put("success", "1");
            resultmap.put("data", data);
            resultmap.put("msg", "得到部门排队人数成功");
            result = JsonHelper.Object2Json(resultmap);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * Gets the all agent status.
     *
     * @param customerNo the customer no
     * @return the all agent status
     */
    @RequestMapping(method = RequestMethod.GET, value = "/agent/getAllAgentStatus/{customerNo}")
    public String getAllAgentStatus(@PathVariable String customerNo) {
        String data = JsonHelper.Object2Json(this.service.getAllAgentState(customerNo));
        Map<String, String> resultmap = new HashMap<String, String>();
        String result = "";
        try {
            resultmap.put("success", "1");
            resultmap.put("data", data);
            resultmap.put("msg", "得到用户所有坐席的状态成功");
            result = JsonHelper.Object2Json(resultmap);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    @RequestMapping(method = RequestMethod.POST, value = "/agent/getAllAgentStatusAndQueueCount/{customerNo}")
    public String getAllAgentStatusAndQueueCount(@PathVariable String customerNo) {
        List<Object[]> queueList = this.service.getDeptQueueSize(customerNo);
        List<Object[]> agentList = this.service.getAllAgentState(customerNo);
        Map<String, List<Object[]>> map = new HashMap<String, List<Object[]>>();
        map.put("queuecount", queueList);
        map.put("agentstatus", agentList);
        String data = JsonHelper.Object2Json(map);
        Map<String, String> resultmap = new HashMap<String, String>();
        String result = "";
        try {
            resultmap.put("success", "1");
            resultmap.put("data", data);
            resultmap.put("msg", "得到用户所有坐席的状态和部门排队人数成功");
            result = JsonHelper.Object2Json(resultmap);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 更新坐席状态.
     *
     * @param customerNo the customer no
     * @param agentId the agent id
     * @param lsh the lsh
     * @param status the status
     * @return the string
     */
    @RequestMapping(method = RequestMethod.POST, value = "/agent/setstatus/{customerNo}/{agentId}/{lsh}/{status}")
    public String updateAgentStatus(@PathVariable final String customerNo, @PathVariable final String agentId,
        @PathVariable final String lsh, @PathVariable final String status) {
        log.info("中心端更新坐席状态：customerNo" + customerNo + ",agentId=" + agentId + ",lsh=" + lsh + ",status=" + status);
        BindPhone bp = this.service.getLocalBindPhone(customerNo, lsh);
        String agentState = this.service.getAgentState(bp);
        User user = this.baseService.getUserByNumber(customerNo);
        log.info("agentId:{}数据库当前坐席状态:{}", agentId, agentState);
        if ((user.getFinishingTime() == 0) && AgentStatusEnum.CONNECT.getValue().equals(agentState)) {
			log.warn("忽略中心端更新状态！");
            return RESULT_SUCCESS;
        }
        if (status.indexOf(",") > 0) {
            String data[] = status.split(",");
            String agentStatus = data[0];
            String agentType = data[1];
            if (this.settings.isMainServer()) {
                if (this.isStateIllegal(agentStatus)) {
                    int count = this.service.controllerUpdateAgentStateAndNeedlogin(customerNo, lsh, agentStatus, "1".equals(agentType));
                    log.info("更新坐席状态条数：{}", count);
                    if(count == 0) {
                        this.baseService.modifyAgentNeedlogin(bp.getId(), "1".equals(agentType));
                    }
                } else {
                    this.service.controllerUpdateAgentNeedlogin(customerNo, lsh, "1".equals(agentType));
                }
            } else {
                //备用机只更新needLogin状态
                log.info("备用机只更新needLogin状态");
                this.baseService.modifyAgentNeedlogin(bp.getId(), "1".equals(agentType));
            }
        } else {
            String realStatus = status;
            if (status.contains("@")) {
                //移除缓存
                log.info("中心端发来整理结束标识，清除保护缓存！key={}", bp.getId());
                CacheUtil.remove(CacheUtil.CACHE_AGENT_STATE_NAME, bp.getId());
                realStatus = status.substring(0, status.indexOf("@"));
            }
            if (this.settings.isMainServer()) {
                if (this.isStateIllegal(realStatus)) {
                    this.service.controllerUpdateAgentState(customerNo, lsh, realStatus);
                }
            }
        }
        //将请求转发到备份机器，清除备份机的缓存
        if (this.settings.isMainServer() && StringUtils.isNotEmpty(this.settings.getBackupServerEndpoint())) {
            final String[] backEndpoints = this.settings.getBackupServers();
            ConcurrentHelper.doInBackground(() -> {
				for (String endpoint : backEndpoints) {
					HttpHelper.SendPostRequest(endpoint + "setstatus/" + customerNo + "/" + agentId + "/" + lsh
							+ "/" + status, null);
				}
			});

        }
        return RESULT_SUCCESS;
    }
    
    /**
     * 获取坐席状态(新版)
     *
     * @param customerNo 400号码
     * @param agentId 坐席ID
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/agent/getstatusnew/{customerNo}/{agentId}")
    public String getAgentStatusNew(@PathVariable String customerNo, @PathVariable String agentId) {
        BindPhone bp = this.service.getBindPhoneByCustomerNoAndAgentId(customerNo, agentId);
        if (bp == null) {
            log.warn("根据坐席ID：{}，未找到绑定号码！", agentId);
            return RESULT_SUCCESS;
        }
        SocketMessage message = new AgentMessage(bp.getCustomerNo(), bp.getAgentId(),
                bp.getLsh(), bp.getStatus(), bp.getOrigBindPhoneNo(), bp.isNeedLogin());
        HttpHelper.postPlatformClient(commonSetting.getPlatformClientEndpoint() + "sendAgentMessage", message);
        return RESULT_SUCCESS;
    }

    /**
     * 更新坐席状态（新版）
     * @param customerNo
     * @param agentId
     * @param status
     * @param needLogin
     * @return 
     */
    @RequestMapping(method = RequestMethod.POST, value = "/agent/setstatusnew/{customerNo}/{agentId}/{status}/{needLogin}")
    public String updateAgentStatusNew(@PathVariable final String customerNo, @PathVariable final String agentId, @PathVariable final String status, @PathVariable final String needLogin) {
        log.info("中心端更新坐席状态：customerNo" + customerNo + ",agentId=" + agentId + ",status=" + status + ",needLogin=" + needLogin);
        BindPhone bp = this.service.getBindPhoneByCustomerNoAndAgentId(customerNo, agentId);
        String result = RESULT_FAIL;
        if(bp == null) {
            log.warn("{}：未找到绑定号码！", agentId);
            return result;
        }
        if (this.settings.isMainServer()) {
            String agentState = this.service.getAgentStatusWithMultiple(bp);
            log.info("agentId:{}数据库当前坐席状态:{}", agentId, agentState);
            if(agentState.startsWith("order")) {
                log.warn("坐席在整理状态中，终止整理任务！");
                this.stopOrder(customerNo, bp.getAgentId());
            }
            boolean isNeedLogin = "1".equals(needLogin);
            if (agentState.startsWith(AgentStatusEnum.CALLING.getValue()) || AgentStatusEnum.CONNECT.getValue().equals(agentState)) {
                log.info("电话在呼叫中或接通中，只更新needLogin！");
                this.baseService.modifyAgentNeedlogin(bp.getId(), isNeedLogin);
            } else {
                int count = this.service.controllerUpdateAgentStateAndNeedlogin(customerNo, bp.getLsh(), status, isNeedLogin);
                log.info("更新坐席状态条数：{}", count);
                if(count == 0) {
                    this.baseService.modifyAgentNeedlogin(bp.getId(), isNeedLogin);
                }
                result=RESULT_SUCCESS;
            }
        } else {
            //备用机只更新needLogin状态
            log.info("备用机只更新needLogin状态");
            this.baseService.modifyAgentNeedlogin(bp.getId(), "1".equals(needLogin));
        }
        //将请求转发到备份机器，设置needLogin
        if (this.settings.isMainServer() && StringUtils.isNotEmpty(this.settings.getBackupServerEndpoint())) {
            final String[] backEndpoints = this.settings.getBackupServers();
            ConcurrentHelper.doInBackground(() -> {
				for (String endpoint : backEndpoints) {
					HttpHelper.SendPostRequest(endpoint + "setstatusnew/" + customerNo + "/" + agentId + "/" + status + "/" + needLogin, null);
				}
			});
        }
        return result;
    }
    
    /**
     * 转移（新版）
     *
     * @param customerNo the customer no
     * @param lsh the lsh
     * @param transferAgentId the transfer agent id
     * @return the string
     */
    @RequestMapping(method = RequestMethod.POST, value = "/agent/transfernew/{customerNo}/{lsh}/{transferAgentId}")
    public String transferAgentNew(@PathVariable final String customerNo, @PathVariable final String lsh, @PathVariable final String transferAgentId) {
        BindPhone bindPhone = this.service.getLocalBindPhone(customerNo, lsh);
        BindPhone transBindPhone = this.service.getBindPhoneByCustomerNoAndAgentId(customerNo, transferAgentId);
        if(transBindPhone == null) {
            log.warn("未找到转移到绑定号码！忽略操作费！");
            return RESULT_SUCCESS;
        }
        final MainProcessThread process = (MainProcessThread) CacheUtil.get(CacheUtil.CACHE_CALL_PROCESS_NAME,
            customerNo + "_" + bindPhone.getId());
        if (this.settings.isMainServer() && process == null
            && StringUtils.isNotEmpty(this.settings.getBackupServerEndpoint())) {
            log.info("当前通话不在主服务器上，转发到备份服务器！");
            final String[] backEndpoints = this.settings.getBackupServers();
            ConcurrentHelper.doInBackground(() -> {
				for (String endpoint : backEndpoints) {
					HttpHelper.SendPostRequest(endpoint + "transfernew/" + customerNo + "/" + lsh + "/" + transferAgentId, null);
				}
			});
            return RESULT_SUCCESS;
        }
        log.info("enter transferAgent customerNo=" + customerNo +  " lsh=" + lsh + " transferAgentId=" + transferAgentId);
        
        ConcurrentHelper.doInBackground(() -> {
			if (process != null) {
				process.transCall(transBindPhone.getLsh(), true, true);
			} else {
				log.warn("找不到通话流程！转移操作被忽略！");
			}
		});
        return RESULT_SUCCESS;
    }

    /**
     * 挂断.
     *
     * @param customerNo the customer no
     * @param agentId the agent id
     * @param lsh the lsh
     * @return the string
     */
    @RequestMapping(method = RequestMethod.POST, value = "/agent/hangup/{customerNo}/{agentId}/{lsh}")
    public String hangupAgent(@PathVariable final String customerNo, @PathVariable final String agentId,
        @PathVariable final String lsh) {
        BindPhone bindPhone = this.service.getLocalBindPhone(customerNo, lsh);
        final MainProcessThread process = (MainProcessThread) CacheUtil.get(CacheUtil.CACHE_CALL_PROCESS_NAME,
            customerNo + "_" + bindPhone.getId());
        if (this.settings.isMainServer() && process == null
            && StringUtils.isNotEmpty(this.settings.getBackupServerEndpoint())) {
            log.info("当前通话不在主服务器上，转发到备份服务器！");
            final String[] backEndpoints = this.settings.getBackupServers();
            ConcurrentHelper.doInBackground(() -> {
				for (String endpoint : backEndpoints) {
					HttpHelper.SendPostRequest(endpoint + "hangup/" + customerNo + "/" + agentId + "/" + lsh, null);
				}
			});

            return RESULT_SUCCESS;
        }
        log.info("enter hangupAgent customerNo=" + customerNo + " agentId=" + agentId + " lsh=" + lsh);
        ConcurrentHelper.doInBackground(() -> {
			if (process != null) {
				process.handupAgent();
			} else {
				log.warn("找不到通话流程！挂断操作被忽略！");
			}
		});
        return RESULT_SUCCESS;
    }
    
    /**
     * 挂断（新版）
     *
     * @param customerNo the customer no
     * @param lsh the lsh
     * @return the string
     */
    @RequestMapping(method = RequestMethod.POST, value = "/agent/hangupnew/{customerNo}/{lsh}")
    public String hangupAgentNew(@PathVariable final String customerNo, @PathVariable final String lsh) {
        BindPhone bindPhone = this.service.getLocalBindPhone(customerNo, lsh);
        final MainProcessThread process = (MainProcessThread) CacheUtil.get(CacheUtil.CACHE_CALL_PROCESS_NAME,
            customerNo + "_" + bindPhone.getId());
        if (this.settings.isMainServer() && process == null
            && StringUtils.isNotEmpty(this.settings.getBackupServerEndpoint())) {
            log.info("当前通话不在主服务器上，转发到备份服务器！");
            final String[] backEndpoints = this.settings.getBackupServers();
            ConcurrentHelper.doInBackground(() -> {
				for (String endpoint : backEndpoints) {
					HttpHelper.SendPostRequest(endpoint + "hangupnew/" + customerNo + "/" + lsh, null);
				}
			});

            return RESULT_SUCCESS;
        }
        log.info("enter hangupAgent customerNo=" + customerNo + " lsh=" + lsh);
        ConcurrentHelper.doInBackground(() -> {
			if (process != null) {
				process.handupAgent();
			} else {
				log.warn("找不到通话流程！挂断操作被忽略！");
			}
		});
        return RESULT_SUCCESS;
    }

    /**
     * 保持
     *
     * @param customerNo the customer no
     * @param agentId the agent id
     * @param lsh the lsh
     * @return the string
     */
    @RequestMapping(method = RequestMethod.POST, value = "/agent/callhold/{customerNo}/{agentId}/{lsh}")
    public String callholdAgent(@PathVariable final String customerNo, @PathVariable final String agentId,
        @PathVariable final String lsh) {
        BindPhone bindPhone = this.service.getLocalBindPhone(customerNo, lsh);
        final MainProcessThread process = (MainProcessThread) CacheUtil.get(CacheUtil.CACHE_CALL_PROCESS_NAME,
            customerNo + "_" + bindPhone.getId());
        if (this.settings.isMainServer() && process == null
            && StringUtils.isNotEmpty(this.settings.getBackupServerEndpoint())) {
            log.info("当前通话不在主服务器上，转发到备份服务器！");
            final String[] backEndpoints = this.settings.getBackupServers();
            ConcurrentHelper.doInBackground(() -> {
				for (String endpoint : backEndpoints) {
					HttpHelper.SendPostRequest(endpoint + "callhold/" + customerNo + "/" + agentId + "/" + lsh,
							null);
				}
			});

            return RESULT_SUCCESS;
        }
        log.info("enter callholdAgent customerNo=" + customerNo + " agentId=" + agentId + " lsh=" + lsh);
        ConcurrentHelper.doInBackground(() -> {
			if (process != null) {
				process.callholdAgent();
			} else {
				log.warn("找不到通话流程！保持操作被忽略！");
			}
		});
        return RESULT_SUCCESS;
    }

    /**
     * 接回.
     *
     * @param customerNo the customer no
     * @param agentId the agent id
     * @param lsh the lsh
     * @return the string
     */
    @RequestMapping(method = RequestMethod.POST, value = "/agent/callback/{customerNo}/{agentId}/{lsh}")
    public String callbackAgent(@PathVariable final String customerNo, @PathVariable final String agentId,
        @PathVariable final String lsh) {
        BindPhone bindPhone = this.service.getLocalBindPhone(customerNo, lsh);
        final MainProcessThread process = (MainProcessThread) CacheUtil.get(CacheUtil.CACHE_CALL_PROCESS_NAME,
            customerNo + "_" + bindPhone.getId());
        if (this.settings.isMainServer() && process == null
            && StringUtils.isNotEmpty(this.settings.getBackupServerEndpoint())) {
            log.info("当前通话不在主服务器上，转发到备份服务器！");
            final String[] backEndpoints = this.settings.getBackupServers();
            ConcurrentHelper.doInBackground(() -> {
				for (String endpoint : backEndpoints) {
					HttpHelper.SendPostRequest(endpoint + "callback/" + customerNo + "/" + agentId + "/" + lsh,
							null);
				}
			});
            return RESULT_SUCCESS;
        }
        log.info("enter callbackAgent customerNo=" + customerNo + " agentId=" + agentId + " lsh=" + lsh);
        ConcurrentHelper.doInBackground(() -> {
			if (process != null) {
				process.callBackAgent();
			} else {
				log.warn("找不到通话流程！接回操作被忽略！");
			}
		});
        return RESULT_SUCCESS;
    }

    /**
     * 转移.
     *
     * @param customerNo the customer no
     * @param agentId the agent id
     * @param lsh the lsh
     * @param transferAgentId the transfer agent id
     * @param transferLsh the transfer lsh
     * @return the string
     */
    @RequestMapping(method = RequestMethod.POST, value = "/agent/transfer/{customerNo}/{agentId}/{lsh}/{transferAgentId}/{transferLsh}")
    public String transferAgent(@PathVariable final String customerNo, @PathVariable final String agentId,
        @PathVariable final String lsh, @PathVariable final String transferAgentId,
        final @PathVariable String transferLsh) {
        BindPhone bindPhone = this.service.getLocalBindPhone(customerNo, lsh);
        final MainProcessThread process = (MainProcessThread) CacheUtil.get(CacheUtil.CACHE_CALL_PROCESS_NAME,
            customerNo + "_" + bindPhone.getId());
        if (this.settings.isMainServer() && process == null
            && StringUtils.isNotEmpty(this.settings.getBackupServerEndpoint())) {
            log.info("当前通话不在主服务器上，转发到备份服务器！");
            final String[] backEndpoints = this.settings.getBackupServers();
            ConcurrentHelper.doInBackground(() -> {
				for (String endpoint : backEndpoints) {
					HttpHelper.SendPostRequest(endpoint + "transfer/" + customerNo + "/" + agentId + "/" + lsh
							+ "/" + transferAgentId + "/" + transferLsh, null);
				}
			});
            return RESULT_SUCCESS;
        }
        log.info("enter transferAgent customerNo=" + customerNo + " agentId=" + agentId + " lsh=" + lsh
            + " transferAgentId=" + transferAgentId + " " + transferLsh);
        ConcurrentHelper.doInBackground(() -> {
			if (process != null) {
				process.transCall(transferLsh, true, true);
			} else {
				log.warn("找不到通话流程！转移操作被忽略！");
			}
		});
        return RESULT_SUCCESS;
    }

    @RequestMapping(method = RequestMethod.POST, value = "/agent/rejectCall/{customerNo}/{lsh}")
    public String rejectCall(@PathVariable final String customerNo, @PathVariable final String lsh) {
        log.info("拒接事件：customerNo=" + customerNo + ", lsh=" + lsh);
        BindPhone bindPhone = this.service.getLocalBindPhone(customerNo, lsh);
        final MainProcessThread process = (MainProcessThread) CacheUtil.get(CacheUtil.CACHE_CALL_PROCESS_NAME,
            customerNo + "_" + bindPhone.getId());
        if (this.settings.isMainServer() && process == null
            && StringUtils.isNotEmpty(this.settings.getBackupServerEndpoint())) {
            log.info("当前通话不在主服务器上，转发到备份服务器！");
            final String[] backEndpoints = this.settings.getBackupServers();
            ConcurrentHelper.doInBackground(() -> {
				for (String endpoint : backEndpoints) {
					HttpHelper.SendPostRequest(endpoint + "rejectCall/" + customerNo + "/" + lsh, null);
				}
			});

            return RESULT_SUCCESS;
        }
        log.info("enter rejectCall customerNo=" + customerNo + " lsh=" + lsh);
        ConcurrentHelper.doInBackground(() -> {
			if (process != null) {
				process.handupAgent();
			} else {
				log.warn("找不到通话流程！拒接操作被忽略！");
			}
		});
        return RESULT_SUCCESS;
    }

    @RequestMapping(method = RequestMethod.POST, value = "/agent/callout/{caller}/{callee}")
    public String callout(@PathVariable String caller, @PathVariable String callee, InterConnection ic) {
        log.info("enter callout caller={} callee={} InterConnection={}", caller, callee, ic);
        try {
            FutureTask<String> task = new FutureTask<>(new CallOutThread(caller, callee, ic));
            new Thread(task).start();
            String result = task.get();
            log.info("外呼线程返回：{}", result);
            return result;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return ex.getMessage();
        } catch (Throwable ex) {
            log.error(ex.getMessage(), ex);
            return ex.getMessage();
        }
    }

    private boolean isStateIllegal(String state) {
        return state.equals("idle") || state.equals("busy") || state.equals("offline");
    }
	
	@RequestMapping(method = RequestMethod.POST, value = "/agent/queueReduce/{customerNo}/{deptLsh}")
    public String callbackAgent(@PathVariable final String customerNo, @PathVariable final String deptLsh) {
		log.info("通知队列排队数减少: customerNo={}, deptLsh={}", customerNo, deptLsh);
		if (this.settings.isMainServer()) {
			Dept dept = this.baseService.getDeptByCustomerNoAndDeptLsh(customerNo, deptLsh);
			this.service.notifyQueueAgentOnhook(dept);
		}
        return RESULT_SUCCESS;
    }
	
	@RequestMapping(method = { RequestMethod.POST }, value = { "/cleanData" })
    @ResponseBody
    String cleanData(final String platform, final String userId) {
        log.debug("中心端二销释放号码,platform:{} userId:{}", platform, userId);
        final Map<String, String> map = new HashMap<String, String>();
        map.put("userId", userId);
        map.put("platform", platform);
        ConcurrentHelper.doInBackground(new Runnable() {
            @Override
            public void run() {
                String reslut = HttpHelper.SendPostRequest(commonSetting.getReleaseCheckUrl(), map);
                log.debug("检查结果:{}", reslut);
                if ("1".equals(reslut)) {
                    baseService.cleanData(userId);
                }
            }
        });
        return RESULT_SUCCESS;
    }
	
	@RequestMapping(method = { RequestMethod.POST }, value = { "/test" })
    @ResponseBody
    public String serviceTest(String test, HttpServletRequest req) {
        log.trace(req.getRemoteAddr());
        return "1";
    }
	
	@RequestMapping(method = RequestMethod.GET, value = "/getUserInfo/{customerNo}")
    @ResponseBody
    public User getUserInfo(@PathVariable String customerNo) {
		User user= baseService.getUserByNumber(customerNo);
		if(user!=null){
			user.setDepts(null);
			user.setInterConns(null);
			user.setBlackList(null);
			user.setWhiteList(null);
		}
		return user;
    }
	
	@RequestMapping(method = RequestMethod.POST, value = "agent/getOffHookCallee/{customerNo}/{deptLsh}/{mainCall}")
    @ResponseBody
    public String getOffHookCallee(@PathVariable String customerNo,@PathVariable String deptLsh,@PathVariable String mainCall) {
		log.debug("记忆功能,customerNo:{} deptLsh:{},mainCall:{}", customerNo, deptLsh,mainCall);
		List<TalkNoteDto> list=baseService.findByCustomerNoAndDeptLshAndMainCall(customerNo, deptLsh, mainCall);
		return JsonHelper.Object2Json(list);
	}
    
    @RequestMapping(method = RequestMethod.POST, value = "agent/autoTestCall/{caller}/{callee}")
    @ResponseBody
    public String autoTestCall(@PathVariable String caller, @PathVariable String callee) {
        log.debug("自动外呼请求，主叫：{}，被叫：{}", caller, callee);
        String fixedCallee = this.service.getFixedCalleeNo(callee);
        if(service.isSipFlag()) {
            //sip平台添加自动通知前缀,以便使用自动通知的网关
            fixedCallee = "T" + callee;
        }
        ConcurrentHelper.doInBackground(new AutoTestCallThread(caller, fixedCallee));
        return RESULT_SUCCESS;
    }
    
    @RequestMapping(method = RequestMethod.POST, value = "/agent/stopOrder/{customerNo}/{agentId}")
    public String stopOrder(@PathVariable final String customerNo, @PathVariable final String agentId) {
        this.service.stopOrder(customerNo + agentId);
        //将请求转发到备份机器
        if (this.settings.isMainServer() && StringUtils.isNotEmpty(this.settings.getBackupServerEndpoint())) {
            final String[] backEndpoints = this.settings.getBackupServers();
            ConcurrentHelper.doInBackground(() -> {
				for (String endpoint : backEndpoints) {
					HttpHelper.SendPostRequest(endpoint + "stopOrder/" + customerNo + "/" + agentId, null);
				}
			});
        }
        return RESULT_SUCCESS;
    }
    
    @RequestMapping(method = RequestMethod.POST, value = "agent/zeroNumberAutoCall")
    @ResponseBody
    public String zeroNumberAutoCall(@RequestBody ZeroCallDto dto) {
        log.debug("零次接通号码自动外呼请求，参数：{}", JsonHelper.Object2Json(dto));
        ConcurrentHelper.doInBackground(new ZeroCallThread(dto, this.service));
        return RESULT_SUCCESS;
    }
    
    @RequestMapping(method = RequestMethod.POST, value = "agent/autoNotify")
    @ResponseBody
    public String autoNotify(@RequestBody RingAutoNotifyInfoDto dto) {
        log.debug("号码通知自动外呼请求，参数：{}", JsonHelper.Object2Json(dto));
        ConcurrentHelper.doInBackground(new AutoNotifyThread(dto, this.service));
        return RESULT_SUCCESS;
    }
    
    @RequestMapping(method = RequestMethod.POST, value = "agent/outcall")
    @ResponseBody
    public String outcall(@RequestBody ZeroCallDto dto) {
        log.debug("呼死你外呼请求，参数：{}", JsonHelper.Object2Json(dto));
        boolean flag = new OutCallRequest(dto, this.service).outcall();
        return flag ? RESULT_SUCCESS : RESULT_FAIL;
    }

    @RequestMapping(method = RequestMethod.POST, value = "agent/ringOutcall")
    @ResponseBody
    public String ringOutcall(@RequestBody RingAutoTestDto dto) {
        log.debug("自动彩铃外呼请求，参数：{}", JsonHelper.Object2Json(dto));
        List<ColorRing> list = this.service.findAllColorRingByCustomerAndLsh(dto.getNumberCode(), dto.getRingLsh().toString());
        if(list.isEmpty()) {
            return RESULT_FAIL;
        }
        ConcurrentHelper.doInBackground(new RingCallOutThread(dto, this.service, list.get(0)));
        return RESULT_SUCCESS;
    }
}
