/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.midware.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

import com.sungoin.netphoneLocal.midware.MidwareConstants;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2015-7-29
 */
@Configuration
@ConfigurationProperties(prefix = "", ignoreInvalidFields = true)
@PropertySource("classpath:conf/midware.properties")
public class MidwareSettings {

    private String platformEndpoint;
    private String platformSocketIp;
    private int platformSocketPort;
    private int httpRepeatCount;
    private int makecallRetrySeconds;
    private String vocDataPath;
    private boolean ringFileCheck;
    private int ivrTimeoutSeconds;
    private int ivrRetryCount;
    private int voiceboxMaxSeconds;
    private String serviceCalledOnhook;
    private String serviceCalledOffhook;
    private String serviceMissedCall;
    private String dtmfTransKey;
    private int agentAlertRingTime;
    private int timeOutInputCount;
    private boolean callTypeTrans;

    private String sktSocketIp;
    private int sktSocketPort;

    private int maxDeptCount = 20;

    private long dtmfIntervalTime = 10;
    private String supportIvrKey = "000";
    
    private boolean sipFlag;
    private String sipVoiceboxPath;
    
    private String dtmfIvrKey = "1*";

    public String getPlatformEndpoint() {
        return this.platformEndpoint;
    }

    public void setPlatformEndpoint(String platformEndpoint) {
        this.platformEndpoint = platformEndpoint;
    }

    public int getHttpRepeatCount() {
        return this.httpRepeatCount;
    }

    public void setHttpRepeatCount(int httpRepeatCount) {
        this.httpRepeatCount = httpRepeatCount;
    }

    public int getMakecallRetrySeconds() {
        return this.makecallRetrySeconds;
    }

    public void setMakecallRetrySeconds(int makecallRetrySeconds) {
        this.makecallRetrySeconds = makecallRetrySeconds;
    }

    public String getVocDataPath() {
        return this.vocDataPath;
    }

    public void setVocDataPath(String vocDataPath) {
        this.vocDataPath = vocDataPath;
    }
    
    public String getCustomRingPath() {
        return this.getVoicesPath() + "/" + MidwareConstants.DATA_PATH_CUSTOM;
    }
    
    public String getCustomRingFile(String fileName) {
        return this.getCustomRingPath() + "/" + fileName;
    }
    
    public String getDefaultRingPath() {
        return this.getVoicesPath() + "/" + MidwareConstants.DATA_PATH_DEFAULT;
    }

    public String getDefaultRingFile(String fileName) {
        return this.getDefaultRingPath() + "/" + fileName;
    }
	
	public String getQueueNumFile(int num) {
        return this.getDefaultRingFile(MidwareConstants.QUEUE_NUM_PREFIX + num + ".wav");
    }

    public String getVoicesPath() {
        return this.vocDataPath + "/voices";
    }

    public String getRingPath() {
        return this.getVoicesPath() + "/" + MidwareConstants.DATA_PATH_RINGS;
    }

    public String getRecordPath() {
        return this.getVoicesPath() + "/" + MidwareConstants.DATA_PATH_RECORDS;
    }

    public String getBoxPath() {
        return this.getVoicesPath() + "/" + MidwareConstants.DATA_PATH_BOXES;
    }

    public boolean isRingFileCheck() {
        return this.ringFileCheck;
    }

    public void setRingFileCheck(boolean ringFileCheck) {
        this.ringFileCheck = ringFileCheck;
    }

    public int getIvrTimeoutSeconds() {
        return this.ivrTimeoutSeconds;
    }

    public void setIvrTimeoutSeconds(int ivrTimeoutSeconds) {
        this.ivrTimeoutSeconds = ivrTimeoutSeconds;
    }

    public int getVoiceboxMaxSeconds() {
        return this.voiceboxMaxSeconds;
    }

    public void setVoiceboxMaxSeconds(int voiceboxMaxSeconds) {
        this.voiceboxMaxSeconds = voiceboxMaxSeconds;
    }

    public String getPlatformSocketIp() {
        return this.platformSocketIp;
    }

    public void setPlatformSocketIp(String platformSocketIp) {
        this.platformSocketIp = platformSocketIp;
    }

    public int getPlatformSocketPort() {
        return this.platformSocketPort;
    }

    public void setPlatformSocketPort(int platformSocketPort) {
        this.platformSocketPort = platformSocketPort;
    }

    public String getServiceCalledOnhook() {
        return this.serviceCalledOnhook;
    }

    public void setServiceCalledOnhook(String serviceCalledOnhook) {
        this.serviceCalledOnhook = serviceCalledOnhook;
    }

    public String getServiceCalledOffhook() {
        return this.serviceCalledOffhook;
    }

    public void setServiceCalledOffhook(String serviceCalledOffhook) {
        this.serviceCalledOffhook = serviceCalledOffhook;
    }

    public String getServiceMissedCall() {
        return this.serviceMissedCall;
    }

    public void setServiceMissedCall(String serviceMissedCall) {
        this.serviceMissedCall = serviceMissedCall;
    }

    public int getIvrRetryCount() {
        return this.ivrRetryCount;
    }

    public void setIvrRetryCount(int ivrRetryCount) {
        this.ivrRetryCount = ivrRetryCount;
    }

    public String getDtmfTransKey() {
        return this.dtmfTransKey;
    }

    public void setDtmfTransKey(String dtmfTransKey) {
        this.dtmfTransKey = dtmfTransKey;
    }

    public int getAgentAlertRingTime() {
        return this.agentAlertRingTime;
    }

    public void setAgentAlertRingTime(int agentAlertRingTime) {
        this.agentAlertRingTime = agentAlertRingTime;
    }

    public int getTimeOutInputCount() {
        return this.timeOutInputCount;
    }

    public void setTimeOutInputCount(int timeOutInputCount) {
        this.timeOutInputCount = timeOutInputCount;
    }

    public boolean isCallTypeTrans() {
        return this.callTypeTrans;
    }

    public void setCallTypeTrans(boolean callTypeTrans) {
        this.callTypeTrans = callTypeTrans;
    }

    public String getSktSocketIp() {
        return this.sktSocketIp;
    }

    public void setSktSocketIp(String sktSocketIp) {
        this.sktSocketIp = sktSocketIp;
    }

    public int getSktSocketPort() {
        return this.sktSocketPort;
    }

    public void setSktSocketPort(int sktSocketPort) {
        this.sktSocketPort = sktSocketPort;
    }

    public long getDtmfIntervalTime() {
        return this.dtmfIntervalTime * 1000;
    }

    public void setDtmfIntervalTime(long dtmfIntervalTime) {
        this.dtmfIntervalTime = dtmfIntervalTime;
    }

    public int getMaxDeptCount() {
        return this.maxDeptCount;
    }

    public void setMaxDeptCount(int maxDeptCount) {
        this.maxDeptCount = maxDeptCount;
    }

    public String getSupportIvrKey() {
        return supportIvrKey;
    }

    public void setSupportIvrKey(String serviceIvrKey) {
        this.supportIvrKey = serviceIvrKey;
    }

    public boolean isSipFlag() {
        return sipFlag;
    }

    public void setSipFlag(boolean sipFlag) {
        this.sipFlag = sipFlag;
    }

    public String getSipVoiceboxPath() {
        return sipVoiceboxPath;
    }

    public void setSipVoiceboxPath(String sipVoiceboxPath) {
        this.sipVoiceboxPath = sipVoiceboxPath;
    }

    public String getDtmfIvrKey() {
        return dtmfIvrKey;
    }

    public void setDtmfIvrKey(String dtmfIvrKey) {
        this.dtmfIvrKey = dtmfIvrKey;
    }

}
