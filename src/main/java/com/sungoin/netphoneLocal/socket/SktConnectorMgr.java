/*
 * 
 */
package com.sungoin.netphoneLocal.socket;

import com.sungoin.netphoneLocal.midware.config.MidwareSettings;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.nio.charset.Charset;
import javax.annotation.Resource;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.mina.core.future.ConnectFuture;
import org.apache.mina.core.session.IdleStatus;
import org.apache.mina.filter.codec.ProtocolCodecFilter;
import org.apache.mina.filter.codec.textline.TextLineCodecFactory;
import org.apache.mina.transport.socket.nio.NioSocketConnector;
import org.springframework.stereotype.Service;

// TODO: Auto-generated Javadoc
/**
 * The Class PlatformConnectorMgr.
 * 
 * <AUTHOR> 2014-6-27
 */
@Service
public class SktConnectorMgr {
    /** The Constant log. */
    private static final Log log = LogFactory.getLog(SktConnectorMgr.class);
	
	@Resource
	private MidwareSettings settings;
	

    /** The cf. */
    private ConnectFuture cf = null;

    /** The connector. */
    private NioSocketConnector connector = null;

    /** The is connected. */
    public volatile boolean isConnected = false;

    /** The terminated. */
    public volatile boolean terminated = false;


    /**
     * Gets the cf.
     * 
     * @return the cf
     */
    public ConnectFuture getCf() {
        return cf;
    }

    /**
     * Inits the connector.
     * 
     * @return true, if successful
     */
    public synchronized boolean initConnector() {
        try {
            log.info("init skt socket Connector,serverip:" + settings.getSktSocketIp()+ " port:" + settings.getSktSocketPort());
            connector = new NioSocketConnector();
            InetAddress address = InetAddress.getByName(settings.getSktSocketIp());
            //connector.getFilterChain().addLast("logger", new LoggingFilter());
			connector.getFilterChain().addLast("codec", new ProtocolCodecFilter(new TextLineCodecFactory(Charset.forName("UTF-8"))));
            connector.setConnectTimeoutMillis(10000);
            connector.setHandler(new SktClientHandler(this));// 设置事件处理器
			//设置超时时间
			connector.getSessionConfig().setIdleTime(IdleStatus.BOTH_IDLE, 20);
			connector.getSessionConfig().setIdleTime(IdleStatus.READER_IDLE, 30);
			
            cf = connector.connect(new InetSocketAddress(address, settings.getSktSocketPort()));// 建立连接
            //等待连接创建完成,
            //如果不加这句则在连接异常时getSession()并不会抛异常,获得的SESSION为null
            cf.awaitUninterruptibly(1000);
            if (cf.isConnected()) {
                isConnected = true;
                log.info("skt socket server 连接成功");
            } else {
                isConnected = false;
                connector.dispose();
                log.warn("skt socket server 连接失败");
            }
        } catch (Exception e) {
            isConnected = false;
            connector.dispose();
            log.error("在SktConnectorMgr中的connect方法中出错原因是:", e);
        }
        return isConnected;
    }

    /**
     * Interrupt.
     */
    public synchronized void interrupt() {
        isConnected = false;
        if (connector != null && !connector.isDisposed()) {
            connector.dispose();
        }
    }

    /**
     * Close.
     */
    public synchronized void close() {
        isConnected = false;
        terminated = true;
        connector.dispose();
    }
}
