/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.socket.message;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
public class CallincomeMessage extends SocketMessage {

    private final String type = MESSAGE_TYPE_CALLINCOME;
    
    private String customerNo;
    private String agentId;
    private String callin;
    private String talkId;
    private String dist;
	private String gh;
	private String ivrKey;
	private String callee;
	private final long incomingTime;
	
    public CallincomeMessage(String customerNo, String agentId, String callin, String talkId, String dist, String gh,String ivrKey,String callee,long incomingTime) {
        this.customerNo = customerNo;
        this.agentId = agentId;
        this.callin = callin;
        this.talkId = talkId;
        this.dist = dist;
		this.gh = gh;
		this.ivrKey = ivrKey;
		this.callee = callee;
		this.incomingTime = incomingTime;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getCallin() {
        return callin;
    }

    public void setCallin(String callin) {
        this.callin = callin;
    }

    public String getTalkId() {
        return talkId;
    }

    public void setTalkId(String talkId) {
        this.talkId = talkId;
    }

    public String getDist() {
        return dist;
    }

    public void setDist(String dist) {
        this.dist = dist;
    }

	public String getGh() {
		return gh;
	}

	public void setGh(String gh) {
		this.gh = gh;
	}

    public String getIvrKey() {
		return ivrKey;
	}

	public void setIvrKey(String ivrKey) {
		this.ivrKey = ivrKey;
	}

	public String getCallee() {
		return callee;
	}

	public void setCallee(String callee) {
		this.callee = callee;
	}

	public long getIncomingTime() {
		return incomingTime;
	}

	@Override
    public int hashCode() {
        int hash = 5;
        hash = 97 * hash + Objects.hashCode(this.customerNo);
        hash = 97 * hash + Objects.hashCode(this.agentId);
        hash = 97 * hash + Objects.hashCode(this.callin);
        hash = 97 * hash + Objects.hashCode(this.talkId);
        hash = 97 * hash + Objects.hashCode(this.dist);
        hash = 97 * hash + Objects.hashCode(this.gh);
        hash = 97 * hash + Objects.hashCode(this.ivrKey);
        hash = 97 * hash + Objects.hashCode(this.callee);
        hash = 97 * hash + Objects.hashCode(this.incomingTime);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final CallincomeMessage other = (CallincomeMessage) obj;
        if (!Objects.equals(this.customerNo, other.customerNo)) {
            return false;
        }
        if (!Objects.equals(this.agentId, other.agentId)) {
            return false;
        }
        if (!Objects.equals(this.callin, other.callin)) {
            return false;
        }
        if (!Objects.equals(this.talkId, other.talkId)) {
            return false;
        }
        if (!Objects.equals(this.dist, other.dist)) {
            return false;
        }
        if (!Objects.equals(this.gh, other.gh)) {
            return false;
        }
        if (!Objects.equals(this.ivrKey, other.ivrKey)) {
            return false;
        }
        if (!Objects.equals(this.callee, other.callee)) {
            return false;
        }
        if (!Objects.equals(this.incomingTime, other.incomingTime)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder(256);
        sb.append(MESSAGE_HEAD).append(type).append(SPLIT).append(customerNo).append(SPLIT).append(agentId)
            .append(SPLIT).append(callin).append(SPLIT).append(talkId).append(SPLIT)
            .append(dist).append(SPLIT).append(gh).append(SPLIT).append(ivrKey).append(SPLIT).append(callee)
            .append(SPLIT).append(incomingTime).append(SPLIT).append(createTime).append(SPLIT).append(id).append(MESSAGE_FOOTER);
        return sb.toString();
    }

}

