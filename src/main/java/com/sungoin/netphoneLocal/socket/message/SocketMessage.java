/*
 * To change this license header, choose License Headers in Project Properties. To change this template file, choose
 * Tools | Templates and open the template in the editor.
 */
package com.sungoin.netphoneLocal.socket.message;

import com.sungoin.netphoneLocal.util.DateTimeUtil;
import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;

// TODO: Auto-generated Javadoc
/**
 * The Class SocketMessage.
 *
 * <AUTHOR>
 */
public abstract class SocketMessage {
	private static final AtomicLong ID_GENERATE = new AtomicLong(0);
    /**
     * The Constant MESSAGE_TYPE_AGENT.
     */
    public static final String MESSAGE_TYPE_AGENT = "agent";

    /**
     * The Constant MESSAGE_TYPE_DEPT.
     */
    public static final String MESSAGE_TYPE_DEPT = "dept";

    public static final String MESSAGE_TYPE_CALLINCOME = "call_income";

    public static final String MESSAGE_TYPE_MISSEDCALL = "missed_call";

    public static final String MESSAGE_TYPE_CALLEEONHOOK = "callee_onhook";

    /**
     * The Constant DATETIME_PATTERN.
     */
    public static final String DATETIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * The Constant SPLIT.
     */
    public static final String SPLIT = ",";
    //	public static final String MESSAGE_HEAD = "<msg>";
    //	public static final String MESSAGE_FOOTER = "</msg>";
    /**
     * The Constant MESSAGE_HEAD.
     */
    public static final String MESSAGE_HEAD = "";

    /**
     * The Constant MESSAGE_FOOTER.
     */
    public static final String MESSAGE_FOOTER = "";

    /**
     * The Constant HEARTBEAT_MESSSAGE.
     */
    public static final String HEARTBEAT_MESSSAGE = MESSAGE_HEAD + "heartbeat" + MESSAGE_FOOTER;

    /**
     * The create time.
     */
    protected final String createTime;
	
	protected final long id;

    /**
     * Instantiates a new socket message.
     */
    public SocketMessage() {
        this.createTime = System.currentTimeMillis() + "";
		this.id = ID_GENERATE.incrementAndGet();
    }
}
