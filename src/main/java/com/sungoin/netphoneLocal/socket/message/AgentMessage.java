/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose
 * Tools | Templates and open the template in the editor.
 */
package com.sungoin.netphoneLocal.socket.message;

import com.sungoin.netphoneLocal.util.DES;

// TODO: Auto-generated Javadoc
/**
 * The Class AgentMessage.
 * 
 * <AUTHOR> 2014-7-25
 */
public class AgentMessage extends SocketMessage {

    /** The Constant STATE_IDLE. */
    public static final String STATE_IDLE = "idle";

    /** The Constant STATE_BUSY. */
    public static final String STATE_BUSY = "busy";

    /** The Constant STATE_CONNECT. */
    public static final String STATE_CONNECT = "connect";

    /** The type. */
    private final String type = MESSAGE_TYPE_AGENT;

    /** The customer no. */
    private final String customerNo;

    /** The agent id. */
    private final String agentId;

    /** The lsh. */
    private final String lsh;

    /** The state. */
    private final String state;

    /** The bind phone. */
    private final String phone;

    /** The bind phone. */
    private final String gh;

    /** The bind phone. */
    private final String uqid;

    /** The bind phone. */
    private final long calleeOffhookTime;

    private final long incomingTime;

    private final String ivrKey;
    
    private Boolean needLogin;

    /**
     * Gets the customer no.
     * 
     * @return the customer no
     */
    public String getCustomerNo() {
        return this.customerNo;
    }

    /**
     * Gets the agent id.
     * 
     * @return the agent id
     */
    public String getAgentId() {
        return this.agentId;
    }

    /**
     * Gets the state.
     * 
     * @return the state
     */
    public String getState() {
        return this.state;
    }

    /**
     * Gets the lsh.
     * 
     * @return the lsh
     */
    public String getLsh() {
        return this.lsh;
    }

    /**
     * Gets the phone.
     *
     * @return the phone
     */
    public String getPhone() {
        return this.phone;
    }

    /**
     * Gets the creates the time.
     * 
     * @return the creates the time
     */
    public String getCreateTime() {
        return this.createTime;
    }

    public String getUqid() {
        return this.uqid;
    }

    public long getCalleeOffhookTime() {
        return this.calleeOffhookTime;
    }

    public long getIncomingTime() {
        return this.incomingTime;
    }

    public String getGh() {
        return this.gh;
    }

    public String getIvrKey() {
        return this.ivrKey;
    }

    public Boolean getNeedLogin() {
        return needLogin;
    }

    public void setNeedLogin(Boolean needLogin) {
        this.needLogin = needLogin;
    }

    /**
     * Instantiates a new agent message.
     * 
     * @param customerNo the customer no
     * @param agentId the agent id
     * @param lsh the lsh
     * @param state the state
     */
    public AgentMessage(String customerNo, String agentId, String lsh, String state, String phone) {
        this.customerNo = customerNo;
        this.agentId = agentId;
        this.lsh = lsh;
        this.state = state;
        this.phone = phone;
        this.gh = "";
        this.uqid = "";
        this.calleeOffhookTime = 0;
        this.incomingTime = 0;
        this.ivrKey = "";
    }
    
    public AgentMessage(String customerNo, String agentId, String lsh, String state, String phone, Boolean needLogin) {
        this.customerNo = customerNo;
        this.agentId = agentId;
        this.lsh = lsh;
        this.state = state;
        this.phone = phone;
        this.gh = "";
        this.uqid = "";
        this.calleeOffhookTime = 0;
        this.incomingTime = 0;
        this.ivrKey = "";
        this.needLogin = needLogin;
    }

    public AgentMessage(String customerNo, String agentId, String lsh, String state, String phone, String uqid,
        long calleeOffhookTime, long incomingTime, String gh, String ivrKey) {
        this.customerNo = customerNo;
        this.agentId = agentId;
        this.lsh = lsh;
        this.state = state;
        this.phone = phone;
        this.uqid = uqid;
        this.calleeOffhookTime = calleeOffhookTime;
        this.incomingTime = incomingTime;
        this.gh = gh;
        this.ivrKey = (ivrKey == null ? "" : ivrKey);
    }
    
    public AgentMessage(String customerNo, String agentId, String lsh, String state, String phone, String uqid,
        long calleeOffhookTime, long incomingTime, String gh, String ivrKey, Boolean needLogin) {
        this.customerNo = customerNo;
        this.agentId = agentId;
        this.lsh = lsh;
        this.state = state;
        this.phone = phone;
        this.uqid = uqid;
        this.calleeOffhookTime = calleeOffhookTime;
        this.incomingTime = incomingTime;
        this.gh = gh;
        this.ivrKey = (ivrKey == null ? "" : ivrKey);
        this.needLogin = needLogin;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {
        int hash = 7;
        hash = 53 * hash + (this.customerNo != null ? this.customerNo.hashCode() : 0);
        hash = 53 * hash + (this.agentId != null ? this.agentId.hashCode() : 0);
        return hash;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (this.getClass() != obj.getClass()) {
            return false;
        }
        final AgentMessage other = (AgentMessage) obj;
        if ((this.customerNo == null) ? (other.customerNo != null) : !this.customerNo.equals(other.customerNo)) {
            return false;
        }
        return !((this.agentId == null) ? (other.agentId != null) : !this.agentId.equals(other.agentId));
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder(256);
        sb.append(MESSAGE_HEAD).append(this.type).append(SPLIT).append(this.customerNo).append(SPLIT)
            .append(this.agentId).append(SPLIT).append(this.lsh).append(SPLIT).append(this.state).append(SPLIT)
            .append(this.phone).append(SPLIT).append(this.uqid).append(SPLIT)
            .append(this.calleeOffhookTime).append(SPLIT).append(this.incomingTime).append(SPLIT).append(this.gh)
            .append(SPLIT).append(this.ivrKey).append(SPLIT).append(this.createTime).append(SPLIT).append(this.id)
            .append(MESSAGE_FOOTER);
        return sb.toString();
    }
}
