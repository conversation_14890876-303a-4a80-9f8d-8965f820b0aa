/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.socket.message;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class CalleeOnhookMessage extends SocketMessage {

    private final String type = MESSAGE_TYPE_CALLEEONHOOK;
    public static final String FINISH_TYPE_CONNECTED = "0";
    public static final String FINISH_TYPE_MAINCALL_ONHOOK = "1";
    public static final String FINISH_TYPE_CALL_FAIL = "2";

    private String customerNo;
    private String agentId;
    private String gh;
    private String uqid;
    private String onHookType;
    private String finishType;

    public CalleeOnhookMessage(String customerNo, String agentId, String gh, String uqid, String onHookType, String finishType) {
        this.customerNo = customerNo;
        this.agentId = agentId;
        this.gh = gh;
        this.uqid = uqid;
        this.onHookType = onHookType;
        this.finishType = finishType;
    }

    public String getCustomerNo() {
        return this.customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getAgentId() {
        return this.agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getGh() {
        return this.gh;
    }

    public void setGh(String gh) {
        this.gh = gh;
    }

    public String getUqid() {
        return this.uqid;
    }

    public void setUqid(String uqid) {
        this.uqid = uqid;
    }

    public String getOnHookType() {
        return this.onHookType;
    }

    public void setOnHookType(String onHookType) {
        this.onHookType = onHookType;
    }

    public String getCreateTime() {
        return createTime;
    }

    public String getFinishType() {
        return finishType;
    }

    public void setFinishType(String finishType) {
        this.finishType = finishType;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 71 * hash + Objects.hashCode(this.type);
        hash = 71 * hash + Objects.hashCode(this.customerNo);
        hash = 71 * hash + Objects.hashCode(this.agentId);
        hash = 71 * hash + Objects.hashCode(this.gh);
        hash = 71 * hash + Objects.hashCode(this.uqid);
        hash = 71 * hash + Objects.hashCode(this.onHookType);
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (this.getClass() != obj.getClass()) {
            return false;
        }
        final CalleeOnhookMessage other = (CalleeOnhookMessage) obj;
        if (!Objects.equals(this.type, other.type)) {
            return false;
        }
        if (!Objects.equals(this.customerNo, other.customerNo)) {
            return false;
        }
        if (!Objects.equals(this.agentId, other.agentId)) {
            return false;
        }
        if (!Objects.equals(this.gh, other.gh)) {
            return false;
        }
        if (!Objects.equals(this.uqid, other.uqid)) {
            return false;
        }
        if (!Objects.equals(this.onHookType, other.onHookType)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder(256);
        sb.append(MESSAGE_HEAD).append(this.type).append(SPLIT).append(this.customerNo).append(SPLIT)
            .append(this.agentId).append(SPLIT).append(this.gh).append(SPLIT).append(this.uqid).append(SPLIT)
            .append(this.onHookType).append(SPLIT).append(this.createTime).append(SPLIT).append(this.id)
            .append(MESSAGE_FOOTER);
        return sb.toString();
    }

}
