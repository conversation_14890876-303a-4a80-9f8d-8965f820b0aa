/*
 * To change this license header, choose License Headers in Project Properties. To change this template file, choose
 * Tools | Templates and open the template in the editor.
 */

package com.sungoin.netphoneLocal.socket.message;

// TODO: Auto-generated Javadoc
/**
 * The Class DeptMessage.
 * 
 * <AUTHOR>
 */
public class DeptMessage extends SocketMessage {

    /** The type. */
    private final String type = MESSAGE_TYPE_DEPT;

    /** The customer no. */
    private String customerNo;

    /** The dept id. */
    private String deptId;

    /** The count. */
    private String count;

    /**
     * Instantiates a new dept message.
     * 
     * @param customerNo the customer no
     * @param deptId the dept id
     * @param count the count
     */
    public DeptMessage(String customerNo, String deptId, String count) {
        this.customerNo = customerNo;
        this.deptId = deptId;
        this.count = count;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }
    
    /* (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {
        int hash = 7;
        hash = 97 * hash + (this.customerNo != null ? this.customerNo.hashCode() : 0);
        hash = 97 * hash + (this.deptId != null ? this.deptId.hashCode() : 0);
        return hash;
    }

    /* (non-Javadoc)
     * @see java.lang.Object#equals(java.lang.Object)
     */
    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final DeptMessage other = (DeptMessage) obj;
        if ((this.customerNo == null) ? (other.customerNo != null) : !this.customerNo
                .equals(other.customerNo)) {
            return false;
        }
        return !((this.deptId == null) ? (other.deptId != null) : !this.deptId.equals(other.deptId));
    }

    /* (non-Javadoc)
     * @see java.lang.Object#toString()
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder(128);
        sb.append(MESSAGE_HEAD).append(type).append(SPLIT).append(customerNo).append(SPLIT).append(deptId)
                .append(SPLIT).append(count).append(SPLIT).append(createTime).append(SPLIT).append(id).append(MESSAGE_FOOTER);
        return sb.toString();
    }
}
