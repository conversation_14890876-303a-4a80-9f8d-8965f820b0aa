/*
 * To change this license header, choose License Headers in Project Properties. To change this template file, choose
 * Tools | Templates and open the template in the editor.
 */
package com.sungoin.netphoneLocal.socket;

import com.sungoin.netphoneLocal.socket.message.SocketMessage;
import com.sungoin.netphoneLocal.boot.StartupAble;
import com.sungoin.netphoneLocal.boot.StartupResister;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.mina.core.future.ConnectFuture;
import org.springframework.stereotype.Service;

// TODO: Auto-generated Javadoc
/**
 * The Class PlatformSocketClient.
 *
 * <AUTHOR> 2014-7-25
 */
@Service
public class PlatformSocketClient implements StartupAble {

    /**
     * The Constant log.
     */
    private static final Log log = LogFactory.getLog(PlatformSocketClient.class);

    @Resource
    private PlatformConnectorMgr mgr;

    /**
     * The messages.
     */
    private final LinkedBlockingQueue<SocketMessage> messages = new LinkedBlockingQueue<SocketMessage>();

    /**
     * Adds the message.
     *
     * @param message the message
     */
    public void addMessage(SocketMessage message) {
        if (mgr.isConnected) {
            if (messages.remove(message)) {
                log.info("移除过期的消息：" + message);
            }
            messages.offer(message);
        }
    }

    /**
     * startSendThread.
     */
    public void startSendThread() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (!mgr.terminated) {
                    if (mgr.isConnected) {
                        try {
                            SocketMessage message = messages.take();
                            log.debug("收到消息：" + message);
                            if (sendMessage(message.toString())) {
                                log.debug("消息发送成功！");
                            } else {
                                log.warn("消息发送失败！");
                                if (!messages.contains(message)) {
                                    log.debug("队列中没有更新的消息，重新回到队列中待发送！");
                                    messages.offer(message);
                                } else {
                                    log.debug("队列中存在更新的消息，剔除此消息！");
                                }
                            }
                        } catch (Exception ex) {
                            log.error(ex.getMessage());
                        }
                    }
                }
            }
        }).start();
    }

    /**
     * Send message.
     *
     * @param message the message
     * @return true, if successful
     */
    private boolean sendMessage(String message) {
        ConnectFuture cf = mgr.getCf();
        boolean status = false;
        try {
            cf.getSession().write(message);// 发送消息
            status = true;
        } catch (Exception e) {
            status = false;
            log.error("在PlatformSocketClient中的sendMessage方法中出错原因是:" + e.getMessage());
        }
        return status;
    }

//    @PostConstruct
//    public void init() {
//        StartupResister.register(this);
//    }

    @Override
    public void start() {
        int retryCount = 0;
        mgr.initConnector();
        while (!mgr.isConnected && retryCount < 3) {
            try {
                TimeUnit.SECONDS.sleep(2);
            } catch (InterruptedException ex) {
                log.error(ex.getMessage(), ex);
            }
            log.warn("连接中心平台socket server失败！重试第" + (++retryCount) + "次");
            mgr.initConnector();
        }
        if (!mgr.isConnected) {
//			throw new IllegalStateException("连接中心平台socket server失败！");
            log.error("连接中心平台socket server失败！");
        }
        this.startSendThread();
    }
}
