/*
 * To change this license header, choose License Headers in Project Properties. To change this template file, choose
 * Tools | Templates and open the template in the editor.
 */
package com.sungoin.netphoneLocal.socket;

import com.sungoin.netphoneLocal.socket.message.SocketMessage;
import java.util.concurrent.TimeUnit;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.mina.core.service.IoHandlerAdapter;
import org.apache.mina.core.session.IdleStatus;
import org.apache.mina.core.session.IoSession;

// TODO: Auto-generated Javadoc
/**
 * The Class PlatformClientHandler.
 * 
 * <AUTHOR>
 */
public class SktClientHandler extends IoHandlerAdapter {
    /** The Constant log. */
    private static final Log log = LogFactory.getLog(SktClientHandler.class);
	
	private final SktConnectorMgr mgr;
	
    /**
     * Instantiates a new platform client handler.
	 * @param mgr
     */
    public SktClientHandler(SktConnectorMgr mgr) {
		this.mgr = mgr;
    }

    /* (non-Javadoc)
     * @see org.apache.mina.core.service.IoHandlerAdapter#exceptionCaught(org.apache.mina.core.session.IoSession, java.lang.Throwable)
     */
    @Override
    public void exceptionCaught(IoSession session, Throwable cause) throws Exception {
        log.error("exceptionCaught method was called..." + cause.getMessage(), cause);
        session.close(true);
		log.info("session close command is send...");
    }

    /* (non-Javadoc)
     * @see org.apache.mina.core.service.IoHandlerAdapter#sessionClosed(org.apache.mina.core.session.IoSession)
     */
    @Override
    public void sessionClosed(IoSession session) throws Exception {
        log.warn("sessionClosed method was called!");
        mgr.interrupt();
        while (!mgr.isConnected) {
            if (mgr.terminated) {
                break;
            }
            try {
                TimeUnit.SECONDS.sleep(10);
                log.info("开始重新连接skt socket服务器：");
                mgr.initConnector();
            } catch (Exception ex) {
                log.error("重新连接skt socket服务器失败！" + ex.getMessage());
            }
        }
    }
	
    /* (non-Javadoc)
     * @see org.apache.mina.core.service.IoHandlerAdapter#sessionIdle(org.apache.mina.core.session.IoSession, org.apache.mina.core.session.IdleStatus)
     */
    @Override
    public void sessionIdle(IoSession session, IdleStatus status) throws Exception {
        if (status == IdleStatus.BOTH_IDLE) {
            log.info("sessionIdle method was called ! heartbeat...");
            session.write(SocketMessage.HEARTBEAT_MESSSAGE);
        } else if(status == IdleStatus.READER_IDLE) {
			 log.warn("sessionReadIdle method was called，close session!");
			 session.close(true);
		}
    }

	@Override
	public void messageReceived(IoSession session, Object message) throws Exception {
		log.info("message received:" + message);
	}
	
}
