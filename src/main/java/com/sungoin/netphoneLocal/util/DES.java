package com.sungoin.netphoneLocal.util;

import java.io.File;
import java.io.IOException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.List;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FileUtils;

public final class DES {

    public DES() {
    }

    private static final String strKey = "!@#$%sungoin&*()&*@#";

    /**
     * 根据参数生成Key;
     * 
     * @param strKey
     */
    private static Key getKey() {
        Key key = null;
        try {
            KeyGenerator _generator = KeyGenerator.getInstance("DES");
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(strKey.getBytes());
            _generator.init(56, secureRandom);
            key = _generator.generateKey();
            _generator = null;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return key;
    }

    /**
     * 获得一次3DES加密后的密文
     * 
     * @param
     * @return strMi
     */
    public static String getEncString(String strMing) {
        byte[] byteMi = null;
        byte[] byteMing = null;
        String strMi = "";
        Key key = getKey();
        try {
            byteMing = strMing.getBytes("utf-8");
            byteMi = getEncCode(byteMing, key);
            strMi = Base64.encodeBase64String(byteMi);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            byteMi = null;
            byteMing = null;
        }
        return strMi;
    }

    /**
     * 获得两次3DES加密后的密文
     * 
     * @param
     * @return strMi
     */
    public static String getTwiceEncString(String strMing) {
        return getEncString(getEncString(strMing));
    }

    /**
     * 获得一次3DES解密后的明文
     * 
     * @param
     * @return strMing
     */
    public static String getDecString(String strMi) {

        byte[] byteMing = null;
        byte[] byteMi = null;
        String strMing = "";
        Key key = getKey();
        try {
            byteMi = Base64.decodeBase64(strMi);
            byteMing = getDecCode(byteMi, key);
            strMing = new String(byteMing, "utf-8");
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            byteMing = null;
            byteMi = null;
        }
        return strMing;
    }

    /**
     * 获得两次3DES解密后的明文
     * 
     * @param
     * @return strMing
     */
    public static String getTwiceDecString(String strMi) {
        return getDecString(getDecString(strMi));
    }

    /**
     * 获得一次3DES加密后的密文
     * 
     * @param byts
     * @return
     */
    private static byte[] getEncCode(byte[] byts, Key key) {
        byte[] byteFina = null;
        Cipher cipher;
        try {
            cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byteFina = cipher.doFinal(byts);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            cipher = null;
        }
        return byteFina;
    }

    /**
     * 获得一次3DES解密后的明文
     * 
     * @param bytd
     * @return
     */
    private static byte[] getDecCode(byte[] bytd, Key key) {
        byte[] byteFina = null;
        Cipher cipher = null;
        try {
            cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, key);
            byteFina = cipher.doFinal(bytd);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            cipher = null;
        }
        return byteFina;
    }
	
	public static void decryptFile(String srcFile, String destFile) throws Exception {
		File src = new File(srcFile);
		File dest = new File(destFile);
		List<String> lines = FileUtils.readLines(src);
		List<String> destLines = new ArrayList(lines.size());
		for(String line : lines) {
			destLines.add(DES.getTwiceDecString(line));
		}
		FileUtils.writeLines(dest, destLines);
	}
    
    public static void encryptFile(String srcFile, String destFile) throws Exception {
		File src = new File(srcFile);
		File dest = new File(destFile);
		List<String> lines = FileUtils.readLines(src);
		List<String> destLines = new ArrayList(lines.size());
		for(String line : lines) {
			destLines.add(DES.getTwiceEncString(line));
		}
		FileUtils.writeLines(dest, destLines);
	}

    public static void main(String[] args) throws Exception {
//        String ming = "13916432587";
//        //        String ming = "[{\"bind_phone\":\"f220dd19-702a-47d4-8463-1c2d8c045808\",\"lsh\":\"6adde4c2-9d83-4e29-8c4c-65d9ef694007\",\"end_flag\":1,\"customer_original_no\":\"31233491\",\"caller_offhook_time\":\"2016-08-23 16:51:54\",\"customer_no\":\"4000219999\",\"callee_no\":\"13916432587\",\"caller_no\":\"02131233320\",\"talk_type\":5,\"onhook_mode\":1,\"dept_id\":\"11680607529\",\"onhook_time\":\"2016-08-23 16:52:35\",\"gh\":\"2025\",\"outgoing_time\":\"2016-08-23 16:52:29\",\"talk_interval\":0,\"incoming_time\":\"2016-08-23 16:51:53\",\"callee_offhook_time\":null}]";
//        String encyStr = DES.getEncString(ming);
//        System.out.println("一次加密后的密文是:" + encyStr);
//
//        String decyStr = DES.getDecString(encyStr);
//        System.out.println("一次解密后的明文是:" + decyStr);
//
//        encyStr = DES.getTwiceEncString(ming);
//        System.out.println("两次加密后的密文是:" + encyStr);
//        decyStr = DES.getTwiceDecString(encyStr);
//        System.out.println("两次解密后的明文是:" + decyStr);

		decryptFile("d:/a.txt", "d:/a_dest.csv");
//        encryptFile("d:/b.txt", "d:/b_dest.csv");
    }

}
