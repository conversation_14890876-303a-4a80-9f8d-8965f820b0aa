/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.util;

import net.sf.ehcache.Cache;
import net.sf.ehcache.CacheManager;
import net.sf.ehcache.Element;

/**
 * <AUTHOR> 2015-7-20
 *         EHCache util
 */
public final class CacheUtil {

    public static final String CACHE_NAME_SINGLE = "single";
    public static final String CACHE_NAME_CLUSTER = "cluster";
    public static final String CACHE_NAME_QUEUE = "queue";
    public static final String CACHE_AGENT_STATE_NAME = "agentState";
    public static final String CACHE_CALL_PROCESS_NAME = "callProcess";
    public static final String CACHE_CONNECT_NAME = "connect";
    public static final String CACHE_OFFLINE_NAME = "offline";

    public static CacheManager manager = CacheManager.getInstance();

    public static Object get(String cacheName, Object key) {
        Cache cache = loadCache(cacheName);
        try {
            cache.acquireReadLockOnKey(key);
            Element element = cache.get(key);
            return element == null || element.isExpired() ? null : element.getObjectValue();
        } finally {
            cache.releaseReadLockOnKey(key);
        }
    }

    public static void put(String cacheName, Object key, Object value) {
        Cache cache = loadCache(cacheName);
        if (value == null) {
            remove(cacheName, key);
            return;
        }
        try {
            cache.acquireWriteLockOnKey(key);
            cache.putIfAbsent(new Element(key, value));
        } finally {
            cache.releaseWriteLockOnKey(key);
        }
    }

    public static boolean remove(String cacheName, Object key) {
        Cache cache = loadCache(cacheName);
        try {
            cache.acquireWriteLockOnKey(key);
            return cache.remove(key);
        } finally {
            cache.releaseWriteLockOnKey(key);
        }
    }
    
    public static Object removeAndReturn(String cacheName, Object key) {
        Cache cache = loadCache(cacheName);
        try {
            cache.acquireWriteLockOnKey(key);
            Element element = cache.removeAndReturnElement(key);
            return element == null || element.isExpired() ? null : element.getObjectValue();
        } finally {
            cache.releaseWriteLockOnKey(key);
        }
    }

    public static Cache loadCache(String cacheName) {
        Cache cache = manager.getCache(cacheName);
        if (cache == null) {
            throw new NullPointerException("cache name:" + cacheName
                + " is not found!,please check ehcache.xml");
        }
        return cache;
    }

    public static boolean isCacheExist(String cacheName) {
        return manager.cacheExists(cacheName);
    }

    public static Cache createNewCache(String name, int maxElementsInMemory,
        boolean overflowToDisk, boolean eternal, long timeToLiveSeconds,
        long timeToIdleSeconds) {
        if (manager.cacheExists(name)) {
            throw new IllegalStateException("cache name:" + name
                + " is already exist.");
        }
        Cache cache = new Cache(name, maxElementsInMemory, overflowToDisk,
            eternal, timeToLiveSeconds, timeToIdleSeconds);
        manager.addCache(cache);
        return cache;
    }
}
