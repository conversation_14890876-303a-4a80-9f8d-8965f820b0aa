/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.util;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import com.sungoin.netphoneLocal.business.bean.BusinessConstants;

/**
 * <AUTHOR> 2015-8-4
 */
public class DateTimeUtil {

    public static String format(Date date, DateTimeUtil.Pattern pattern) {
        return date == null ? null : DateFormatUtils.format(date, pattern.context);
    }

    public static String formatDate(Date date) {
        return date == null ? null : DateFormatUtils.format(date, DateTimeUtil.Pattern.DATE.context);
    }

    public static String formatShortDate(Date date) {
        return date == null ? null : DateFormatUtils.format(date, DateTimeUtil.Pattern.SHORTDATE.context);
    }

    public static String formatTime(Date date) {
        return date == null ? null : DateFormatUtils.format(date, DateTimeUtil.Pattern.TIME.context);
    }

    public static String formatShortTime(Date date) {
        return date == null ? null : DateFormatUtils.format(date, DateTimeUtil.Pattern.SHORTTIME.context);
    }

    public static String formatDateTime(Date date) {
        return date == null ? null : DateFormatUtils.format(date, DateTimeUtil.Pattern.DATETIME.context);
    }

    public static Date parseDate(String text, DateTimeUtil.Pattern pattern) {
        try {
            return DateUtils.parseDate(text, pattern.context);
        } catch (ParseException ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public static enum Pattern {

        DATE("yyyy-MM-dd"), TIME("HH:mm:ss"), DATETIME("yyyy-MM-dd HH:mm:ss"), SHORTTIME("HH:mm"), SHORTDATETIME(
            "yyyy-MM-dd HH:mm"), SHORTDATE("yyyyMMdd");
        private final String context;

        private Pattern(String context) {
            this.context = context;
        }
    }

    /**
     * 获取指定时间的星期数.
     *
     * @param dt the dt
     * @return the week of date
     */
    public static int getWeek(Date dt) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w <= 0) {
            w = 7;
        }
        return w;
    }

    /**
     * 获取指定时间在当月的第几天.
     *
     * @param dt the dt
     * @return the week of date
     */
    public static int getMonthOfDay(Date dt) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);
        return cal.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 计算两个时间相差秒数.
     *
     * @param startTime the start time
     * @param endTime the end time
     * @return the time difference
     */
    public static long getTimeDifference(Date startTime, Date endTime) {
        long timeDifference = endTime.getTime() - startTime.getTime();
        long i = timeDifference / BusinessConstants.TIME_SECONDS;
        return i;
    }

    /**
     * 比较当前时间是否属于周期时间段内.
     *
     * @param nowTime
     * @param startTime
     * @param endTime
     * @return true, if successful
     */
    public static boolean compareToWeekTime(String nowTime, String startTime, String endTime) {
        int i = nowTime.compareTo(startTime);
        int j = nowTime.compareTo(endTime);
        return i >= 0 && j <= 0;
    }

    /**
     * 判断时间是否前夸天.
     *
     * @param nowTime the now time
     * @param startTime the start time
     * @param endTime the end time
     * @return true, if successful
     */
    public static boolean compareToAcrossWeekTime(String nowTime, String startTime, String endTime) {
        int i = startTime.compareTo(endTime);
        int j = nowTime.compareTo(endTime);
        return i > 0 && j <= 0;
    }

    /**
     * 判断时间是否夸天.
     *
     * @param nowTime the now time
     * @param startTime the start time
     * @param endTime the end time
     * @return true, if successful
     */
    public static boolean compareToAfterWeekTime(String nowTime, String startTime, String endTime) {
        int i = startTime.compareTo(endTime);
        int j = nowTime.compareTo(startTime);
        return i > 0 && j >= 0;
    }
    
    public static boolean satisfyTime(String nowTime, String startTime, String endTime) {
        return compareToWeekTime(nowTime, startTime, endTime) || compareToAcrossWeekTime(nowTime, startTime, endTime) || compareToAfterWeekTime(nowTime, startTime, endTime);
    }
    
    public static String addDay(int day){
    	Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DATE, day);
        return formatDate(cal.getTime());
    }
    
    public static void main(String[] args) {
        String beginTime = "09:00";
        String endTime = "09:00";
        String nowTime = DateTimeUtil.formatShortTime(new Date());
        System.out.println(DateTimeUtil.satisfyTime(nowTime, beginTime, endTime));
    }
}
