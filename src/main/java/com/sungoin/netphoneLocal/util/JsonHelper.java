/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.util;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 *
 * <AUTHOR> 2015-7-20
 */
public class JsonHelper {

	private static final ObjectMapper mapper = new ObjectMapper();

	static {
		mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
	}

	public static String Object2Json(Object obj) {
		String returnValue = null;
		try {
			returnValue = mapper.writeValueAsString(obj);
		} catch (IOException ex) {
			throw new ClassCastException("对象" + obj + "转换Json出错！" + ex.getMessage());
		}
		return returnValue;
	}

	public static <T extends Object> T json2Object(String json, Class<T> clz) {
		try {
			return mapper.readValue(json, clz);
		} catch (IOException ex) {
			throw new ClassCastException("字符串" + json + "转换对象出错！" + ex.getMessage());
		}
	}

	public static <T extends Object> List<T> json2List(String json, Class<T> clz) {
		try {
			JavaType javaType = mapper.getTypeFactory().constructParametricType(List.class, clz);
			return mapper.readValue(json, javaType);
		} catch (IOException ex) {
			throw new ClassCastException("字符串" + json + "转换对象出错！" + ex.getMessage());
		}
	}
}
