/**
 * @Title: HttpHelper.java
 * @Package com.sj.util
 * @Description: header注释
 * <AUTHOR>
 * @version V1.0
 */
package com.sungoin.netphoneLocal.util;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CookieStore;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.tsccm.ThreadSafeClientConnManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sungoin.netphoneLocal.business.bean.baidu.BaiduResult;
import com.sungoin.netphoneLocal.midware.service.sungoin.SungoinResponse;
import com.sungoin.netphoneLocal.socket.message.SocketMessage;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import javax.net.ssl.SSLContext;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContexts;
import org.apache.http.conn.ssl.TrustStrategy;

// TODO: Auto-generated Javadoc
/**
 * The Class HttpHelper.
 */
public class HttpHelper {

    /**
     * The log.
     */
     private static final Logger log = LoggerFactory.getLogger(HttpHelper.class);

    public static final String RETURN_SUCCEED = "1";

    /**
     * The Constant httpclient.
     */
    private static final HttpClient httpclient = new DefaultHttpClient(new ThreadSafeClientConnManager());// 默认最多20个并发连接
    private static HttpClient httpsclient = null;
    static {
        ((ThreadSafeClientConnManager) httpclient.getConnectionManager()).setMaxTotal(100);
        ((ThreadSafeClientConnManager) httpclient.getConnectionManager()).setDefaultMaxPerRoute(20);
        // 设置超时时间
        HttpParams params = httpclient.getParams();
        Integer timeout = 20000;
        HttpConnectionParams.setConnectionTimeout(params, timeout);
        HttpConnectionParams.setSoTimeout(params, timeout);
        //初始化httpsClient，忽略所有证书
        try {
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(timeout)
                    .setSocketTimeout(timeout).setConnectionRequestTimeout(timeout).build();
            
            SSLContext sslContextIgnore = SSLContexts.custom().loadTrustMaterial(null, new TrustStrategy() {
                @Override
                public boolean isTrusted(X509Certificate[] xcs, String string) throws CertificateException {
                    return true;
                }
            }).build();
            
            SSLConnectionSocketFactory socketFactoryIgnore = new SSLConnectionSocketFactory(sslContextIgnore);
            httpsclient = HttpClients.custom().setMaxConnPerRoute(20).setMaxConnTotal(50).setDefaultRequestConfig(requestConfig)
                    .setSslcontext(sslContextIgnore).setSSLSocketFactory(socketFactoryIgnore).build();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * default
     *
     * @param url
     * @param map
     * @return
     */
    public static String SendGetRequest(String url, Map<String, String> map) {
        return SendGetRequest(url, map, null);
    }

    public static String SendGetRequest(String url, Map<String, String> map, CookieStore cookieStore) {
        if (!map.isEmpty()) {
            StringBuilder sb = new StringBuilder(url);
            if (url.contains("?")) {
                sb.append("&");
            } else {
                sb.append("?");
            }
            for (java.util.Map.Entry<String, String> entry : map.entrySet()) {
                sb.append(entry.getKey()).append("&");
            }
            sb.deleteCharAt(sb.length() - 1);
            url = sb.toString();
        }
        return SendGetRequest(new HttpGet(url), cookieStore);
    }

    /**
     * Send web message.
     *
     * @param http
     * @param cookieStore
     * @return the string
     */
    public static String SendGetRequest(HttpGet http, CookieStore cookieStore) {
        InputStream instream = null;
        try {
            if (cookieStore != null) {
                ((DefaultHttpClient) httpclient).setCookieStore(cookieStore);
            }
            HttpResponse response = httpclient.execute(http);
            if (response.getStatusLine().getStatusCode() != 200) {
                throw new IllegalStateException("调用" + http.getURI() + "出错！返回状态码："
                    + response.getStatusLine().getStatusCode());
            }
            HttpEntity entity = response.getEntity();
            instream = entity.getContent();
            return IOUtils.toString(instream, "utf-8");
        } catch (Exception e) {
            log.error("调用" + http.getURI() + "出错了 ！" + e.getMessage(), e);
            throw new IllegalArgumentException(e.getMessage(), e);
        } finally {
            try {
                if (http != null) {
                    http.abort();
                }
                if (instream != null) {
                    instream.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    public static String SendPostRequest(String url, Map<String, String> map) {
        return SendPostRequest(url, map, null, null, false);
    }

    public static String SendPostRequest(String url, Map<String, String> map, CookieStore cookieStore,
        UsernamePasswordCredentials nameAndPassword, boolean transJson) {
        InputStream instream = null;
        HttpPost http = null;
        try {
            http = new HttpPost(url);
            if (cookieStore != null) {
                ((DefaultHttpClient) httpclient).setCookieStore(cookieStore);
            }
            if (nameAndPassword != null) {
                ((DefaultHttpClient) httpclient).getCredentialsProvider()
                    .setCredentials(AuthScope.ANY, nameAndPassword);
            }
            List<NameValuePair> params = new ArrayList<NameValuePair>();
            if (null != map && !map.isEmpty()) {
                if (transJson) {
                    log.debug("传出参数:" + JsonHelper.Object2Json(map));
                    params.add(new BasicNameValuePair("data", JsonHelper.Object2Json(map)));
                } else {
                    StringBuilder sb = new StringBuilder();
                    for (Map.Entry<String, String> entry : map.entrySet()) {
                        sb.append(" 传出参数:").append(entry.getKey()).append("值").append(entry.getValue());
                        params.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                    }
                    log.debug(sb.toString());
                }
            }
            http.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
            HttpResponse response = httpclient.execute(http);
            if (response.getStatusLine().getStatusCode() != 200) {
                throw new IllegalStateException("调用" + http.getURI() + "出错！返回状态码："
                    + response.getStatusLine().getStatusCode());
            }
            HttpEntity entity = response.getEntity();
            instream = entity.getContent();
            return IOUtils.toString(instream, "utf-8");
        } catch (Exception e) {
            log.error("调用" + http.getURI() + "出错了 ！" + e.getMessage(), e);
            throw new IllegalArgumentException(e.getMessage(), e);
        } finally {
            try {
                if (http != null) {
                    http.abort();
                }
                if (instream != null) {
                    instream.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    public static InputStream SendPostRequestStream(String url, Map<String, String> map) {
        InputStream instream = null;
        HttpPost http = null;
        try {
            http = new HttpPost(url);
            List<NameValuePair> params = new ArrayList<NameValuePair>();
            if (null != map && !map.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    sb.append(" 传出参数:").append(entry.getKey()).append("值").append(entry.getValue());
                    params.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                }
                log.debug(sb.toString());
            }
            http.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
            HttpResponse response = httpclient.execute(http);
            if (response.getStatusLine().getStatusCode() != 200) {
                throw new IllegalStateException("调用" + http.getURI() + "出错！返回状态码："
                    + response.getStatusLine().getStatusCode());
            }
            HttpEntity entity = response.getEntity();
            instream = entity.getContent();
        } catch (Exception e) {
            log.error("调用" + http.getURI() + "出错了 ！" + e.getMessage(), e);
            throw new IllegalArgumentException(e.getMessage(), e);
        } finally {
            try {
                if (http != null) {
                    http.abort();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return instream;
    }
	static Map<String, String> urlMap = new HashMap();
	static Connection conn = null;
//	static Connection conn = JdbcUtil.getConn("**************************************************************************************************************************************", "root", "sungoin");
	public static final void importSktCall() throws Exception {
		File file = new File("d:/0625.txt");
		List<String> list = FileUtils.readLines(file, "utf-8");
		for(String s : list) {
			String orgId = s.substring(0,40);
			String userId = s.substring(41,81);
			String data = s.substring(82);
			Map map = new HashMap();
			map.put("orgId", orgId);
			map.put("userId", userId);
			map.put("data", data);
			String callUrl = getOrgUrl(orgId) + "thirdCallcenter/saveCall";
			if(callUrl.startsWith("http://crm1")) {
				continue;
			}
			SendPostRequest(callUrl,map);
			TimeUnit.MILLISECONDS.sleep(200);
		}
	}
	
	public static final void importSktRecord() throws Exception {
		File file = new File("d:/0625-u.txt");
		List<String> list = FileUtils.readLines(file, "utf-8");
		for(String s : list) {
			String[] ss = s.split(" ");
			String playUrl = ss[0];
			String orgId = ss[1];
			String phone = ss[2];
			String userId = ss[3];
			String downloadUrl = ss[4];
			String callId = ss[5];
			Map map = new HashMap();
			map.put("orgId", orgId);
			map.put("userId", userId);
			map.put("phone", phone);
			map.put("callId", callId);
			map.put("playUrl", playUrl);
			map.put("downloadUrl", downloadUrl);
			String recordUrl = getOrgUrl(orgId) + "thirdCallcenter/saveRecord";
			if(recordUrl.startsWith("http://crm1")) {
				continue;
			}
			SendPostRequest(recordUrl,map);
			TimeUnit.MILLISECONDS.sleep(200);
		}
	}
	
	public static String getOrgUrl(String orgId) throws SQLException {
		if(urlMap.containsKey(orgId)) {
			return urlMap.get(orgId);
		} else {
			String sql = "SELECT o.bucketId FROM universaldb_v2.organization o WHERE o.organizationId = '" + orgId + "'";
			Statement st = conn.createStatement();
			ResultSet rs = st.executeQuery(sql);
			int bucketId = 0;
			String url = "http://crm.sunke.com/";
			while(rs.next()) {
				bucketId = rs.getInt("bucketId");
			}
			rs.close();
			st.close();
			if(bucketId ==2) {
				url = "http://crm1.sunke.com/";
			} else if(bucketId == 3) {
				url = "http://crm2.sunke.com/";
			}
			urlMap.put(orgId, url);
			return url;
		}
	}
	
	
	public static void fixCall() throws IOException, SQLException {
		File file = new File("d:/1.csv");
		List<String> list = FileUtils.readLines(file,"utf-8");
		Statement st = conn.createStatement();
		st.execute("use wisecloudcrm_5434");
		for(String s : list) {
			String[] ss = s.split(",");
			String row_id = ss[0];
			String result = ss[1];
			System.out.println(row_id);
			String sql = "update `call` set result = " + result + " where raw_id = '" + row_id + "'";
			st.addBatch(sql);
		}
		st.executeBatch();
		st.close();
	}

    public static void httpDownloadFile(String url, File file) {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        try {
            final HttpGet httpGet = new HttpGet(url);
            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(10000)
                .setConnectionRequestTimeout(5000).setSocketTimeout(60000).build();
            httpGet.setConfig(requestConfig);
			Thread chehckThread = new Thread(new Runnable() {
				@Override
				public void run() {
					try {
						log.debug("开始监控文件下载：" + url);
						TimeUnit.MINUTES.sleep(1);
						httpGet.abort();
					} catch (InterruptedException ex) {
						log.debug("请求完成，中断监控");
					}
				}
			});
			chehckThread.start();
			log.debug("监控线程启动：" + url);
            response = httpclient.execute(httpGet);
            HttpEntity httpEntity = response.getEntity();
            InputStream is = httpEntity.getContent();
            FileUtils.copyInputStreamToFile(is, file);
            EntityUtils.consume(httpEntity);
			chehckThread.interrupt();
			
        } catch (Exception e) {
            log.error("文件下载流异常：", e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                if (httpclient != null) {
                    httpclient.close();
                }
            } catch (IOException e) {
                log.error("文件下载流关闭异常：", e);
            }
        }
    }
    
    public static BaiduResult postBaidu(String url, Object obj) {
        HttpPost post = null;
        try {
        	post = new HttpPost(url);
        	post.setHeader("Content-Type", "application/json;charset=utf-8");
            StringEntity jsonstr = new StringEntity(JsonHelper.Object2Json(obj), "utf-8");
            post.setEntity(jsonstr);
            // 发送请求
            HttpResponse httpResponse = httpclient.execute(post);
            // 获取响应输入流
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            String c = "";
            if (statusCode == HttpServletResponse.SC_OK) {
                HttpEntity entity = httpResponse.getEntity();
                InputStream content = entity.getContent();
                c = IOUtils.toString(content, "utf-8");
                if (c.length() > 0) {
                    ObjectMapper mapper = new ObjectMapper();
                    return mapper.readValue(c, BaiduResult.class);
                } else {
                    return null;
                }
            } else {
            	log.error("请求服务端失败 - statusCode = {}", statusCode);
            }
        } catch (Exception e) {
            throw new RuntimeException("send request error.", e);
        } finally {
            try {
                if (post != null) {
                    post.abort();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return null;
    }
    
    public static int postPlatformClient(String url, SocketMessage message) {
        int statusCode = -1;
        HttpPost post= null;
        try {
            post = new HttpPost(url);
        	post.setHeader("Content-Type", "application/json;charset=utf-8");
            StringEntity jsonstr = new StringEntity(JsonHelper.Object2Json(message), "utf-8");
            post.setEntity(jsonstr);
            // 发送请求
            HttpResponse httpResponse = httpclient.execute(post);
            // 获取响应输入流
            statusCode = httpResponse.getStatusLine().getStatusCode();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (post != null) {
                    post.abort();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return statusCode;
    }
    
    public static SungoinResponse postSungoinHttps(String url, Object obj) {
        HttpPost post = null;
        try {
        	post = new HttpPost(url);
        	post.setHeader("Content-Type", "application/json;charset=utf-8");
            StringEntity jsonstr = new StringEntity(JsonHelper.Object2Json(obj), "utf-8");
            post.setEntity(jsonstr);
            // 发送请求
            HttpResponse httpResponse = httpsclient.execute(post);
            // 获取响应输入流
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            String c = "";
            if (statusCode == HttpServletResponse.SC_OK) {
                HttpEntity entity = httpResponse.getEntity();
                InputStream content = entity.getContent();
                c = IOUtils.toString(content, "utf-8");
                if (c.length() > 0) {
                    ObjectMapper mapper = new ObjectMapper();
                    return mapper.readValue(c, SungoinResponse.class);
                } else {
                    return null;
                }
            } else {
            	log.error("请求服务端失败 - statusCode = {}", statusCode);
            }
        } catch (Exception e) {
            throw new RuntimeException("send request error.", e);
        } finally {
            try {
                if (post != null) {
                    post.abort();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return null;
    }
    
    public static String postHttpsJson(String url, Object param) {
        HttpPost post = null;
        try {
        	post = new HttpPost(url);
        	post.setHeader("Content-Type", "application/json;charset=utf-8");
            StringEntity jsonstr = new StringEntity(JsonHelper.Object2Json(param), "utf-8");
            post.setEntity(jsonstr);
            // 发送请求
            HttpResponse httpResponse = httpsclient.execute(post);
            // 获取响应输入流
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == HttpServletResponse.SC_OK) {
                HttpEntity entity = httpResponse.getEntity();
                InputStream content = entity.getContent();
                return IOUtils.toString(content, "utf-8");
            } else {
            	log.error("请求服务端失败 - statusCode = {}", statusCode);
            }
        } catch (Exception e) {
            throw new RuntimeException("send request error.", e);
        } finally {
            try {
                if (post != null) {
                    post.abort();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return null;
    }
    
    public static String postHttpsParams(String url, Map<String, String> param) {
        InputStream instream = null;
        HttpPost http = null;
        try {
            http = new HttpPost(url);
            List<NameValuePair> params = new ArrayList<NameValuePair>();
            if (null != param && !param.isEmpty()) {
                StringBuilder sb = new StringBuilder();
                for (Map.Entry<String, String> entry : param.entrySet()) {
                    sb.append(" 传出参数:").append(entry.getKey()).append("值").append(entry.getValue());
                    params.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                }
                log.debug(sb.toString());
            }
            http.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
            HttpResponse response = httpclient.execute(http);
            if (response.getStatusLine().getStatusCode() != 200) {
                throw new IllegalStateException("调用" + http.getURI() + "出错！返回状态码："
                    + response.getStatusLine().getStatusCode());
            }
            HttpEntity entity = response.getEntity();
            instream = entity.getContent();
            return IOUtils.toString(instream, "utf-8");
        } catch (Exception e) {
            log.error("调用" + http.getURI() + "出错了 ！" + e.getMessage(), e);
            throw new IllegalArgumentException(e.getMessage(), e);
        } finally {
            try {
                if (http != null) {
                    http.abort();
                }
                if (instream != null) {
                    instream.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }
}
