package com.sungoin.netphoneLocal.util;

import org.apache.commons.lang3.StringUtils;

public final class MyStringUtil {

	private static final String ONE = "1";
	private static final int ZREO=0;
	/**
	 * 获取手机号码前7位.
	 *
	 * @param mobile the mobile
	 * @return the no by mobile
	 */
	public static String getNoByMobile(String mobile) {
		if (StringUtils.isEmpty(mobile) || mobile.length() < 11) {
			return "";
		}
		return mobile.substring(0, 7);
	}
 
	public static boolean String2Boolean(String str) {
		return ONE.equals(str);
	}
	
	public static int String2Inteter(String str) {
		if(StringUtils.isBlank(str)){
			return ZREO;
		}
		return  Integer.parseInt(str);
	}
	
	public static boolean isMobile(String mobile) {
		if(StringUtils.isNotEmpty(mobile)) {
			if(mobile.length() == 11 && mobile.startsWith("1")) {
				return true;
			} 
			if(mobile.length() == 12 && mobile.startsWith("01") && !mobile.startsWith("010")) {
				return true;
			}
		}
		return false;
	}
}
