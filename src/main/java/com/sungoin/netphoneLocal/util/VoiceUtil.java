/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import it.sauronsoftware.jave.AudioAttributes;
import it.sauronsoftware.jave.Encoder;
import it.sauronsoftware.jave.EncoderException;
import it.sauronsoftware.jave.EncodingAttributes;
import it.sauronsoftware.jave.InputFormatException;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.SystemUtils;


/**
 *
 * <AUTHOR>
 */
public class VoiceUtil {

	private static final Logger LOGGER = LoggerFactory.getLogger(VoiceUtil.class);

	private VoiceUtil() {
	}

	public static void wav2mp3(File wavFile, File mp3File, int bitRate) throws IllegalArgumentException, EncoderException {
		if (mp3File.exists()) {
			return;
		}
		AudioAttributes audio = new AudioAttributes();
		audio.setCodec("libmp3lame");
		audio.setBitRate(bitRate);
		audio.setChannels(1);
		audio.setSamplingRate(8000);
		EncodingAttributes attrs = new EncodingAttributes();
		attrs.setFormat("mp3");
		attrs.setAudioAttributes(audio);
		Encoder encoder = new Encoder();
		encoder.encode(wavFile, mp3File, attrs);
	}
	
	public static int wav2wav(File wavFile, File mp3File)  {
		try {
			AudioAttributes audio = new AudioAttributes();
			audio.setCodec("pcm_alaw");
			audio.setBitRate(64000);
			audio.setChannels(1);
			audio.setSamplingRate(8000);
			EncodingAttributes attrs = new EncodingAttributes();
			attrs.setFormat("wav");
			attrs.setAudioAttributes(audio);
			Encoder encoder = new Encoder();
			encoder.encode(wavFile, mp3File, attrs);
			return 1;
		} catch (Exception e) {
			LOGGER.error("{}:文件转换失败:",wavFile.getAbsolutePath(),e);
			return -1;
		}
	}
	
	public static void wavToMp3(String sources,String desFileName){
	    try {
			List<String> commend = new ArrayList<String>();
	        //
	        //ffmpeg  -i /data/12337524473779309.wav -vn -acodec libmp3lame 
	        //-ab 8000 -ac 2 -ar 8000 -f mp3 -y /data/12337524473779309.mp3
            if(SystemUtils.IS_OS_WINDOWS) {
                commend.add("D:\\ffmpeg\\ffmpeg.exe");
            } else {
                commend.add("/usr/local/bin/ffmpeg");
            }
	        commend.add("-i");
	        commend.add(sources);
	        commend.add("-vn");
	        commend.add("-acodec");
	        commend.add("libmp3lame");
	        commend.add("-ab");
	        commend.add("8000");
	        commend.add("-ac");
	        commend.add("1");
	        commend.add("-ar");
	        commend.add("8000");
	        commend.add("-f");
	        commend.add("mp3");
	        commend.add("-y");
	        commend.add(desFileName);
	        StringBuffer test=new StringBuffer();
	        for(int i=0;i<commend.size();i++)
	                test.append(commend.get(i)+" ");
	        LOGGER.info("音频转换执行命令:{}",test.toString());
	        ProcessBuilder builder = new ProcessBuilder();
	        builder.command(commend);
            builder.redirectErrorStream(true);
            Process process =builder.start();
            process.waitFor(10, TimeUnit.MINUTES);
        } catch (Exception e) {
        	LOGGER.error("音频转换失败",e);
        }
        LOGGER.info("{}:音频转换成功",sources);
	} 
	
	public static void wavToMp3(String leftFile, String rightFile, String complexFile) {
        try {
            File left = new File(leftFile);
            File right = new File(rightFile);
            if (!left.exists() && !right.exists()) {
            	LOGGER.error("合成源文件不存在！");
                return;
            } else if (left.exists() && !right.exists()) {
            	LOGGER.warn("右声道源文件不存在！");
                VoiceUtil.wavToMp3(leftFile, complexFile);
                return;
            } else if (right.exists() && !left.exists()) {
            	LOGGER.warn("左声道源文件不存在！");
                VoiceUtil.wavToMp3(rightFile, complexFile);
                return;
            }
            LOGGER.debug("开始合并音频：左声道：{}，右声道：{},合并后文件：{}", leftFile, rightFile, complexFile);
            List<String> commend = new ArrayList<String>();
            if(SystemUtils.IS_OS_WINDOWS) {
                commend.add("D:\\ffmpeg\\ffmpeg.exe");
            } else {
                commend.add("/usr/local/bin/ffmpeg");
            }
	        commend.add("-i");
	        commend.add(leftFile);
	        commend.add("-i");
	        commend.add(rightFile);
	        commend.add("-filter_complex");
	        commend.add("[0:a][1:a]amerge=inputs=2[aout]");
	        commend.add("-map");
	        commend.add("[aout]");
	        commend.add(complexFile);
	        StringBuffer test=new StringBuffer();
	        for(int i=0;i<commend.size();i++)
	                test.append(commend.get(i)+" ");
	        LOGGER.info("音频合成执行命令:{}",test.toString());
	        ProcessBuilder builder = new ProcessBuilder();
	        builder.command(commend);
            builder.redirectErrorStream(true);
            Process process =builder.start();
            process.waitFor(10, TimeUnit.MINUTES);
        } catch (Exception e) {
        	LOGGER.error("音频合成失败",e);
        }
        LOGGER.info("{}:音频合成成功",complexFile);
    }

}
