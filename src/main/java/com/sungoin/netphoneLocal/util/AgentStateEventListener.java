/*
 * To change this license header, choose License Headers in Project Properties. To change this template file, choose
 * Tools | Templates and open the template in the editor.
 */
package com.sungoin.netphoneLocal.util;

import com.sungoin.netphoneLocal.business.service.BaseService;
import com.sungoin.netphoneLocal.midware.MidwareConstants;
import com.sungoin.netphoneLocal.midware.service.ProcessService;
import net.sf.ehcache.Ehcache;
import net.sf.ehcache.Element;
import net.sf.ehcache.event.CacheEventListenerAdapter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * The Class AgentStateEventListener.
 *
 * <AUTHOR> 2014-8-13
 */
public class AgentStateEventListener extends CacheEventListenerAdapter {

	/**
	 * The Constant log.
	 */
	private static final Logger log = LoggerFactory.getLogger(AgentStateEventListener.class);

	/* (non-Javadoc)
	 * @see net.sf.ehcache.event.CacheEventListenerAdapter#notifyElementExpired(net.sf.ehcache.Ehcache, net.sf.ehcache.Element)
	 */
	@Override
	public void notifyElementExpired(Ehcache cache, Element element) {
		log.info("notifyElementExpired：key=" + element.getObjectKey() + " value=" + element.getObjectValue());
		String bindPhoneId = (String) element.getObjectKey();
		String customerNo = (String) element.getObjectValue();
		BaseService bs = SpringHelper.getBean(BaseService.class);
		boolean sendMessage = bs.getUserByNumber(customerNo).getPushStatusFlag();
        ProcessService  ps = SpringHelper.getBean(ProcessService.class);
        if("connect".equals(ps.getBindPhoneStatus(bindPhoneId))) {
            ps.midwareUpdateAgentState(bindPhoneId, MidwareConstants.AGENT_STATE_IDLE, sendMessage);
        }
	}
}
