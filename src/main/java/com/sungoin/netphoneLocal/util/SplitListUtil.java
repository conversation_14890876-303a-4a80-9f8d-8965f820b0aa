/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SplitListUtil {

	public static List<List<Object>> split(List<Object> list, int splitSize) {
		List<List<Object>> array = new ArrayList<List<Object>>();
		if (list == null || list.isEmpty()) {
			return array;
		}
		List[] datas = new List[splitSize];
		for (int i = 0; i < splitSize; i++) {
			datas[i] = new ArrayList();
			array.add(datas[i]);
		}
		for (int i = 0; i < list.size(); i++) {
			datas[i % splitSize].add(list.get(i));
		}
//		int count = list.size();
//		if (splitSize > count) {
//			array.add(list);
//			return array;
//		}
//		//如果不能整除
//		if (count % splitSize != 0) {
//			int scale = 0; //精度
//			BigDecimal b1 = new BigDecimal(Double.toString(count));
//			BigDecimal b2 = new BigDecimal(Double.toString(splitSize));
//			//每个数组的个数
//			int one = b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).intValue();
//			//最后一个数组的个数
//			int lost = count - one * (splitSize - 1);
//			array.add(list.subList(0, lost));
//			list = list.subList(lost, list.size());
//			count = list.size();
//			
//		}
//		int start = 0;
//		
//		int number = count / splitSize;
//		while (!(count - count / splitSize == start)) {
//			array.add(list.subList(start, number));
//			start = number;
//			number = number + count / splitSize;
//		}
//		array.add(list.subList(start, count));

		return array;
	}

}
