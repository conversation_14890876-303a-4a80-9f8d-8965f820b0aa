/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.util;

import java.io.File;
import java.security.Key;
import java.security.Security;
import java.util.ArrayList;
import java.util.List;
import javax.crypto.Cipher;
import org.apache.commons.io.FileUtils;

/**
 *
 * <AUTHOR>
 */
public class CtiDes {
	private static String strDefaultKey = "1!@#$sungoin&shang@jing@2012!*b&*!";
    private Cipher encryptCipher = null;
    private Cipher decryptCipher = null;
    private static CtiDes instance = null;
    
    public synchronized static CtiDes getInstance() throws Exception {
        if(instance == null) {
            instance = new CtiDes();
        }
        return instance;
    }
    
    /**
     * * 将byte数组转换为表示16进制值的字符串， 如：byte[]{8,18}转换为：0813， 和public static byte[] *
     * hexStr2ByteArr(String strIn) 互为可逆的转换过程 * * @param arrB * 需要转换的byte数组 * @return
     * 转换后的字符串 * @throws Exception * 本方法不处理任何异常，所有异常全部抛出
     */
    public static String byteArr2HexStr(byte[] arr) throws Exception {
        int iLen = arr.length;
        // 每个byte用两个字符才能表示，所以字符串的长度是数组长度的两倍
        StringBuilder sb = new StringBuilder(iLen * 2);
        for (int i = 0; i < iLen; i++) {
            int intTmp = arr[i];
            // 把负数转换为正数
            while (intTmp < 0) {
                intTmp = intTmp + 256;
            }
            // 小于0F的数需要在前面补0
            if (intTmp < 16) {
                sb.append("0");
            }
            sb.append(Integer.toString(intTmp, 16));
        }
        return sb.toString();
    }

    /**
     * * 将表示16进制值的字符串转换为byte数组， 和public static String byteArr2HexStr(byte[]
     * arrB) * 互为可逆的转换过程 * * @param strIn * 需要转换的字符串 * @return 转换后的byte数组 * @throws
     * Exception * 本方法不处理任何异常，所有异常全部抛出 * <AUTHOR>
     * href="mailto:<EMAIL>">LiGuoQing</a>
     */
    public static byte[] hexStr2ByteArr(String strIn) throws Exception {
        byte[] arr = strIn.getBytes();
        int iLen = arr.length;
        // 两个字符表示一个字节，所以字节数组长度是字符串长度除以2
        byte[] arrOut = new byte[iLen / 2];
        int kd=2;
        for (int i = 0; i < iLen; i = i + kd) {
            String strTmp = new String(arr, i, 2);
            arrOut[i / 2] = (byte) Integer.parseInt(strTmp, 16);
        }
        return arrOut;
    }

    /** * 默认构造方法，使用默认密钥 * * @throws Exception */
    private CtiDes() throws Exception {
        this(strDefaultKey);
    }

    /** * 指定密钥构造方法 * * @param strKey * 指定的密钥 * @throws Exception */
    private CtiDes(String strKey) throws Exception {
        Security.addProvider(new com.sun.crypto.provider.SunJCE());
        Key key = getKey(strKey.getBytes());
        encryptCipher = Cipher.getInstance("DES");
        encryptCipher.init(Cipher.ENCRYPT_MODE, key);
        decryptCipher = Cipher.getInstance("DES");
        decryptCipher.init(Cipher.DECRYPT_MODE, key);
    }

    /**
     * * 加密字节数组 * * @param arrB * 需加密的字节数组 * @return 加密后的字节数组 * @throws
     * Exception
     */
    public byte[] encrypt(byte[] arr) throws Exception {
        return encryptCipher.doFinal(arr);
    }

    /** * 加密字符串 * * @param strIn * 需加密的字符串 * @return 加密后的字符串 * @throws Exception */
    public String encrypt(String strIn) throws Exception {
        return byteArr2HexStr(encrypt(strIn.getBytes()));
    }

    /**
     * * 解密字节数组 * * @param arrB * 需解密的字节数组 * @return 解密后的字节数组 * @throws
     * Exception
     */
    public byte[] decrypt(byte[] arr) throws Exception {
        return decryptCipher.doFinal(arr);
    }

    /** * 解密字符串 * * @param strIn * 需解密的字符串 * @return 解密后的字符串 * @throws Exception */
    public String decrypt(String strIn) throws Exception {
        return new String(decrypt(hexStr2ByteArr(strIn)));
    }

    /**
     * * 从指定字符串生成密钥，密钥所需的字节数组长度为8位 不足8位时后面补0，超出8位只取前8位 * * @param arrBTmp *
     * 构成该字符串的字节数组 * @return 生成的密钥 * @throws java.lang.Exception
     */
    private Key getKey(byte[] arrTmp) throws Exception {
        // 创建一个空的8位字节数组（默认值为0）
        byte[] arr = new byte[8];
        // 将原始字节数组转换为8位
        for (int i = 0; i < arrTmp.length && i < arr.length; i++) {
            arr[i] = arrTmp[i];
        }
        // 生成密钥
        Key key = new javax.crypto.spec.SecretKeySpec(arr, "DES");
        return key;
    }

	public static void decryptFile(String srcFile, String destFile) throws Exception {
		final CtiDes des = CtiDes.getInstance();
		File src = new File(srcFile);
		File dest = new File(destFile);
		List<String> lines = FileUtils.readLines(src);
		List<String> destLines = new ArrayList(lines.size());
		for(String line : lines) {
			destLines.add(des.decrypt(line));
		}
		FileUtils.writeLines(dest, destLines);
	}
	
    public static void main(String[] args) {
        // TODO Auto-generated method stub
        try {
//            String test = "13916621178";
//            CtiDes des = CtiDes.getInstance();
//            System.out.println("加密前的字符：" + test);
//			String test1 = des.encrypt(test);
//            System.out.println("加密后的字符：" + test1);
//            System.out.println("解密后的字符：" + des.decrypt(test1));
            long beginTime = System.currentTimeMillis();
			decryptFile("d:/a.txt", "d:/a_dest.csv");
            long endTime = System.currentTimeMillis();
            System.out.println("used " + (endTime - beginTime) + " millis seconds");
        } catch (Exception e) {
            e.printStackTrace();
            // TODO: handle exception e.printStackTrace();
        }
    }
}
