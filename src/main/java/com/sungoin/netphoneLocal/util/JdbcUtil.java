package com.sungoin.netphoneLocal.util;

import java.io.UnsupportedEncodingException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.logging.Level;
import java.util.logging.Logger;
import net.sf.ehcache.search.Results;
import org.apache.commons.lang3.CharUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;

public final class JdbcUtil {

	public static Connection getConn(String jdbcUrl) {
		Connection conn = null;
		try {
			conn = DriverManager.getConnection(jdbcUrl);
		} catch (Exception e) {
			throw new RuntimeException("MySQL操作错误：" + e.getMessage());
		}
		return conn;
	}

	public static Connection getConn(String jdbcUrl, String username, String password) {
		Connection conn = null;
		try {
			conn = DriverManager.getConnection(jdbcUrl, username, password);
		} catch (Exception e) {
			throw new RuntimeException("MySQL操作错误：" + e.getMessage());
		}
		return conn;
	}

	public static void main(String[] args) throws SQLException, UnsupportedEncodingException {
		Connection conn = JdbcUtil.getConn("**************************************************************************************************************************************", "root", "sungoin");
//		Connection conn = JdbcUtil.getConn("****************************************************************************************************************************************", "root", "Sungoin2017!mysql");
//		String searchStr = "netphone/configMainPage\\?entity=navigateConfig";
//		String replaceStr = "systemSetting?id=navigateConfig";
		int begin = 3911;
		int end = 9999;
//		String updateSql = "update Account as _Account set lastContactedOn = (select ( _Activity.`createdOn` ) as _column1 from `Activity` as _Activity where ( _Activity.`isDeleted` = 0 or _Activity.`isDeleted` is null ) and _Activity.`accountId` = '${destID}' and ( ( 1 = 1 ) ) and ( ( _Activity.`activityId` = '${sourceID}' ) ) and (systemTypeCode not in (3,4,5,6) ))  where (( _Account.`isDeleted` = 0 or _Account.`isDeleted` is null ) and ( 1 = 1 )) AND (_Account.accountId = '${destID}')";
//		String updateSql = "ALTER TABLE `third_call` modify COLUMN downloadUrl varchar(500)";
//		String updateSql2 = "ALTER TABLE `third_call` modify COLUMN playUrl varchar(500)";
		
		Statement st = conn.createStatement();
		for (int i = begin; i <= end; i++) {
			try {
				String dbName = "wisecloudcrm_" + i;
				String sql = "ALTER DATABASE " + dbName + " CHARACTER SET utf8 COLLATE utf8_5624_1";
				st.execute(sql);
//				st.execute("use " + dbName);
//				st.execute(updateSql);
//				st.execute(updateSql2);
//				String sql = "update " + dbName + ".triggerrule set updateSql = ? where autoId = 2";
//				PreparedStatement pst = conn.prepareCall(sql);
//				pst.setString(1, updateSql);
//				pst.execute();
//				pst.close();
				
//				String sql2 = "select m.autoId, m.menuJson from " + dbName + ".custommenu m where m.menuType in (0,1) and m.menuJson like '%" + searchStr + "%' ";
//				Statement st2 = conn.createStatement();
//				ResultSet rs2 = st2.executeQuery(sql2);
//				while (rs2.next()) {
//					System.out.println("开始修复处理数据库：" + dbName);
//					long autoId = rs2.getLong("autoId");
//					String menuJson = rs2.getString("menuJson");
//					String replaceJson = menuJson.replaceAll(searchStr, replaceStr);
//					PreparedStatement ps = conn.prepareStatement("update " + dbName + ".custommenu set menuJson = ? where autoId = ?");
//					ps.setString(1, replaceJson);
//					ps.setLong(2, autoId);
//					ps.execute();
//					ps.close();
//					System.out.println("修复数据库：" + dbName + "结束！");
//				}
//				rs2.close();
//				st2.close();
				System.out.println(dbName + " updated.");
			} catch (Exception ex) {
				
			}
		}
		conn.close();
	}
}
