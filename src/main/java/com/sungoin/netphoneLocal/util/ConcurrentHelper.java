/*
 * To change this template, choose Tools | Templates and open the template in the editor.
 */

package com.sungoin.netphoneLocal.util;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

public class ConcurrentHelper {

    /** The Constant MAX_THREAD_COUNT. */
    public static final int MAX_THREAD_COUNT = 128;

    /** The Constant es. */
    private static final ExecutorService es = Executors.newFixedThreadPool(MAX_THREAD_COUNT);

    /**
     * Do in background.
     * 
     * @param task the task
     */
    public static void doInBackground(Runnable task) {
        es.submit(task);
    }

    /**
     * Do in background.
     * 
	 * @param <T>
     * @param task the task
     * @return the future< t>
     */
    public static <T> Future<T> doInBackground(Callable<T> task) {
        return es.submit(task);
    }

    /**
     * Destory.
     */
    public static void destory() {
        es.shutdown();
    }
}
