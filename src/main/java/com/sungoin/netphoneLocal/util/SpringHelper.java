/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.sungoin.netphoneLocal.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 
 * <AUTHOR>
 * 2015-7-28
 */
@Component
public class SpringHelper implements ApplicationContextAware {
	private static ApplicationContext ctx;

	public static void setCtx(ApplicationContext ctx) {
		SpringHelper.ctx = ctx;
	}

	public static ApplicationContext getCtx() {
		return ctx;
	}
	
	@Override
	public void setApplicationContext(ApplicationContext ac) throws BeansException {
		SpringHelper.setCtx(ac);
	}

	public static <T extends Object> T getBean(Class<T> type) {
		if(ctx == null) {
			throw new IllegalStateException("spring context is not initialized yet!");
		}
		return ctx.getBean(type);
	}
}
