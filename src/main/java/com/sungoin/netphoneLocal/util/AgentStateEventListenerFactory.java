/*
 * To change this license header, choose License Headers in Project Properties. To change this template file, choose
 * Tools | Templates and open the template in the editor.
 */

package com.sungoin.netphoneLocal.util;

import java.util.Properties;

import net.sf.ehcache.event.CacheEventListener;
import net.sf.ehcache.event.CacheEventListenerFactory;

/**
 * The Class AgentStateEventListenerFactory.
 * 
 * <AUTHOR> 2014-8-13
 */
public class AgentStateEventListenerFactory extends CacheEventListenerFactory {

    /* (non-Javadoc)
     * @see net.sf.ehcache.event.CacheEventListenerFactory#createCacheEventListener(java.util.Properties)
     */
    @Override
    public CacheEventListener createCacheEventListener(Properties prprts) {
        return new AgentStateEventListener();
    }

}
