package com.sungoin.netphoneLocal.util;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <p>CopyRright (c)2015-SUNGOIN</p>
 * <p>Project：agent</p>
 * <p>Module ID:util </p>
 * </p>Description：MD5加密工具类 </p>  
 * @author：wanghaochuan
 * @date：2015-9-19
 */
public class MD5Util {
	
	 //用于建立十六进制字符的输出的小写字符数组
	private static final char[] DIGITS_LOWER = { '0', '1', '2', '3', '4', '5',
			'6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };
	//用于建立十六进制字符的输出的大写字符数组
	private static final char[] DIGITS_UPPER = { '0', '1', '2', '3', '4', '5',
			'6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };
	
	 public static String encoderByMd5(String str) throws NoSuchAlgorithmException, UnsupportedEncodingException{
        MessageDigest md5=MessageDigest.getInstance("MD5");
        return encodeHexStr(md5.digest(str.getBytes("utf-8")),Boolean.TRUE);
	 }
	
	/**
	 * 将字节数组转换为十六进制字符串
	 * 
	 * @param data byte[]
	 * @param toLowerCase <code>true</code> 传换成小写格式 ， <code>false</code> 传换成大写格式
	 * @return 十六进制String
	 */
	public static String encodeHexStr(byte[] data, boolean toLowerCase) {
		return encodeHexStr(data, toLowerCase ? DIGITS_LOWER : DIGITS_UPPER);
	}

	/**
	 * 将字节数组转换为十六进制字符串
	 * 
	 * @param data byte[]
	 * @param toDigits 用于控制输出的char[]
	 * @return 十六进制String
	 */
	protected static String encodeHexStr(byte[] data, char[] toDigits) {
		return new String(encodeHex(data, toDigits));
	}

	/** 
     * 将十六进制字符转换成一个整数 
     *  
     * @param ch 十六进制char 
     * @param index  十六进制字符在字符数组中的位置 
     * @return 一个整数 
     */  
    protected static int toDigit(char ch, int index) {  
        int digit = Character.digit(ch, 16);  
        if (digit == -1) {  
            throw new RuntimeException("Illegal hexadecimal character " + ch  
                    + " at index " + index);  
        }  
        return digit;  
    }  
    
	/** 
     * 将字节数组转换为十六进制字符数组 
     *  
     * @param data byte[] 
     * @param toDigits  用于控制输出的char[] 
     * @return 十六进制char[] 
     */  
    protected static char[] encodeHex(byte[] data, char[] toDigits) {  
        int l = data.length;  
        char[] out = new char[l << 1];  
        // two characters form the hex value.  
        for (int i = 0, j = 0; i < l; i++) {  
            out[j++] = toDigits[(0xF0 & data[i]) >>> 4];  
            out[j++] = toDigits[0x0F & data[i]];  
        }  
        return out;  
    }  
}
