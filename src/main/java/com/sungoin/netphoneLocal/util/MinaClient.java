/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.util;

import com.sungoin.netphoneLocal.socket.PlatformClientHandler;
import com.sungoin.netphoneLocal.socket.PlatformConnectorMgr;
import com.sungoin.netphoneLocal.socket.message.SocketMessage;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.nio.charset.Charset;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.mina.core.future.ConnectFuture;
import org.apache.mina.core.service.IoHandlerAdapter;
import org.apache.mina.core.session.IdleStatus;
import org.apache.mina.core.session.IoSession;
import org.apache.mina.filter.codec.ProtocolCodecFilter;
import org.apache.mina.filter.codec.textline.TextLineCodecFactory;
import org.apache.mina.transport.socket.nio.NioSocketConnector;

/**
 *
 * <AUTHOR>
 */
public class MinaClient {

	private ConnectFuture cf = null;

	private NioSocketConnector connector = null;

	/**
	 * The is connected.
	 */
	public volatile boolean isConnected = false;

	/**
	 * The terminated.
	 */
	public volatile boolean terminated = false;

	public synchronized boolean initConnector() {
		try {
			connector = new NioSocketConnector();
			InetAddress address = InetAddress.getByName("*************");
			//connector.getFilterChain().addLast("logger", new LoggingFilter());
			connector.getFilterChain().addLast("codec", new ProtocolCodecFilter(new TextLineCodecFactory(Charset.forName("UTF-8"))));
			connector.setConnectTimeoutMillis(10000);
			connector.setHandler(new MinaClientHandler(this));// 设置事件处理器
			cf = connector.connect(new InetSocketAddress(address, 19999));// 建立连接
			//等待连接创建完成,
			//如果不加这句则在连接异常时getSession()并不会抛异常,获得的SESSION为null
			cf.awaitUninterruptibly(1000);
			if (cf.isConnected()) {
				isConnected = true;
				System.out.println("platform socket server 连接成功");
			} else {
				isConnected = false;
				connector.dispose();
				System.out.println("platform socket server 连接失败");
			}
		} catch (Exception e) {
			isConnected = false;
			connector.dispose();
			System.out.println("在PlatformConnectorMgr中的connect方法中出错原因是:" + e);
		}
		return isConnected;
	}

	/**
	 * Interrupt.
	 */
	public synchronized void interrupt() {
		isConnected = false;
		if (connector != null && !connector.isDisposed()) {
			connector.dispose();
		}
	}

	/**
	 * Close.
	 */
	public synchronized void close() {
		isConnected = false;
		terminated = true;
		connector.dispose();
	}
	
	public static void main(String[] args) {
		MinaClient client = new MinaClient();
		client.initConnector();
		while(client.isConnected) {
			try {
				Thread.currentThread().join();
			} catch (InterruptedException ex) {
				Logger.getLogger(MinaClient.class.getName()).log(Level.SEVERE, null, ex);
			}
		}
	}

	private class MinaClientHandler extends IoHandlerAdapter {

		private final MinaClient mgr;

		/**
		 * Instantiates a new platform client handler.
		 *
		 * @param mgr
		 */
		public MinaClientHandler(MinaClient mgr) {
			this.mgr = mgr;
		}

		/* (non-Javadoc)
     * @see org.apache.mina.core.service.IoHandlerAdapter#exceptionCaught(org.apache.mina.core.session.IoSession, java.lang.Throwable)
		 */
		@Override
		public void exceptionCaught(IoSession session, Throwable cause) throws Exception {
			System.out.println("exceptionCaught method was called..." + cause.getMessage());
			session.close(true);
			System.out.println("session close command is send...");
		}

		/* (non-Javadoc)
     * @see org.apache.mina.core.service.IoHandlerAdapter#sessionClosed(org.apache.mina.core.session.IoSession)
		 */
		@Override
		public void sessionClosed(IoSession session) throws Exception {
			System.out.println("sessionClosed method was called!");
			mgr.interrupt();
			while (!mgr.isConnected) {
				if (mgr.terminated) {
					break;
				}
				try {
					TimeUnit.SECONDS.sleep(10);
					System.out.println("开始重新连接platform socket服务器：");
					mgr.initConnector();
				} catch (Exception ex) {
					System.out.println("重新连接platform socket服务器失败！" + ex.getMessage());
				}
			}
		}

		/* (non-Javadoc)
     * @see org.apache.mina.core.service.IoHandlerAdapter#sessionCreated(org.apache.mina.core.session.IoSession)
		 */
		@Override
		public void sessionCreated(IoSession session) throws Exception {
			session.getConfig().setIdleTime(IdleStatus.BOTH_IDLE, 5);
			session.getConfig().setIdleTime(IdleStatus.READER_IDLE, 10);
		}

		/* (non-Javadoc)
     * @see org.apache.mina.core.service.IoHandlerAdapter#sessionIdle(org.apache.mina.core.session.IoSession, org.apache.mina.core.session.IdleStatus)
		 */
		@Override
		public void sessionIdle(IoSession session, IdleStatus status) throws Exception {
			if (status == IdleStatus.BOTH_IDLE) {
				System.out.println("sessionIdle method was called ! heartbeat...");
				session.write(SocketMessage.HEARTBEAT_MESSSAGE);
			} else if (status == IdleStatus.READER_IDLE) {
				System.out.println("sessionReadIdle method was called，close session!");
				session.close(true);
			}
		}

		@Override
		public void messageReceived(IoSession session, Object message) throws Exception {
			System.out.println("message received:" + message);
		}
	}
}
