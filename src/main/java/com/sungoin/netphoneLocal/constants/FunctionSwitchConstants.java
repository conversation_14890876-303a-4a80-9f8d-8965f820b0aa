/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.constants;

/**
 *
 * <AUTHOR>
 */
public class FunctionSwitchConstants {
	public static final String FUNCTION_SWITCH = "按键转接";
	public static final String FUNCTION_QUEUE_TOTAL_MINUTES = "排队总分钟数";
	public static final String FUNCTION_IVR_TIMEOUT_KEY = "按键超时收码";
	public static final String FUNCTION_CALL_TIMEOUT = "通话超时时长";
    public static final String FUNCTION_ROOT_HAS_CHILD_DEPT = "根部门存在子导航";
    public static final String FUNCTION_CUSTOMER_WHITE_LIST = "自定义白名单";
    public static final String FUNCTION_CUSTOMER_IVR_ERROR_TIP = "自定义按键错误提示音";
    public static final String FUNCTION_CUSTOMER_IVR_QUIT_TIP = "自定义按键退出提示音";
    
    public static final String FUNCTION_CUSTOMER_PROCESS = "自定义流程";

    public static final String CALLEE_GLOBAL_RULE = "被叫全局规则";
    
    public static final String QUEUE_TIMEOUT_NO_INPUT = "排队超时不收码";
    
    public static final String QUEUE_TIMEOUT_INPUT_TIME = "排队超时收码时长";
    
    public static final String MISSED_CALL_CHARGE_TIME = "未接不扣费失效时段";
    
    public static final String FUNCTION_AGENT_CALL_ONCE = "坐席只呼叫一次";
}
