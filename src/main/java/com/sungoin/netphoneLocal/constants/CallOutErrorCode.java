/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.constants;

/**
 *
 * <AUTHOR>
 */
public class CallOutErrorCode {
    public static final int FORBID = 200; //业务不允许
    public static final int BUSY = 202; //被叫忙
    public static final int NO_ANSWER = 203; //被叫无应答
    public static final int REJECT = 204; //被叫拒接
    public static final int MAINCALL_ONHOOK = 205; //主叫挂机
    public static final int INVALID_NUMBER = 206; //空号
    public static final int POWER_OFF = 207; //关机
    public static final int UNAVAILABLE = 208; //暂时无法接听
    public static final int SUSPEND = 209; //停机
    public static final int NO_IDLE_BINDPHONE = 210; //无空闲号码
    public static final int API_ERROR = 211; //接口错误
    
    public static int getCodeByDjCode(int djCode) {
        switch(djCode) {
            case 1 :
            case 28 : return INVALID_NUMBER;
            case 17 : return BUSY;
            case 18 :
            case 19 : return NO_ANSWER;
            case 20 : return POWER_OFF;
            case 21 : return REJECT;
            default : return UNAVAILABLE;
        }
    }
    
    public static int getBaiduPushCode(int code) {
        if(code < 200) {
            code = getCodeByDjCode(code);
        }
        return code;
    }
}
