# To change this license header, choose License Headers in Project Properties.
# To change this template file, choose Too<PERSON> | Templates
# and open the template in the editor.

# EMBEDDED SERVER CONFIGURATION (ServerProperties) 
server.port=9080 

#DB properties:  
spring.datasource.url=************************************************************************************
spring.datasource.username=netphone_local
spring.datasource.password=netphone_local@220429#
spring.datasource.driverClassName=com.mysql.jdbc.Driver
spring.datasource.max-active=50
spring.datasource.max-idle=20
spring.datasource.min-idle=10
spring.datasource.initial-size=10
spring.datasource.test-on-borrow=true
spring.datasource.validation-query=SELECT 1;

#JPA Configuration:  
spring.jpa.show-sql=true
spring.jpa.hibernate.ddl-auto=none
#spring.jpa.open-in-view=true
#spring.jpa.database-platform=org.hibernate.dialect.MySQL5Dialect
#spring.jpa.hibernate.naming_strategy=org.hibernate.cfg.ImprovedNamingStrategy
#spring.jpa.database=org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5InnoDBDialect
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
#spring.jpa.properties.hibernate.cache.region.factory_class=org.hibernate.cache.ehcache.EhCacheRegionFactory

#linuxç³»ç»tomcatä¸´æ¶ç®å½
server.tomcat.basedir = /usr/local/tomcattmp

#windowsç³»ç»tomcatä¸´æ¶ç®å½
#server.tomcat.basedir = d:/tomcattmp

rocketmq.accessKey=pszAC57BB2g7YyhD
rocketmq.secretKey=xe603Erst42F1fd8
rocketmq.nameSrvAddr=rmq-cn-36z3t9ve503.cn-shanghai.rmq.aliyuncs.com:8080
rocketmq.namespace=rmq-cn-36z3t9ve503