# To change this license header, choose License Headers in Project Properties.
# To change this template file, choose Tools | Templates
# and open the template in the editor.

#平台ID
platform_id = 70
#是否主机
main_server = true
#备份机请求地址
backup_server_endpoint = http://192.168.1.35:9090/branch/service/agent/
#本地区号
local_no = 021
#坐席外呼前缀 0:外地坐席呼叫补0;1:外地坐席呼叫补区号
prefix = 1
#原始被叫是否加本地区号
orig_callee_prefix = false


#base_phone_config表指定使用的400号码
local_customer_no =

#总机类型 (全局==gloable，独立==separate)
switchboard_type = gloable

#启动时是否连接cti
boot_connect_cti = false

#被叫关闭透传功能时，主叫小号是否加区号
original_no_prefix = false

#主叫合法性验证时过滤的区号（逗号分隔）
caller_check_ignore = 0555

#允许呼入的短号码，逗号分隔。此项为空，表示所有的短号码都拦截。
access_short_num = 

#检查中心端释放号码请求是否有效的url
release_check_url= http://d.sungoin.com/provider/userWebservice/cleanData.action

special_orig = 400123456-31233490,**********-31233491

#是否允许呼叫手机
allow_callee_mobile = true

#指定小号（400号码:小号，400号码:小号）
special_orig=

#原始被叫是否用400号码
orig_use_netphone = true
#400彩铃文件
cloud_storage_user=400file
cloud_storage_password=400file

sync_file_close=1

#允许00开通的400号码（已废弃，采用号码配置中维护）
foreign_prefix_num=400123456,400555645

#忽略主叫号码验证(主叫号码，逗号分隔)
ignor_check_caller=

#被叫手机加本地区号（优先级高，true：加，false：不加）
mobile_plus_local_area = true

#本地固话保留主叫区号
keep_caller_local_district = false

#主叫号码添加国家代号
add_caller_nation_code = false

#测试环境用
in_test = true

#sip服务器连接延时
sip_connect_delay=1500

#二销号码摘机后放音
second_sale_offhook = false

auto_test_caller = 31233412

#通信服务请求地址
platform_client_endpoint = http://localhost:10026/message/

#零次呼用的主叫号码
zero_caller = 31233412

#零次呼放音延迟时间（毫秒）
zero_delay_time = 5000

#特殊主叫平台处理 默认false
special_prefix=false

#防伪码验证地址
security_check_url = https://platform.400vv.com/platform-service/interact/remote/securityCodeCheck