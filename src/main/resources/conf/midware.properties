# To change this license header, choose License Headers in Project Properties.
# To change this template file, choose Tools | Templates
# and open the template in the editor.

#检查彩铃文件是否存在
ring_file_check = false
#外呼失败重试的秒数（小于这个值会重新呼叫一次）
makecall_retry_seconds = -1
#语音文件的根目录
voc_data_path = /data
#导航收码超时秒数
ivr_timeout_seconds=30
#导航收码错误重试次数
ivr_retry_count = 3
#留言最长秒数
voicebox_max_seconds=60
#按键转移的命令符
dtmf_trans_key = **
#坐席提示音播放秒数
agent_alert_ring_time = 5
#排队超时提示音重复次数
time_out_input_count = 2
#是否用转移呼叫方式外呼
call_type_trans = true
#最大导航数(超过数值,根导航采用原生sql方式获取)
max_dept_count = 20

#中心端web服务路径
platform_endpoint = http://*************:8080/platform_client/service/
#中心端socket地址
platform_socket_ip = *************
#中心端socekt端口
platform_socket_port = 16999
#中心端http请求失败重试次数
http_repeat_count = 3

#中心端web服务名
service_calledOnhook = calledOnhook
service_calledOffhook = connectNotice
service_missedCall = lastNotConnentPhone

#skt socket地址
skt_socket_ip = *************
#skt socekt端口
skt_socket_port = 16999

#尚景提示音按键码
support_ivr_key = 000

#sip 标识
sip_flag = true
#sip 留言外部路径
sip_voicebox_path = /usr/local/xswitch-6.0.7/data

#按键转导航的命令符
dtmf_ivr_key = 1*