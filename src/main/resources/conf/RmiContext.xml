<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">
	<!-- 主服务器用的配置 -->
	<bean id="serviceExporter" class="org.springframework.remoting.rmi.RmiServiceExporter">  
		<property name="service" ref="RmiService" />  
		<property name="serviceName" value="RmiService" />  
		<property name="serviceInterface" value="com.sungoin.netphoneLocal.business.rmi.RmiService" />  
		<property name="registryPort" value="8088" />  
	</bean>  
	
	<!-- 备份务器用的配置 -->
<!--	<bean id="RmiService" class="org.springframework.remoting.rmi.RmiProxyFactoryBean">  
		<property name="refreshStubOnConnectFailure" value="true" />
        <property name="serviceUrl" value="rmi://127.0.0.1:8088/RmiService" />  
        <property name="serviceInterface" value="com.sungoin.netphoneLocal.business.rmi.RmiService" />  
    </bean>-->
</beans>
