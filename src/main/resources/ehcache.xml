
<ehcache>
    <!-- Sets the path to the directory where cache .data files are created.

	 If the path is a Java System Property it is replaced by
	 its value in the running VM.

	 The following properties are translated:
	 user.home - User's home directory
	 user.dir - User's current working directory
	 java.io.tmpdir - Default temp file path
    <diskStore path="java.io.tmpdir"/> -->

    <!--Default Cache configuration. These will applied to caches programmatically created through
	the CacheManager.

	The following attributes are required:

	maxElementsInMemory            - Sets the maximum number of objects that will be created in memory
	eternal                        - Sets whether elements are eternal. If eternal,  timeouts are ignored and the
					 element is never expired.
	overflowToDisk                 - Sets whether elements can overflow to disk when the in-memory cache
					 has reached the maxInMemory limit.

	The following attributes are optional:
	timeToIdleSeconds              - Sets the time to idle for an element before it expires.
					 i.e. The maximum amount of time between accesses before an element expires
					 Is only used if the element is not eternal.
					 Optional attribute. A value of 0 means that an Element can idle for infinity.
					 The default value is 0.
	timeToLiveSeconds              - Sets the time to live for an element before it expires.
					 i.e. The maximum time between creation time and when an element expires.
					 Is only used if the element is not eternal.
					 Optional attribute. A value of 0 means that and <PERSON><PERSON> can live for infinity.
					 The default value is 0.
	diskPersistent                 - Whether the disk store persists between restarts of the Virtual Machine.
					 The default value is false.
	diskExpiryThreadIntervalSeconds- The number of seconds between runs of the disk expiry thread. The default value
					 is 120 seconds.
	-->
	<diskStore path="java.io.tmpdir"/>
	<!--	<cacheManagerPeerProviderFactory
	class="net.sf.ehcache.distribution.RMICacheManagerPeerProviderFactory"
	properties="peerDiscovery=manual,rmiUrls=//************:40001/hibernate|//************:40001/cluster"/>
<cacheManagerPeerListenerFactory
	class="net.sf.ehcache.distribution.RMICacheManagerPeerListenerFactory"
	properties="hostName=************,port=40001,socketTimeoutMillis=2000"/>-->
	
	<!-- 默认缓存 不支持集群 -->
	<defaultCache
		maxElementsInMemory="10000"
		eternal="false"
		overflowToDisk="true"
		timeToIdleSeconds="1800"/>

	<!-- 单机缓存 不支持集群 -->
	<cache name="single"
		   maxElementsInMemory="10000"
		   eternal="false"
		   timeToIdleSeconds="120"
		   overflowToDisk="true"/>
		
	<!-- 单机缓存 不支持集群 -->
	<cache name="queue"
		   maxElementsInMemory="5000"
		   eternal="false"
		   timeToIdleSeconds="120"
		   overflowToDisk="true"/>
	
	<cache name="connect"
			   maxElementsInMemory="5000"
			   eternal="false"
			   timeToIdleSeconds="7200"
			   overflowToDisk="true"/>
	
	<!-- 集群缓存 集群同步 -->
	<!--cache name="cluster"
		   maxElementsInMemory="10000"
		   eternal="false"
		   timeToIdleSeconds="60"
		   overflowToDisk="true">
			<cacheEventListenerFactory
			class="net.sf.ehcache.distribution.RMICacheReplicatorFactory"
			properties="replicateAsynchronously=true, replicatePuts=true,
		replicateUpdates=true, replicateUpdatesViaCopy=true, replicateRemovals=true" />
		<bootstrapCacheLoaderFactory class="net.sf.ehcache.distribution.RMIBootstrapCacheLoaderFactory"/>
	</cache-->
	
	<cache name="callProcess"
		   maxElementsInMemory="5000"
		   eternal="false"
		   timeToIdleSeconds="6000"
		   overflowToDisk="true"/>
	
	<cache name="agentState"   
		   maxEntriesLocalHeap="10000"
		   eternal="false"   
		   timeToIdleSeconds="60"
		   overflowToDisk="true"   
		   memoryStoreEvictionPolicy="LRU">  
		<cacheEventListenerFactory class="com.sungoin.netphoneLocal.util.AgentStateEventListenerFactory"/>
    </cache>
        
    <cache name="offline"
		   maxElementsInMemory="1000"
		   eternal="false"
		   timeToIdleSeconds="600"
		   overflowToDisk="true"/>
</ehcache>