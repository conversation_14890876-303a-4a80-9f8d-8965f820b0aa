package com.sungoin.netphoneLocal.business.service;

import java.util.Date;
import java.util.UUID;

import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.sungoin.cti.client.util.JsonHelper;
import com.sungoin.netphoneLocal.business.BaseTest;
import com.sungoin.netphoneLocal.business.po.TalkNote;


public class BaiduServiceTest extends BaseTest {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(BaiduServiceTest.class);
	
	@Autowired
	private BaiduService service;
	
	private static final String uuid=UUID.randomUUID().toString();
	private static final Date now=new Date();

	@Test
	public void testQueryIvrUrl() throws Exception {
		TalkNote talk=new TalkNote();
		talk.setCallerNo("***********");
		talk.setId(uuid);
		talk.setCustomerNo("4009635519");
		talk.setIncomingTime(now);
		LOGGER.info(JsonHelper.Object2Json(service.queryIvrInfo(talk, "")));
	} 
	
	@Test
	public void testQueryBindPhone() throws Exception {
		TalkNote talk=new TalkNote();
		talk.setCallerNo("***********");
		talk.setId(uuid);
		talk.setCustomerNo("4009635519");
		talk.setIncomingTime(now);
		LOGGER.info(JsonHelper.Object2Json(service.queryBindPhone(talk, "1")));
	}
	
}
