/**
 * 
 */
package com.sungoin.netphoneLocal.business.service;

import static org.junit.Assert.assertEquals;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import com.sungoin.netphoneLocal.business.BaseTest;
import com.sungoin.netphoneLocal.business.bean.AgentBean;
import com.sungoin.netphoneLocal.business.bean.BusinessConstants;
import com.sungoin.netphoneLocal.business.po.BindPhone;

/**
 * <AUTHOR>
 */
public class BusinessServiceTest extends BaseTest {

    @Resource
    BusinessService businessService;
    
    @Test
    public void testFindByUniqueName() {
        String excludeNo = "4008759583";
        String uniqueName = "aaa";
        String bindPhone = "9VTxE87Pv8UA8qRVGEBwoKgriMLrJ2SZejH0YZB45rc=";
        System.out.println("========================= start query " + System.currentTimeMillis());
        List<BindPhone> list = businessService.findMultipleBindPhone(bindPhone, uniqueName, excludeNo);
        System.out.println("========================= end query " + System.currentTimeMillis());
        System.out.println(list);
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BusinessService#createQueue(java.lang.String, java.lang.String)}
     * .
     */
    @Test
    public void testCreateQueue() throws Exception {
        String deptId = "2a87fad7-7d70-489f-8992-04277d4c9b91";
        String callerId = "12344";
        this.businessService.createQueue(deptId);
        assertEquals(1, this.businessService.getQueueSizeByDept(deptId));
        assertEquals(BusinessConstants.QUEUE_WAIT,
            this.businessService.getQueueStatus(deptId, callerId)[0]);

        Thread.sleep(21000);
        assertEquals(BusinessConstants.QUEUE_TIMEOUT,
            this.businessService.getQueueStatus(deptId, callerId)[0]);

        assertEquals(BusinessConstants.QUEUE_TIMEOUT,
            this.businessService.getQueueStatus(deptId, callerId)[0]);

        this.businessService.continueWait(deptId, callerId);
        assertEquals(BusinessConstants.QUEUE_WAIT,
            this.businessService.getQueueStatus(deptId, callerId)[0]);

        this.businessService.removeQueue(deptId, callerId, false);
        assertEquals(0, this.businessService.getQueueSizeByDept(deptId));

    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BusinessService#createQueue(java.lang.String, java.lang.String)}
     * .
     */
    @Test
    @Transactional
    public void testCreateQueue2() throws Exception {
        final String deptId = "d6a10f75-a6c5-43f4-967b-640cbe172369";
        this.businessService.getQueueSizeByDept(deptId);

        //        final BusinessService service = SpringHelper
        //            .getBean(BusinessService.class);

        new Thread() {
            @Override
            public void run() {
                BusinessServiceTest.this.businessService
                    .addToQueue(deptId, "1");
            }
        }.start();

        new Thread() {
            @Override
            public void run() {
                BusinessServiceTest.this.businessService
                    .addToQueue(deptId, "1");
            }
        }.start();

        new Thread() {
            @Override
            public void run() {
                BusinessServiceTest.this.businessService
                    .addToQueue(deptId, "1");
            }
        }.start();

        Thread.sleep(500);

        assertEquals(3, this.businessService.getQueueSizeByDept(deptId));
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BusinessService#putConnect(java.lang.String)}
     * .
     */
    @Test
    public void testPutConnect() throws Exception {
        this.businessService.getConnectCount("123");
        new Thread() {
            @Override
            public void run() {
                BusinessServiceTest.this.businessService.addConnect("123");
            }
        }.start();

        new Thread() {
            @Override
            public void run() {
                BusinessServiceTest.this.businessService.addConnect("123");
            }
        }.start();

        new Thread() {
            @Override
            public void run() {
                BusinessServiceTest.this.businessService.addConnect("123");
            }
        }.start();

        Thread.sleep(500);
        //        this.businessService.addConnect("123");
        assertEquals(3, this.businessService.getConnectCount("123"));
        //        this.businessService.addConnect("123");
        //        assertEquals(2, this.businessService.getConnectCount("123"));
        //        this.businessService.subtractConnect("123");
        //        assertEquals(1, this.businessService.getConnectCount("123"));
        //        this.businessService.subtractConnect("123");
        //        assertEquals(0, this.businessService.getConnectCount("123"));
        //        this.businessService.subtractConnect("123");
        //        assertEquals(0, this.businessService.getConnectCount("123"));
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BusinessService#subtractConnect(java.lang.String)}
     * .
     */
    @Test
    public void testSubtractConnect() throws Exception {
        System.out.println(this.businessService.getConnectCount("123"));
        this.businessService.addConnect("123");
        System.out.println(this.businessService.getConnectCount("123"));
        this.businessService.subtractConnect("123");
        System.out.println(this.businessService.getConnectCount("123"));
    }

    /**
     * Test method for {@link com.sungoin.netphoneLocal.business.service.BusinessService#main(java.lang.String[])}.
     */
    @Test
    public void testSort() throws Exception {
        List<AgentBean> beans = new ArrayList<AgentBean>();
       
        AgentBean bean4= new AgentBean();
        bean4.setCallerId("4");
        Thread.sleep(120);
        AgentBean bean2= new AgentBean();
        bean2.setCallerId("2");
        Thread.sleep(100);
        AgentBean bean1= new AgentBean();
        bean1.setCallerId("1");
        AgentBean bean3= new AgentBean();
        bean3.setCallerId("3");
        
      
        beans.add(bean1);
        beans.add(bean2);
        beans.add(bean3);
        beans.add(bean4);
        Collections.sort(beans);
        
        for (int i = 0; i < beans.size(); i++) {
            System.out.println(beans.get(i).getCallerId());
        }
    }
}
