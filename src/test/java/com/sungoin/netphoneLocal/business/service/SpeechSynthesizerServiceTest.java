package com.sungoin.netphoneLocal.business.service;

import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.sungoin.netphoneLocal.business.BaseTest;


public class SpeechSynthesizerServiceTest extends BaseTest {

	private static final Logger LOGGER = LoggerFactory.getLogger(SpeechSynthesizerServiceTest.class);
	
	@Autowired
	private SpeechSynthesizerService service;
	
	@Test
	public void testProcess() throws Exception {
		LOGGER.info(service.process("4009635519", "尚景测试 请按1 无 请按2 333 请按3 "));
//		LOGGER.info(service.process("4009635519", "欢迎致电商客通，你的满意我的责任！"));
//		LOGGER.info(service.process("4009635519", "欢迎致电百度，你的满意我的责任！"));
//		LOGGER.info(service.process("4009635519", "欢迎致电腾讯，你的满意我的责任！"));
//		LOGGER.info(service.process("4009635519", "欢迎致电阿里巴巴，你的满意我的责任！"));
//		Thread.currentThread().join();
	}

}
