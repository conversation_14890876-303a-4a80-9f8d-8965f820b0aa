/**
 * 
 */
package com.sungoin.netphoneLocal.business.service;

import static org.junit.Assert.assertNotNull;

import com.sungoin.netphoneLocal.business.bean.BusinessConstants;
import com.sungoin.netphoneLocal.business.po.BindPhone;
import com.sungoin.netphoneLocal.constants.FunctionSwitchConstants;
import com.sungoin.netphoneLocal.midware.service.MainProcessThread;
import com.sungoin.netphoneLocal.midware.service.ProcessService;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.sungoin.netphoneLocal.business.BaseTest;
import com.sungoin.netphoneLocal.business.bean.CallNumDto;

import java.util.Objects;


/**
 * <AUTHOR>
 *
 */
public class InterConnectionServiceTest extends BaseTest{

    private static final Logger log = LoggerFactory.getLogger(InterConnectionServiceTest.class);

    @Autowired
    private InterConnectionService interConnService;

    @Autowired
    private ProcessService service;

    @Autowired
    private BaseService baseService;

    /**
     * Test method for {@link com.sungoin.netphoneLocal.business.service.InterConnectionService#interconnectionProcess(java.lang.String, java.lang.String, java.lang.String)}.
     */
    @Test
    public void testInterconnectionProcess() throws Exception {
        CallNumDto dto= interConnService.interconnectionProcess("4008857222", "02115214346165", "***********");
        assertNotNull(dto);
    }

    @Test
    public void testHttpProcess() throws Exception {
        BindPhone currentBindPhone=new BindPhone();
        currentBindPhone.setOrigBindPhoneNo("***********");
        String data=currentBindPhone.getOrigBindPhoneNo().startsWith("0") ? "off" : service.getFunctionData(BusinessConstants.GLOBAL_IONN, FunctionSwitchConstants.CALLEE_GLOBAL_RULE);
        String location=currentBindPhone.getOrigBindPhoneNo().startsWith("0") ? "-" : service.getLocationByBindPhone(currentBindPhone.getOrigBindPhoneNo());
        if(Objects.equals("on", data)
                && StringUtils.startsWith(location, "广东")
                && (location.contains("联通") || location.contains("移动"))){
            String fixedCalleeNo=baseService.getDistrictNo(currentBindPhone.getOrigBindPhoneNo())+currentBindPhone.getOrigBindPhoneNo();
            System.out.println(String.format("广东地区，移动、联通被叫默认互联互通配置加被区号！,被叫全局加区号规则：%s，原始被叫：%s，处理后被叫：%s，地区：%s", data, currentBindPhone.getOrigBindPhoneNo(), fixedCalleeNo, location));
        }
    }

}
