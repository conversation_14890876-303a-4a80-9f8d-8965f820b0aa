/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.service;

import com.sungoin.netphoneLocal.business.BaseTest;
import com.sungoin.netphoneLocal.business.po.NumberFuncConfig;
import javax.annotation.Resource;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 */
public class NumberFuncConfigServiceTest extends BaseTest {
    @Resource
    private NumberFuncConfigService service;
    
    @Test
    public void testGetByNumberCode() {
        NumberFuncConfig config = service.getByNumberCode("**********");
        System.out.println(config.isIvrTransferFlag());
        System.out.println(config.containsShortNum("95562"));
        System.out.println(config.containsShortNum("10000"));
    }
}
