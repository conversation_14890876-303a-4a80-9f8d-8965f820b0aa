/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.fail;

import java.io.File;
import java.io.InputStream;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.io.FileUtils;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import com.sungoin.cloudstorage.client.CloudStorageClient;
import com.sungoin.cloudstorage.dto.DownLoadResponse;
import com.sungoin.netphoneLocal.business.BaseTest;
import com.sungoin.netphoneLocal.business.po.BindPhone;
import com.sungoin.netphoneLocal.business.po.BlackList;
import com.sungoin.netphoneLocal.business.po.CrbtRing;
import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.business.po.DeptTimePlan;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.config.CommonSettings;
import com.sungoin.netphoneLocal.constants.FunctionSwitchConstants;
import com.sungoin.netphoneLocal.midware.config.MidwareSettings;
import com.sungoin.netphoneLocal.util.HttpHelper;

/**
 * <AUTHOR>
 */
public class BaseServiceTest extends BaseTest {

    @Resource
    BaseService instance;

    @Resource
    CommonSettings commonSettings;
	
	@Resource
    MidwareSettings midwareSetting;
	
    public BaseServiceTest() {
    }

    @BeforeClass
    public static void setUpClass() {
    }

    @AfterClass
    public static void tearDownClass() {
    }

    @Before
    public void setUp() {
    }

    @After
    public void tearDown() {
    }

    @Test
    public void testGroupCall() {
        Dept dept = instance.findTopDeptByCustomerNoAndIvrKey("4006323090", "8");
        System.out.println(dept);
    }
    /**
     * Test of saveUser method, of class BusinessService.
     */
    @Test
    public void testSaveUser() {
        System.out.println("saveUser");
        User user = new User();
        user.setNumber("4000333333");
        user.setOriginalNo("33333333");
        User expResult = user;
        User result = this.instance.saveUser(user);
        assertEquals(expResult, result);
    }

    /**
     * Test of getUser method, of class BusinessService.
     */
    @Test
    @Transactional
    public void testGetUser() {
        System.out.println("getUser");
        String id = "3e004118-cf7d-4773-bda9-e03a710edef6";
        User user = this.instance.getUser(id);
        System.out.println(user.getBlackList().size());
        BlackList bl = user.getBlackList().get(0);
        this.instance.deleteBlackList(bl);
        user.getBlackList().remove(bl);
        System.out.println("===================");
        System.out.println(user.getBlackList().size());
    }

    /**
     * Test of getUserByNumber method, of class BusinessService.
     */
    @Test
    public void testGetUserByNumber() {
        System.out.println("getUserByNumber");
        String number = "4008393718";
        User result = this.instance.getUserByNumber(number);
		System.out.println(result.toString());
		System.out.println(commonSettings.getForeignPrefixNum());
		System.out.println(midwareSetting.getBoxPath());
        assertEquals(result.getNumber(), number);
    }

    /**
     * Test of getAll method, of class BusinessService.
     */
    @Test
    public void testGetAll() {
        System.out.println("getAll");
        List<User> result = this.instance.getAll();
        System.out.println(result);
        assertEquals(result.size(), 2);
    }

    /**
     * Test of getDept method, of class BusinessService.
     */
    @Test
    public void testGetDept() {
        System.out.println("getDept");
        String id = "";
        Dept expResult = null;
        Dept result = this.instance.getDept(id);
        assertEquals(expResult, result);
        // TODO review the generated test code and remove the default call to fail.
        fail("The test case is a prototype.");
    }

    /**
     * Test of saveDept method, of class BusinessService.
     */
    @Test
    public void testSaveDept() {
        System.out.println("saveDept");
        User user = this.instance.getUser("9acc41d4-2fe1-420d-9427-69f32723cf0a");
        Dept dept = new Dept();
        dept.setUser(user);
        dept.setCustomerNo(user.getNumber());
        dept.setDeptLsh(user.getNumber());//根部门
        this.instance.saveOrUpdateDept(dept);
    }

    @Test
    public void testUpdateUser() {
        User user = this.instance.getUserByNumber("4008779257");
        assertNotNull(user);
        String updateUser = "4008779257";
        assertEquals(updateUser, user.getUpdateUser());
        user.setUpdateUser("boss");
        this.instance.saveUser(user);
        user = this.instance.getUserByNumber("4008779257");
        assertNotEquals(updateUser, user.getUpdateUser());
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#getUserByOriginalNo(java.lang.String)}
     * .
     */
    @Test
    public void testGetUserByOriginalNo() throws Exception {
        User user = this.instance.getUserByOriginalNo(false, "3322", "112");
        assertNull(user);
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#getDistrictNo(java.lang.String)}
     * .
     */
    @Test
    public void testGetDistrictNo() throws Exception {
        assertEquals("021", this.instance.getDistrictNo("***********"));
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#deleteBindPhone(com.sungoin.netphoneLocal.business.po.Dept)}
     * .
     */
    @Test
    public void testDeleteBindPhone() throws Exception {
        this.instance.deleteBindPhone(this.instance.getDept("ffa04dbe-2dec-4ef4-90f6-1ab831f052c8"));
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#deleteDept(com.sungoin.netphoneLocal.business.po.Dept)}
     * .
     */
    @Test
    public void testDeleteDept() throws Exception {
        Dept dept = this.instance.getDept("9f3a4b85-e7fc-4fdf-aada-0de1d61d039c");
        this.instance.deleteDept(dept);
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#cleanDept(java.lang.String)}
     * .
     */
    @Test
    public void testCleanDept() throws Exception {
        this.instance.cleanDept("4008010134");
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#processCallerNo(java.lang.String)}
     * .
     */
    @Test
    public void testProcessCallerNo() throws Exception {
        System.out.println(this.instance.processCallerNo("***********"));
//        System.out.println(this.instance.processCallerNo("0***********"));
//        System.out.println(this.instance.processCallerNo("073166318523"));
//        System.out.println(this.instance.processCallerNo("010***********"));
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#processCallNo(java.lang.String)}
     * .
     */
    @Test
    public void testProcessCallNo() throws Exception {
        System.out.println(this.instance.processCallNo("0***********"));
        System.out.println(this.instance.processCallNo("021***********"));
        System.out.println(this.instance.processCallNo("***********"));
        System.out.println(this.instance.processCallNo("62358538"));
        System.out.println(this.instance.processCallNo("***********"));
        System.out.println(this.instance.processCallNo("0***********"));
        System.out.println(this.instance.processCallNo("073166318523"));
        System.out.println(this.instance.processCallNo("010***********"));
        System.out.println(this.instance.processCallNo("***********"));
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#syncDeptTimePlan(com.sungoin.netphoneLocal.business.po.DeptTimePlan)}
     * .
     */
    @Test
    public void testSyncDeptTimePlan() throws Exception {
        DeptTimePlan timePlan = new DeptTimePlan();
        timePlan.setId("581006b0-5b76-42d3-9d50-11dbcb31cc9e");
        timePlan.setCustomerNo("4008580580");
        timePlan.setDeptLsh("147258369");
        timePlan.setTimeTitle(9);
        timePlan.setStartTime("2015-08-11 14:46");
        timePlan.setEndTime("2015-09-14 14:46");
        this.instance.syncDeptTimePlan(timePlan);
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#getChildDeptBySatisfyTimePlan(com.sungoin.netphoneLocal.business.po.Dept, java.lang.String)}
     * .
     */
    @Transactional
    @Test
    public void testGetChildDeptBySatisfyTimePlan() throws Exception {
        Dept dept = this.instance.getDept("2a96904c-5fb2-47fc-9fa5-bfe0ddacc313");
        Dept childDept = this.instance.getChildDeptBySatisfyTimePlan(dept, "***********");
        System.out.println(childDept);
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#getIdleAgent(com.sungoin.netphoneLocal.business.po.Dept, java.lang.String)}
     * .
     */
    @Test
    public void testGetIdleAgent() throws Exception {
        Dept dept = this.instance.getDept("c35ba3cd-0fbc-4bdd-b9d4-bc87a860700d");
        List<BindPhone> agents = this.instance.getIdleAgent(dept, "***********");
        System.out.println(agents.size());
        for (BindPhone phone : agents) {
            System.out.println(phone);
        }

    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#resetAgentStatusAndCallNum()}
     * .
     */
    @Test
    public void testResetAgentStatus() throws Exception {
        this.instance.resetAgentStatus();
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#modifyAgentCallNum(java.lang.String)}
     * .
     */
    @Test
    public void testModifyAgentCallNum() throws Exception {
        this.instance.modifyAgentCallNum("b1aa1f72-0716-4c15-a83c-171ed596efea");
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#getStatusByCustomerNo(java.lang.String)}
     * .
     */
    @Test
    public void testGetStatusByCustomerNo() throws Exception {
        List<Object[]> list = this.instance.getStatusByCustomerNo("4000000182");
        assertNotNull(list);

        for (int i = 0; i < list.size(); i++) {
            Object[] obj = list.get(i);
            System.out.println("agentId:" + obj[0] + " status:" + obj[1]);
        }
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#getBindPhoneByCustomerNoAndReportNum(java.lang.String, java.lang.String)}
     * .
     */
    @Test
    public void testGetBindPhoneByCustomerNoAndReportNum() throws Exception {
        System.out.println(this.instance.getBindPhoneByCustomerNoAndReportNum("4000000182", "2002"));
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#getAgentStatusMap(java.lang.String, java.lang.String)}
     * .
     */
    @Test
    public void testGetAgentStatusMap() throws Exception {
        System.out.println(this.instance.getAgentStatusMap("4008010134").size());
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#syncDept(com.sungoin.netphoneLocal.business.po.Dept)}
     * .
     */
    @Test
    public void testSyncDept() throws Exception {
        this.instance.syncDept(this.instance.getDept("c6e19bea-8dc4-481c-ab35-c295c2fb311f"));
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#getAllAgentsByDept(com.sungoin.netphoneLocal.business.po.Dept, java.lang.String)}
     * .
     */
    @Test
    public void testGetAllAgentsByDept() throws Exception {
        System.out.println(this.instance.getAllAgentsByDept(
            this.instance.getDept("5fe82b4d-9eff-4797-aa14-903a1fbc4ff2"), "31233460").size());
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#resetAgentCallNum()}
     * .
     */
    @Test
    public void testResetAgentCallNum() throws Exception {
        this.instance.resetAgentCallNum(0);
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#createFile(java.lang.String, java.lang.String)}
     * .
     */
    @Test
    public void testCreateFile() throws Exception {
        String lsh = "3af07616cfa14163ad8b1b09e4d5935e";
        String startTime = "2017-11-02";
        String cloudStorageUser = this.commonSettings.getCloudStorageUser();
        String cloudStoragePassword = this.commonSettings.getCloudStoragePassword();
        try {
            CloudStorageClient client = new CloudStorageClient(cloudStorageUser, cloudStoragePassword);
            DownLoadResponse rs = client.checkResource(lsh, startTime, System.currentTimeMillis());
            if ("0".equals(rs.getCode())) {
                InputStream is = client.downloadDefaultFile(lsh, startTime, System.currentTimeMillis())
                    .getInputStream();
                File file = new File("E:\\voices\\test.wav");
                FileUtils.copyInputStreamToFile(is, file);
            } else {
                System.out.println("文件不存在");
            }
        } catch (Exception e1) {
            e1.printStackTrace();
        }
    }

    @Test
    public void testInGlobalBlack() throws Exception {
        String caller = "10100000";
        boolean result = this.instance.inGlobalBlack(caller);
        assertEquals(result, true);

        caller = "65312602";
        result = this.instance.inGlobalBlack(caller);
        assertEquals(result, false);
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#createFile(java.lang.String, java.lang.String, java.lang.String)}
     * .
     */
    @Test
    public void testCreateFileStringStringString() throws Exception {
        String url = "http://file.sunke.com/cloudstorage/download/down.do?sign=155DB830DE4F9F4E4153E5E59BFE6056AE5C30135851E691B67E0E02436AE10BB4AE1872A962EFEBDB9AB841D401BE0BE80C8352E58D2DD7051A67B21FB4E570B9D21F5271FA3D06F1C0A6CF8D6902CFAD6D6862C85DA8E0F639C09E0C51FC1CD61C56F4F32701492CEC80010CCF5298F6C36FFB372AD4CFD03314736FCD98EC&cloudFileId=006996e5-b771-47c9-a310-dfb71f1f729c&createdTime=2018-07-03";
        File file = new File("E:\\voices\\test.wav");
        HttpHelper.httpDownloadFile(url, file);
        Thread.sleep(5000);

    }

    @Test
    public void testLog() {
        String s = this.instance.getDeptBindphoneDetails("4006615001", "4006615001");
        System.out.println(s);
    }

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.service.BaseService#findDeptCountByCustomerNo(java.lang.String)}
     * .
     */
    @Test
    public void testFindDeptCountByCustomerNo() throws Exception {
        System.out.println(this.instance.findDeptCountByCustomerNo("4008393718"));
    }

	@Test
	public void testGetRecordFlag() throws Exception {
        System.out.println(this.instance.getRecordFlag("4000971818","483064500"));
	}
	
	@Test
    public void testFindMainCallByNumber() throws Exception {
        List<String> list = this.instance.findMainCallByNumber("4008393718");
		System.out.println(list.contains("123456"));
    }
	
	@Test
    public void testFindDeptByCustomerNoAndLsh() throws Exception {
        Dept dept = this.instance.getDeptByCustomerNoAndDeptLsh("4008800002", "***********");
		System.out.println(dept.getId());
    }
	
	@Test
    public void testGetCustomerFunctionData() throws Exception {
        String data = this.instance.getCustomerFunctionData("4006236000", FunctionSwitchConstants.FUNCTION_IVR_TIMEOUT_KEY);
		System.out.println(data);
    }
    
    @Test
    @Transactional
    public void testFindCrbtRingWithTemplate() {
        Dept dept = this.instance.getDeptByCustomerNoAndDeptLsh("4000738289", "4000738289");
        System.out.println(dept.getCrbtRingList().size());
        CrbtRing cring = this.instance.findCrbtRingWithTemplate(dept);
        System.out.println(cring);
    }
    
    @Test
    @Transactional
    public void testBatchUpdateStatus() {
        int count = this.instance.batchModifyAgentStatus("4000000059","1acd677f-0870-421c-a610-8ffc658f3043", "busy", "标识1-2");
        System.out.println("count = " + count);
    }
}
