/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.netphoneLocal.business.service;

import com.sungoin.netphoneLocal.business.BaseTest;
import com.sungoin.netphoneLocal.business.po.BindPhone;
import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.util.CacheUtil;
import javax.annotation.Resource;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.junit.After;
import org.junit.AfterClass;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import static org.junit.Assert.*;
import org.springframework.transaction.annotation.Transactional;

/**
 *
 * <AUTHOR>
 */
public class HibernateCacheServiceTest extends BaseTest {

	@Resource
	BaseService instance;

	@Resource
	HibernateCacheService service;

	public HibernateCacheServiceTest() {
	}

	@BeforeClass
	public static void setUpClass() {
	}

	@AfterClass
	public static void tearDownClass() {
	}

	@Before
	public void setUp() {
	}

	@After
	public void tearDown() {
	}

	/**
	 * Test of clearCache method, of class HibernateCacheService.
	 */
	@Test
	@Transactional
	public void testClearCache() {
		User user = instance.getUser("a0b48639-7a47-45f7-aa00-708fb8f8509d");
		Dept dept = user.getRootDept();
		dept.getBindList().size();
		System.out.println(CacheUtil.loadCache(user.getClass().getName()).getKeys().size());
		System.out.println(CacheUtil.loadCache(user.getClass().getName() + ".depts").getKeys().size());
		System.out.println(CacheUtil.loadCache(Dept.class.getName()).getKeys().size());
		System.out.println(CacheUtil.loadCache(BindPhone.class.getName()).getKeys().size());
		System.out.println(CacheUtil.loadCache(Dept.class.getName()+".bindList").getKeys().size());

		service.clearCache(User.class);
		System.out.println("=====================================");

		System.out.println(CacheUtil.loadCache(user.getClass().getName()).getKeys().size());
		System.out.println(CacheUtil.loadCache(user.getClass().getName() + ".depts").getKeys().size());
		System.out.println(CacheUtil.loadCache(Dept.class.getName()).getKeys().size());
		System.out.println(CacheUtil.loadCache(BindPhone.class.getName()).getKeys().size());
		System.out.println(CacheUtil.loadCache(Dept.class.getName()+".bindList").getKeys().size());
	}

	/**
	 * Test of clearAllCache method, of class HibernateCacheService.
	 */
	@Test
	@Transactional
	public void testClearAllCache() {
		User user = instance.getUser("a0b48639-7a47-45f7-aa00-708fb8f8509d");
		Dept dept = user.getRootDept();
		dept.getBindList().size();
		System.out.println(CacheUtil.loadCache(user.getClass().getName()).getKeys().size());
		System.out.println(CacheUtil.loadCache(user.getClass().getName() + ".depts").getKeys().size());
		System.out.println(CacheUtil.loadCache(Dept.class.getName()).getKeys().size());
		System.out.println(CacheUtil.loadCache(BindPhone.class.getName()).getKeys().size());
		System.out.println(CacheUtil.loadCache(Dept.class.getName()+".bindList").getKeys().size());

		service.clearAllCache();
		System.out.println("=====================================");

		System.out.println(CacheUtil.loadCache(user.getClass().getName()).getKeys().size());
		System.out.println(CacheUtil.loadCache(user.getClass().getName() + ".depts").getKeys().size());
		System.out.println(CacheUtil.loadCache(Dept.class.getName()).getKeys().size());
		System.out.println(CacheUtil.loadCache(BindPhone.class.getName()).getKeys().size());
		System.out.println(CacheUtil.loadCache(Dept.class.getName()+".bindList").getKeys().size());
	}

	@Test
	@Transactional
	public void testClearDeptCache() {
		Dept dept = instance.getDept("889f90ac-ebe7-49d0-8add-f653a208cc57");
		dept.getChildDepts().size();
		System.out.println("===========================");
		System.out.println(CacheUtil.loadCache(Dept.class.getName()).getKeys().size());
		System.out.println(CacheUtil.loadCache(Dept.class.getName()+".childDepts").getKeys().size());
		
		service.clearCache(Dept.class, dept.getId());
		System.out.println(CacheUtil.loadCache(Dept.class.getName()).getKeys().size());
		System.out.println(CacheUtil.loadCache(Dept.class.getName()+".childDepts").getKeys().size());
	}
}
