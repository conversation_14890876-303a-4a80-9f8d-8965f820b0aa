/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.business.service;

import com.sungoin.netphoneLocal.business.BaseTest;
import com.sungoin.netphoneLocal.business.po.AreaStrategyTemplate;
import com.sungoin.netphoneLocal.business.po.TimeStrategyTemplate;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 */
public class TemplateServiceTest extends BaseTest {
    @Resource
    private TemplateService service;
    
    @Test
    public void testGet() {
        AreaStrategyTemplate bindPhoneAreaTemplate = service.findBindPhoneAreaTemplate("**********", "d1d522df-d667-4879-a9ef-8ad9c5372023");
        System.out.println(bindPhoneAreaTemplate);
        List<TimeStrategyTemplate> bindPhoneTimeTemplates = service.findBindPhoneTimeTemplate("**********", "d1d522df-d667-4879-a9ef-8ad9c5372023");
        System.out.println(bindPhoneTimeTemplates);
    }
}
