/**
 * 
 */
package com.sungoin.netphoneLocal.business.rmi.impl;

import javax.annotation.Resource;

import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sungoin.netphoneLocal.business.BaseTest;
import com.sungoin.netphoneLocal.business.po.User;
import com.sungoin.netphoneLocal.business.rmi.RmiService;

/**
 * <AUTHOR>
 */
public class RmiServiceImplTest extends BaseTest {
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = LoggerFactory
        .getLogger(RmiServiceImplTest.class);

    @Resource
    RmiService rmiService;

    /**
     * Test method for
     * {@link com.sungoin.netphoneLocal.business.rmi.impl.RmiServiceImpl#getUserByNumber(java.lang.String)}
     * .
     */
    @Test
    public void testGetUserByNumber() throws Exception {
        LOGGER.debug("class name:{}", this.rmiService.getClass());
        User user = this.rmiService.getUserByNumber("4000000182");
        System.out.println(user);
    }
}
