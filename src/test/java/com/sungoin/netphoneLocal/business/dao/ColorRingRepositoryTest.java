/**
 * 
 */
package com.sungoin.netphoneLocal.business.dao;

import static org.junit.Assert.assertNotNull;

import org.junit.Test;

import com.sungoin.netphoneLocal.business.BaseTest;

/**
 * <AUTHOR>
 */
public class ColorRingRepositoryTest extends BaseTest {

    /**
     * Test method for
     * {@link ColorRingRepository#findColorRingByCustomerNoAndLsh(String, String)}
     * .
     */
    @Test
    public void testFindByCustomerNoAndLsh() throws Exception {
        assertNotNull(this.ringDao.findByCustomerNoAndLsh("4000150577",
            "116888190"));
    }
}
