package com.sungoin.netphoneLocal.business;

import javax.annotation.Resource;

import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.sungoin.netphoneLocal.boot.Application;
import com.sungoin.netphoneLocal.business.dao.ColorRingRepository;
import com.sungoin.netphoneLocal.business.dao.SecondSaleNoRepository;
import com.sungoin.netphoneLocal.business.po.SecondSaleNo;
import java.util.List;
import org.junit.Test;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = Application.class)
public class BaseTest {

    @Resource
    public ColorRingRepository ringDao;

    @Resource
    public SecondSaleNoRepository secondSaleDao;
    
    @Test
    public void test() {
        List<SecondSaleNo> list = secondSaleDao.findByOriginalNo("801080291");
        System.out.println(list);
    }
}
