/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service;

import com.sungoin.netphoneLocal.business.BaseTest;
import com.sungoin.netphoneLocal.business.po.Dept;
import com.sungoin.netphoneLocal.midware.service.cpcc.CpccCustomerDataDto;
import com.sungoin.netphoneLocal.midware.service.cpcc.CpccProcessService;
import javax.annotation.Resource;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 */
public class CpccProcessServiceTest extends BaseTest {
    @Resource
    private CpccProcessService service;
    
    @Test
    public void testGetIvr() {
        String ivr = service.getIvrKeyByCaller("**********", "***********");
        System.out.println(ivr);
    }
    
        
    @Test
    public void testGetDeptByIvr() {
        Dept d = service.getDeptByCustomerNoAndIvrKeyAndDepth("**********", "3", 3);
        System.out.println(d);
    }
    
    
    @Test
    public void testAddIvr() {
        CpccCustomerDataDto dto = new CpccCustomerDataDto();
        dto.setNumberCode("**********");
        dto.setCaller("31233355");
        dto.setIvrKey("3");
        service.addCallno(dto);
    }
}
