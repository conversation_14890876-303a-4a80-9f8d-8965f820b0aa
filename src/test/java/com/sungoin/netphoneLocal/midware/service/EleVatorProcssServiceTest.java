/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service;

import com.sungoin.netphoneLocal.business.BaseTest;
import com.sungoin.netphoneLocal.midware.service.elevator.ElevatorProcessService;
import com.sungoin.netphoneLocal.midware.service.elevator.ElevatorUserInfo;
import com.sungoin.netphoneLocal.midware.service.elevator.OnhookCreateInfo;
import javax.annotation.Resource;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 */
public class EleVatorProcssServiceTest extends BaseTest {
    @Resource
    private ElevatorProcessService instance;
    
    @Test
    public void testGetUserInfo() {
        ElevatorUserInfo resp = instance.getUserInfo("000001");
        System.out.println(resp);
        
//        OnhookCreateInfo info = instance.pushOnhookInfo("000251", "***********", "2023-12-12 15:52:08");
//        System.out.println(info);
    }
}
