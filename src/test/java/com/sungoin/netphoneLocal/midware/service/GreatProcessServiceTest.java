/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service;

import com.sungoin.netphoneLocal.business.BaseTest;
import com.sungoin.netphoneLocal.midware.service.great.GreatProcessService;
import com.sungoin.netphoneLocal.midware.service.great.RequestDto;
import com.sungoin.netphoneLocal.midware.service.great.ResponseDto;
import javax.annotation.Resource;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 */
public class GreatProcessServiceTest extends BaseTest {

    @Resource
    private GreatProcessService instance;
    
    @Test
    public void testGetResponseDto() {
        RequestDto dto = new RequestDto("1", "852963");
        ResponseDto resp = instance.getResult(dto);
        System.out.println(resp.getCode() + " " + resp.getMsg());
    }
}
