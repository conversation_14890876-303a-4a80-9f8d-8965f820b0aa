/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.netphoneLocal.midware.service;

import com.sungoin.netphoneLocal.business.BaseTest;
import com.sungoin.netphoneLocal.config.SungoinSetting;
import com.sungoin.netphoneLocal.midware.service.sungoin.HotlineCallInDto;
import com.sungoin.netphoneLocal.midware.service.sungoin.SungoinProcessService;
import java.util.Date;
import javax.annotation.Resource;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 */
public class SungoinProcessServiceTest extends BaseTest {
    
    @Resource
    private SungoinProcessService instance;
    
    @Resource
    private SungoinSetting sungoinSetting;
    
    @Test
    public void testGetCallee() {
        HotlineCallInDto dto = new HotlineCallInDto();
//        dto.setNumberCode("400251252");
//        dto.setCaller("***********");
//        dto.setIncommTime(new Date().getTime());
//        dto.setUqid("fb052949-a10c-4340-bcb1-63f5f3b3e25b");
//        String callee = instance.getHotlineCallee(dto);
        System.out.println("callee = " + sungoinSetting.getApiCallTimeOut());
    }
}
