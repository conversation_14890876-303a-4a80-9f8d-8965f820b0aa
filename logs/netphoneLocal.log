13:27:12.159 [main] INFO com.sun.net.bus.BaseTest Starting BaseTest on DESKTOP-S3MQHCU with PID 18316 (started by Administrator in D:\work\project\netphonelocal)
13:27:12.163 [main] DEBUG com.sun.net.bus.BaseTest Running with Spring Boot v1.5.2.RELEASE, Spring v4.3.7.RELEASE
13:27:12.163 [main] INFO com.sun.net.bus.BaseTest No active profile set, falling back to default profiles: default
13:27:21.793 [main] INFO com.sun.net.bus.BaseTest Started BaseTest in 9.8 seconds (JVM running for 10.892)
13:27:21.937 [main] DEBUG com.sun.net.bus.rmi.imp.RmiServiceImplTest class name:class com.sungoin.netphoneLocal.business.rmi.impl.RmiServiceImpl
13:27:22.069 [main] ERROR com.sun.net.bus.ser.BaiduService **********:��ѯ�󶨺����쳣:
java.lang.StringIndexOutOfBoundsException: String index out of range: 12
	at java.lang.String.checkBounds(String.java:385) ~[?:1.8.0_452]
	at java.lang.String.<init>(String.java:545) ~[?:1.8.0_452]
	at com.sungoin.netphoneLocal.util.CtiDes.hexStr2ByteArr(CtiDes.java:70) ~[classes/:?]
	at com.sungoin.netphoneLocal.util.CtiDes.decrypt(CtiDes.java:114) ~[classes/:?]
	at com.sungoin.netphoneLocal.business.service.BaiduService.queryBindPhone(BaiduService.java:45) [classes/:?]
	at com.sungoin.netphoneLocal.business.service.BaiduServiceTest.testQueryBindPhone(BaiduServiceTest.java:43) [test-classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50) [junit-4.12.jar:4.12]
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12) [junit-4.12.jar:4.12]
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47) [junit-4.12.jar:4.12]
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:94) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:283) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:173) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:153) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:128) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:203) [surefire-booter-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:155) [surefire-booter-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:103) [surefire-booter-2.18.1.jar:2.18.1]
13:27:22.079 [main] INFO com.sun.net.bus.ser.BaiduServiceTest null
13:27:22.082 [main] ERROR com.sun.net.bus.ser.BaiduService **********:��ѯIVR�쳣:
java.lang.StringIndexOutOfBoundsException: String index out of range: 12
	at java.lang.String.checkBounds(String.java:385) ~[?:1.8.0_452]
	at java.lang.String.<init>(String.java:545) ~[?:1.8.0_452]
	at com.sungoin.netphoneLocal.util.CtiDes.hexStr2ByteArr(CtiDes.java:70) ~[classes/:?]
	at com.sungoin.netphoneLocal.util.CtiDes.decrypt(CtiDes.java:114) ~[classes/:?]
	at com.sungoin.netphoneLocal.business.service.BaiduService.queryIvrInfo(BaiduService.java:28) [classes/:?]
	at com.sungoin.netphoneLocal.business.service.BaiduServiceTest.testQueryIvrUrl(BaiduServiceTest.java:33) [test-classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50) [junit-4.12.jar:4.12]
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12) [junit-4.12.jar:4.12]
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47) [junit-4.12.jar:4.12]
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:94) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:283) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:173) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:153) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:128) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:203) [surefire-booter-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:155) [surefire-booter-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:103) [surefire-booter-2.18.1.jar:2.18.1]
13:27:22.082 [main] INFO com.sun.net.bus.ser.BaiduServiceTest null
13:27:22.137 [main] DEBUG com.sun.net.bus.ser.BaseService modifyAgentCallNum bindPhoneId:b1aa1f72-0716-4c15-a83c-171ed596efea
13:27:22.187 [main] DEBUG com.sun.net.bus.ser.BaseService getRecordFlag customerNo:**********, lsh:483064500
13:27:22.340 [main] DEBUG com.sun.net.bus.ser.BaseService sync Dept :null
13:27:22.776 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo start callNo:013661636379
13:27:22.776 [main] DEBUG com.sun.net.bus.ser.BaseService ��ǰ����������ֻ��ӱ������ţ�021
13:27:22.776 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo end return processCallNo:02113661636379
13:27:22.776 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo start callNo:02113661636379
13:27:22.776 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo end return processCallNo:13661636379
13:27:22.776 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo start callNo:02152358538
13:27:22.776 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo end return processCallNo:52358538
13:27:22.777 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo start callNo:62358538
13:27:22.777 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo end return processCallNo:62358538
13:27:22.777 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo start callNo:13661636379
13:27:22.777 [main] DEBUG com.sun.net.bus.ser.BaseService ��ǰ����������ֻ��ӱ������ţ�021
13:27:22.777 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo end return processCallNo:02113661636379
13:27:22.777 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo start callNo:013000006379
13:27:22.777 [main] DEBUG com.sun.net.bus.ser.BaseService ��ǰ����������ֻ��ӱ������ţ�021
13:27:22.777 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo end return processCallNo:02113000006379
13:27:22.777 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo start callNo:073166318523
13:27:22.777 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo end return processCallNo:073166318523
13:27:22.777 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo start callNo:01013000006379
13:27:22.777 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo end return processCallNo:01013000006379
13:27:22.777 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo start callNo:13000006379
13:27:22.777 [main] DEBUG com.sun.net.bus.ser.BaseService ��ǰ����������ֻ��ӱ������ţ�021
13:27:22.777 [main] DEBUG com.sun.net.bus.ser.BaseService processCallNo end return processCallNo:02113000006379
13:27:22.981 [main] DEBUG com.sun.net.bus.ser.BaseService 4000333333:�ر����壬�����������
13:27:22.981 [main] DEBUG com.sun.net.bus.ser.BaseService cleanCrbtRing customerNo:4000333333
13:27:23.337 [main] DEBUG com.sun.net.bus.ser.BaseService getBindPhoneByCustomerNoAndReportNum customerNo:4000000182, reportNum:2002
13:27:23.378 [main] DEBUG com.sun.net.bus.ser.BaseService cleanDept customerNo:4008010134
13:27:23.400 [main] WARN com.sun.net.bus.ser.BaseService cleanDept δ�ҵ���Ӧ�û�,customerNo:4008010134
13:27:23.771 [main] DEBUG com.sun.net.bus.ser.BaseService delete bindPhone dept:null
13:27:23.966 [main] DEBUG com.sun.net.bus.ser.BaseService mobile :13661636379
13:27:24.789 [main] DEBUG com.sun.net.bus.ser.BaseService sync DeptTimePlan :DeptTimePlan [id=581006b0-5b76-42d3-9d50-11dbcb31cc9e, customerNo=4008580580, deptLsh=147258369]
13:27:25.158 [main] DEBUG com.sun.net.uti.HttpHelper ����߳�������http://file.sunke.com/cloudstorage/download/down.do?sign=155DB830DE4F9F4E4153E5E59BFE6056AE5C30135851E691B67E0E02436AE10BB4AE1872A962EFEBDB9AB841D401BE0BE80C8352E58D2DD7051A67B21FB4E570B9D21F5271FA3D06F1C0A6CF8D6902CFAD6D6862C85DA8E0F639C09E0C51FC1CD61C56F4F32701492CEC80010CCF5298F6C36FFB372AD4CFD03314736FCD98EC&cloudFileId=006996e5-b771-47c9-a310-dfb71f1f729c&createdTime=2018-07-03
13:27:25.158 [Thread-8] DEBUG com.sun.net.uti.HttpHelper ��ʼ����ļ����أ�http://file.sunke.com/cloudstorage/download/down.do?sign=155DB830DE4F9F4E4153E5E59BFE6056AE5C30135851E691B67E0E02436AE10BB4AE1872A962EFEBDB9AB841D401BE0BE80C8352E58D2DD7051A67B21FB4E570B9D21F5271FA3D06F1C0A6CF8D6902CFAD6D6862C85DA8E0F639C09E0C51FC1CD61C56F4F32701492CEC80010CCF5298F6C36FFB372AD4CFD03314736FCD98EC&cloudFileId=006996e5-b771-47c9-a310-dfb71f1f729c&createdTime=2018-07-03
13:27:25.349 [main] ERROR com.sun.net.uti.HttpHelper �ļ��������쳣��
java.io.IOException: Directory 'E:\voices' could not be created
	at org.apache.commons.io.FileUtils.openOutputStream(FileUtils.java:363) ~[commons-io-2.4.jar:2.4]
	at org.apache.commons.io.FileUtils.openOutputStream(FileUtils.java:326) ~[commons-io-2.4.jar:2.4]
	at org.apache.commons.io.FileUtils.copyInputStreamToFile(FileUtils.java:1510) ~[commons-io-2.4.jar:2.4]
	at com.sungoin.netphoneLocal.util.HttpHelper.httpDownloadFile(HttpHelper.java:384) [classes/:?]
	at com.sungoin.netphoneLocal.business.service.BaseServiceTest.testCreateFileStringStringString(BaseServiceTest.java:439) [test-classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50) [junit-4.12.jar:4.12]
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12) [junit-4.12.jar:4.12]
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47) [junit-4.12.jar:4.12]
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17) [junit-4.12.jar:4.12]
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:94) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268) [junit-4.12.jar:4.12]
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:283) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:173) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:153) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:128) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:203) [surefire-booter-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:155) [surefire-booter-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:103) [surefire-booter-2.18.1.jar:2.18.1]
13:27:30.366 [main] DEBUG com.sun.net.bus.ser.BaseService processCallerNo start callNo:17321180674
13:27:30.366 [main] DEBUG com.sun.net.bus.ser.BaseService mobile :17321180674
13:27:30.398 [main] DEBUG com.sun.net.bus.ser.BaseService processCallerNo end return processCallNo:00017321180674
13:27:30.699 [main] INFO com.sun.net.bus.ser.BaseService ����Ĳ���Id����:[["142fefe5-5d43-4bb0-8ee3-e5ac7b1c79a3"],["1a79889c-bb23-4145-8d28-1af809886baa"],["e7c48501-1474-4b24-afc1-2b8aa46b49a4"]]
13:27:32.450 [main] DEBUG com.sun.net.bus.ser.BaseService mobile :13005356379
13:27:32.467 [main] DEBUG com.sun.net.bus.ser.BaseService mobile :13005356379
13:27:32.553 [main] INFO com.sun.net.bus.ser.SpeechSynthesizerService **********:��Ҫ�ϳɵ���������:�о����� �밴1 �� �밴2 333 �밴3 
13:27:32.571 [main] INFO com.sun.net.bus.ser.SpeechSynthesizerService **********:���ҵ���ʷ�ϳ��ļ�
13:27:32.571 [main] INFO com.sun.net.bus.ser.SpeechSynthesizerService **********:�ϳ�����·��:/data/voices/rings\**********\1719900775847_dest.wav
13:27:32.572 [main] INFO com.sun.net.bus.ser.SpeechSynthesizerService akId:y90RRGflJB1a9sUa,akSecret:CKhviD0ZgIGZF6PX1w8ZvKFKO25eUj
13:27:32.799 [main] INFO com.sun.net.bus.ser.SpeechSynthesizerService get token: 286ded9ed4d44f69b03ae7d7e11c30c1, expire time: 1749922054
13:27:33.321 [main] INFO com.sun.net.bus.ser.SpeechSynthesizerService tts start latency 3 ms
13:27:33.431 [nioEventLoopGroup-2-1] INFO com.sun.net.bus.ser.SpeechSynthesizerService tts first latency : 534 ms
13:27:34.337 [nioEventLoopGroup-2-1] INFO com.sun.net.bus.ser.SpeechSynthesizerService name: SynthesisCompleted, status: 20000000, output file :D:\data\voices\rings\**********\1719900775847_dest.wav
13:27:35.166 [main] INFO com.sun.net.bus.ser.SpeechSynthesizerService tts stop latency 1848 ms
13:27:35.252 [main] INFO com.sun.net.bus.ser.SpeechSynthesizerServiceTest /data/voices/rings\**********\1719900775847_dest.wav
13:27:35.666 [main] DEBUG com.sun.net.uti.HttpHelper  ��������:sos_position_codeֵ000001
13:27:35.922 [main] ERROR com.sun.net.uti.HttpHelper ����https://www.great-device.com/rjhj/prod-api/open/400?type=1&code=852963������ ��Connection reset
javax.net.ssl.SSLException: Connection reset
	at sun.security.ssl.Alert.createSSLException(Alert.java:127) ~[?:1.8.0_452]
	at sun.security.ssl.TransportContext.fatal(TransportContext.java:331) ~[?:1.8.0_452]
	at sun.security.ssl.TransportContext.fatal(TransportContext.java:274) ~[?:1.8.0_452]
	at sun.security.ssl.TransportContext.fatal(TransportContext.java:269) ~[?:1.8.0_452]
	at sun.security.ssl.SSLTransport.decode(SSLTransport.java:138) ~[?:1.8.0_452]
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1401) ~[?:1.8.0_452]
	at sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1309) ~[?:1.8.0_452]
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:440) ~[?:1.8.0_452]
	at org.apache.http.conn.ssl.SSLSocketFactory.connectSocket(SSLSocketFactory.java:535) ~[httpclient-4.3.6.jar:4.3.6]
	at org.apache.http.conn.ssl.SSLSocketFactory.connectSocket(SSLSocketFactory.java:403) ~[httpclient-4.3.6.jar:4.3.6]
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:177) ~[httpclient-4.3.6.jar:4.3.6]
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144) ~[httpclient-4.3.6.jar:4.3.6]
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:131) ~[httpclient-4.3.6.jar:4.3.6]
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:611) ~[httpclient-4.3.6.jar:4.3.6]
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:446) ~[httpclient-4.3.6.jar:4.3.6]
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:863) ~[httpclient-4.3.6.jar:4.3.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:82) ~[httpclient-4.3.6.jar:4.3.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:106) ~[httpclient-4.3.6.jar:4.3.6]
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:57) ~[httpclient-4.3.6.jar:4.3.6]
	at com.sungoin.netphoneLocal.util.HttpHelper.postHttpsParams(HttpHelper.java:555) [classes/:?]
	at com.sungoin.netphoneLocal.midware.service.great.GreatProcessService.getResult(GreatProcessService.java:54) [classes/:?]
	at com.sungoin.netphoneLocal.midware.service.great.GreatProcessService$$FastClassBySpringCGLIB$$b25a635a.invoke(<generated>) [classes/:?]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) [spring-core-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:652) [spring-aop-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at com.sungoin.netphoneLocal.midware.service.great.GreatProcessService$$EnhancerBySpringCGLIB$$4a2eefd.getResult(<generated>) [classes/:?]
	at com.sungoin.netphoneLocal.midware.service.GreatProcessServiceTest.testGetResponseDto(GreatProcessServiceTest.java:26) [test-classes/:?]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50) [junit-4.12.jar:4.12]
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12) [junit-4.12.jar:4.12]
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47) [junit-4.12.jar:4.12]
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:94) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58) [junit-4.12.jar:4.12]
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.junit.runners.ParentRunner.run(ParentRunner.java:363) [junit-4.12.jar:4.12]
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:283) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:173) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:153) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:128) [surefire-junit4-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:203) [surefire-booter-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:155) [surefire-booter-2.18.1.jar:2.18.1]
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:103) [surefire-booter-2.18.1.jar:2.18.1]
	Suppressed: java.net.SocketException: Connection reset by peer: socket write error
		at java.net.SocketOutputStream.socketWrite0(Native Method) ~[?:1.8.0_452]
		at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111) ~[?:1.8.0_452]
		at java.net.SocketOutputStream.write(SocketOutputStream.java:155) ~[?:1.8.0_452]
		at sun.security.ssl.SSLSocketOutputRecord.encodeAlert(SSLSocketOutputRecord.java:81) ~[?:1.8.0_452]
		at sun.security.ssl.TransportContext.fatal(TransportContext.java:362) ~[?:1.8.0_452]
		at sun.security.ssl.TransportContext.fatal(TransportContext.java:274) ~[?:1.8.0_452]
		at sun.security.ssl.TransportContext.fatal(TransportContext.java:269) ~[?:1.8.0_452]
		at sun.security.ssl.SSLTransport.decode(SSLTransport.java:138) ~[?:1.8.0_452]
		at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1401) ~[?:1.8.0_452]
		at sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1309) ~[?:1.8.0_452]
		at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:440) ~[?:1.8.0_452]
		at org.apache.http.conn.ssl.SSLSocketFactory.connectSocket(SSLSocketFactory.java:535) ~[httpclient-4.3.6.jar:4.3.6]
		at org.apache.http.conn.ssl.SSLSocketFactory.connectSocket(SSLSocketFactory.java:403) ~[httpclient-4.3.6.jar:4.3.6]
		at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:177) ~[httpclient-4.3.6.jar:4.3.6]
		at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144) ~[httpclient-4.3.6.jar:4.3.6]
		at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:131) ~[httpclient-4.3.6.jar:4.3.6]
		at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:611) ~[httpclient-4.3.6.jar:4.3.6]
		at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:446) ~[httpclient-4.3.6.jar:4.3.6]
		at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:863) ~[httpclient-4.3.6.jar:4.3.6]
		at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:82) ~[httpclient-4.3.6.jar:4.3.6]
		at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:106) ~[httpclient-4.3.6.jar:4.3.6]
		at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:57) ~[httpclient-4.3.6.jar:4.3.6]
		at com.sungoin.netphoneLocal.util.HttpHelper.postHttpsParams(HttpHelper.java:555) [classes/:?]
		at com.sungoin.netphoneLocal.midware.service.great.GreatProcessService.getResult(GreatProcessService.java:54) [classes/:?]
		at com.sungoin.netphoneLocal.midware.service.great.GreatProcessService$$FastClassBySpringCGLIB$$b25a635a.invoke(<generated>) [classes/:?]
		at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:204) [spring-core-4.3.7.RELEASE.jar:4.3.7.RELEASE]
		at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:652) [spring-aop-4.3.7.RELEASE.jar:4.3.7.RELEASE]
		at com.sungoin.netphoneLocal.midware.service.great.GreatProcessService$$EnhancerBySpringCGLIB$$4a2eefd.getResult(<generated>) [classes/:?]
		at com.sungoin.netphoneLocal.midware.service.GreatProcessServiceTest.testGetResponseDto(GreatProcessServiceTest.java:26) [test-classes/:?]
		at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_452]
		at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_452]
		at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_452]
		at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_452]
		at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:50) [junit-4.12.jar:4.12]
		at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12) [junit-4.12.jar:4.12]
		at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:47) [junit-4.12.jar:4.12]
		at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17) [junit-4.12.jar:4.12]
		at org.springframework.test.context.junit4.statements.RunBeforeTestMethodCallbacks.evaluate(RunBeforeTestMethodCallbacks.java:75) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
		at org.springframework.test.context.junit4.statements.RunAfterTestMethodCallbacks.evaluate(RunAfterTestMethodCallbacks.java:86) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
		at org.springframework.test.context.junit4.statements.SpringRepeat.evaluate(SpringRepeat.java:84) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
		at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:325) [junit-4.12.jar:4.12]
		at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:252) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
		at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:94) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
		at org.junit.runners.ParentRunner$3.run(ParentRunner.java:290) [junit-4.12.jar:4.12]
		at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:71) [junit-4.12.jar:4.12]
		at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:288) [junit-4.12.jar:4.12]
		at org.junit.runners.ParentRunner.access$000(ParentRunner.java:58) [junit-4.12.jar:4.12]
		at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:268) [junit-4.12.jar:4.12]
		at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
		at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
		at org.junit.runners.ParentRunner.run(ParentRunner.java:363) [junit-4.12.jar:4.12]
		at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:191) [spring-test-4.3.7.RELEASE.jar:4.3.7.RELEASE]
		at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:283) [surefire-junit4-2.18.1.jar:2.18.1]
		at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:173) [surefire-junit4-2.18.1.jar:2.18.1]
		at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:153) [surefire-junit4-2.18.1.jar:2.18.1]
		at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:128) [surefire-junit4-2.18.1.jar:2.18.1]
		at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:203) [surefire-booter-2.18.1.jar:2.18.1]
		at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:155) [surefire-booter-2.18.1.jar:2.18.1]
		at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:103) [surefire-booter-2.18.1.jar:2.18.1]
Caused by: java.net.SocketException: Connection reset
	at java.net.SocketInputStream.read(SocketInputStream.java:210) ~[?:1.8.0_452]
	at java.net.SocketInputStream.read(SocketInputStream.java:141) ~[?:1.8.0_452]
	at sun.security.ssl.SSLSocketInputRecord.read(SSLSocketInputRecord.java:464) ~[?:1.8.0_452]
	at sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:165) ~[?:1.8.0_452]
	at sun.security.ssl.SSLTransport.decode(SSLTransport.java:109) ~[?:1.8.0_452]
	... 51 more
13:27:35.968 [main] DEBUG com.sun.net.bus.ser.BaseService processCallerNo start callNo:
