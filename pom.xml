<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.sungoin</groupId>
	<artifactId>netphoneLocal</artifactId>
	<!--<version>4.0.9</version>-->
    <version>1.0-SNAPSHOT</version>
	<packaging>jar</packaging>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
	</properties>
	<name>netphoneLocal_v4</name>
	<description>400运营平台</description>
	
	<!-- Inherit defaults from Spring Boot -->
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>1.5.2.RELEASE</version>
	</parent>
	
	<!-- Add typical dependencies for a web application -->
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion> 
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-classic</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>log4j-over-slf4j</artifactId>
				</exclusion>
			</exclusions> 
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- log4j2 -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.17.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.17.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
			<version>2.17.0</version>
		</dependency>
		<!--<dependency>
			<groupId>ojdbc</groupId>
			<artifactId>ojdbc</artifactId>
			<version>6</version>
		</dependency>-->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-ehcache</artifactId>
		</dependency>
		<dependency>
			<groupId>net.sf.ehcache</groupId>
			<artifactId>ehcache-core</artifactId>
			<version>2.6.10</version>
		</dependency>
		<dependency>
			<groupId>org.apache.mina</groupId>
			<artifactId>mina-core</artifactId>
			<version>2.0.9</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.1</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.4</version>
		</dependency>
		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>3.3</version>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.3.6</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient-cache</artifactId>
			<version>4.3.6</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpmime</artifactId>
			<version>4.3.6</version>
		</dependency>
		<!--wav to mp3-->
		<dependency>
			<groupId>com.jave</groupId>
			<artifactId>jave</artifactId>
			<version>1.0.2</version>
		</dependency>
		<dependency>
			<groupId>com.sungoin.cti</groupId>
			<artifactId>cti-client</artifactId>
			<version>1.3.17</version>
		</dependency>
		<dependency>
			<groupId>com.sungoin.cloudstorage</groupId>
			<artifactId>cloudstorage-client</artifactId>
			<version>1.2.9.final</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.scala-lang</groupId>
			<artifactId>scala-library</artifactId>
			<version>2.10.4</version>
		</dependency>
		<dependency>
		    <groupId>com.alibaba.nls</groupId>
		    <artifactId>nls-sdk-tts</artifactId>
		    <version>2.2.1</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun.openservices</groupId>
			<artifactId>ons-client</artifactId>
			<version>1.9.1.Final</version>
		</dependency>
<!--        <dependency>
            <groupId>com.sungoin.sungoincomm</groupId>
            <artifactId>sungoin-comm</artifactId>
            <version>1.2.13</version>
        </dependency>-->
	</dependencies>
    <repositories>
		<repository>
			<id>sungoin-release</id>
			<name>inner nexus server</name>
			<url>http://nexus.sungoin.com/repository/maven-public/</url>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>sungoin-releases</id>
			<name>inner nexus plugin server</name>
			<url>http://nexus.sungoin.com/repository/maven-public/</url>
		</pluginRepository>
	</pluginRepositories>
	<!-- Package as an executable jar -->
	
	<build>
		<plugins>
<!--			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>appassembler-maven-plugin</artifactId>
				<version>1.10</version>
				<executions>
					<execution>
						<id>make-assembly</id>
						<phase>package</phase>
						<goals>
							<goal>assemble</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<configurationDirectory>conf</configurationDirectory>
					<configurationSourceDirectory>src/main/resources</configurationSourceDirectory>
					<copyConfigurationDirectory>true</copyConfigurationDirectory>
					<includeConfigurationDirectoryInClasspath>true</includeConfigurationDirectoryInClasspath>
					<assembleDirectory>${project.build.directory}/NETPHONE-LOCAL-RELEASE</assembleDirectory>
					<binFileExtensions>
						<unix>.sh</unix>
					</binFileExtensions>
					<platforms>
						<platform>windows</platform>
						<platform>unix</platform>
					</platforms>
					<repositoryName>lib</repositoryName>
					<lineLength>1024</lineLength>
					<programs>
						<program>
							<mainClass>com.sungoin.netphoneLocal.boot.Application</mainClass>
							<id>startup</id>
						</program>
					</programs>
					<repositoryLayout>flat</repositoryLayout>
					<useWildcardClassPath>true</useWildcardClassPath>
				</configuration>
			</plugin>-->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>appassembler-maven-plugin</artifactId>
				<version>1.10</version>
				<configuration>
					<repositoryLayout>flat</repositoryLayout>
					<repositoryName>lib</repositoryName>
					<includeConfigurationDirectoryInClasspath>true</includeConfigurationDirectoryInClasspath>
					<copyConfigurationDirectory>src/main/resources</copyConfigurationDirectory>
					<target>${project.build.directory}</target>
					<daemons>
						<daemon>
							<id>netphoneLocal</id>
							<mainClass>com.sungoin.netphoneLocal.boot.Application</mainClass>
							<commandLineArguments>
								<commandLineArgument>start</commandLineArgument>
							</commandLineArguments>
							<platforms>
								<platform>jsw</platform>
							</platforms>
							<generatorConfigurations>
								<generatorConfiguration>
									<generator>jsw</generator>
									<includes>
										<include>linux-x86-32</include>
										<include>windows-x86-64</include>
									</includes>
									<configuration>
										<property>
											<name>configuration.directory.in.classpath.first</name>
											<value>etc</value>
										</property>
										<property>
											<name>set.default.REPO_DIR</name>
											<value>lib</value>
										</property>
										<property>
											<name>wrapper.logfile</name>
											<value>../logs/wrapper.log</value>
										</property>
										<property>
											<name>run.as.user.envvar</name>
											<value>root</value>
										</property>
									</configuration>
								</generatorConfiguration>
							</generatorConfigurations>
							<jvmSettings>
								<initialMemorySize>512M</initialMemorySize>
								<maxMemorySize>1024M</maxMemorySize>
								<systemProperties>  
                                    <systemProperty>java.rmi.server.hostname=************</systemProperty>  
                                    <systemProperty>com.sun.management.jmxremote</systemProperty>  
                                    <systemProperty>com.sun.management.jmxremote.port=9999</systemProperty>  
                                    <systemProperty>com.sun.management.jmxremote.authenticate=false</systemProperty>  
                                    <systemProperty>com.sun.management.jmxremote.ssl=false</systemProperty>
                                </systemProperties>  
								<extraArguments>
									<extraArgument>-server</extraArgument>
									<extraArgument>-XX:+UseG1GC</extraArgument>
								</extraArguments>
							</jvmSettings>
						</daemon>
					</daemons>
				</configuration>
				<executions>
					<execution>
						<id>generate-jsw-scripts</id>
						<phase>package</phase>
						<goals>
							<goal>generate-daemons</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>